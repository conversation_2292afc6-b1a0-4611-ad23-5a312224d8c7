<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>VSCode个人常用插件合集 | 你真是一个美好的人类</title><meta name="keywords" content="工具,VSCode"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="我的常用VSCode插件，提升编码幸福感！">
<meta property="og:type" content="article">
<meta property="og:title" content="VSCode个人常用插件合集">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/fccd987.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="我的常用VSCode插件，提升编码幸福感！">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830205520.png">
<meta property="article:published_time" content="2020-08-30T13:11:02.000Z">
<meta property="article:modified_time" content="2020-08-30T13:11:02.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="工具">
<meta property="article:tag" content="VSCode">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830205520.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/fccd987"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'VSCode个人常用插件合集',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-08-30 13:11:02'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">VSCode个人常用插件合集</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-08-30T13:11:02.000Z" title="发表于 2020-08-30 13:11:02">2020-08-30</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-08-30T13:11:02.000Z" title="更新于 2020-08-30 13:11:02">2020-08-30</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/">前端</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/%E5%B7%A5%E5%85%B7/">工具</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><h3 id="自动闭合-HTML-标签"><a href="#自动闭合-HTML-标签" class="headerlink" title="自动闭合 HTML 标签"></a>自动闭合 HTML 标签</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830171002.png" alt="image-20200830170541187"></p>
<p>最基础的插件之一，用途就像它的名字一样，妈妈再也不用担心我因为缺少一个关闭标记，而花大量时间查找错误啦！</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830212542.gif" alt="st3"></p>
<h3 id="自动重命名-HTML-标签"><a href="#自动重命名-HTML-标签" class="headerlink" title="自动重命名 HTML 标签"></a>自动重命名 HTML 标签</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830170205.png"></p>
<p>自动重命名你的 HTML 标签，当你更改某一个标签的时候，对应的闭合或开始标签也会自动重命名！</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830213146.gif" alt="2"></p>
<h3 id="成对括号匹配"><a href="#成对括号匹配" class="headerlink" title="成对括号匹配"></a>成对括号匹配</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830170954.png" alt="image-20200830170952310"></p>
<p>对于大多数人来说，处理括号都是非常头痛的一件事情，特别是在较多嵌套的时候，更是让人焦头烂额，这款插件可以最大程度的缓解你的头痛症状，它会让成对的括号显示同一个颜色，让你快速的找到开始和结束。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830213219.png" alt="example"></p>
<h3 id="ESLint-TSLint"><a href="#ESLint-TSLint" class="headerlink" title="ESLint | TSLint"></a>ESLint | TSLint</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830174448.png" alt="image-20200830174058091"></p>
<p>这个扩展的主要功能就是格式化你的代码，以便在整个团队中保持一致的格式，如果你的代码不符合规范，那你将会收到大量的错误提示。长期使用这个插件，有利于帮助你养成良好的代码风格！</p>
<h3 id="代码拼写检查器"><a href="#代码拼写检查器" class="headerlink" title="代码拼写检查器"></a>代码拼写检查器</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830174454.png" alt="image-20200830173400052"></p>
<p>作为一个英语渣，这个代码拼写检查器可以保持免受打字错误而带来的 bug，data/date 分不清？每个人都不是完美的，我们可能有时候无意识的拼写错误，这款插件可以快速提示你！</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830213447.gif" alt="suggestions"></p>
<h3 id="同步-VSCode-设置"><a href="#同步-VSCode-设置" class="headerlink" title="同步 VSCode 设置"></a>同步 VSCode 设置</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830180546.png" alt="image-20200830180546035"></p>
<p>可以在多台机器上同步你的 VSCode 的设置，随时随地，都可以把你最习惯的设置同步到另一台电脑上！</p>
<h3 id="REST-Client"><a href="#REST-Client" class="headerlink" title="REST Client"></a>REST Client</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830180607.png" alt="image-20200830180606569"></p>
<p>REST Client 是一个 VS Code 的 REST 客户端扩展，它允许您直接发送 HTTP 请求并在 Visual Studio Code 中查看响应，你可以使用这个选项，而不必在浏览器或 Postman 那里来回地切换请求</p>
<h3 id="Prettier-代码格式化工具"><a href="#Prettier-代码格式化工具" class="headerlink" title="Prettier 代码格式化工具"></a>Prettier 代码格式化工具</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830180648.png" alt="image-20200830180647826"></p>
<p>前面的 ESLint，它可以帮助您自动格式化一致的代码，并显示一些警告和错误。</p>
<p>作为一个 React / Native 开发人员，保持我的代码干净和适当对齐是必须的ーー这是不可协商的。为了更好地阅读代码，将其适当缩进和分隔是一个优先考虑的问题，尤其是在处理长时间编写的代码时ーー样式、函数和处理程序的分隔非常关键，不仅对你而言如此，对你的同事而言也是如此。</p>
<p>只需要点击右键，格式化文档，即可快速格式化你的代码！</p>
<h3 id="Beautify-代码格式化工具"><a href="#Beautify-代码格式化工具" class="headerlink" title="Beautify 代码格式化工具"></a>Beautify 代码格式化工具</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830174507.png" alt="image-20200830173029420"></p>
<p>Beautify 是另一个很好的代码格式化扩展，几乎和上面提到的 Prettier 一样。它可以对用 Javascript、 JSON、 Sass、 CSS 和 HTML 编写的代码进行格式化。</p>
<h3 id="Material-Icon-Theme"><a href="#Material-Icon-Theme" class="headerlink" title="Material Icon Theme"></a>Material Icon Theme</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175734.png" alt="image-20200830175733841"></p>
<p>这个扩展为你的文件列表提供了漂亮可爱的图标。让你的编辑器更加个性化！</p>
<h3 id="路径自动补全"><a href="#路径自动补全" class="headerlink" title="路径自动补全"></a>路径自动补全</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175938.png" alt="image-20200830175937963"></p>
<p>在你使用路径的时候，会自动补全，在一个拥有大量组件，扩展，包的时候，将会节省你大量的时间，让你快速选择到你的文件路径。</p>
<h3 id="MInify"><a href="#MInify" class="headerlink" title="MInify"></a>MInify</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175755.png" alt="image-20200830175754828"></p>
<p>这是一款用于压缩合并 JavaScript 和 CSS 文件的应用程序。它提供了大量自定义的设置，以及自动压缩保存并导出为.min 文件的选项。它能够分别通过 uglify-js、clean-css 和 html-minifier，与 JavaScript、CSS 和 HTML 协同工作。</p>
<p>使用<strong>F1</strong> 运行文件缩小器<code>Minify</code></p>
<h3 id="Mithril-Emmet"><a href="#Mithril-Emmet" class="headerlink" title="Mithril Emmet"></a>Mithril Emmet</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175822.png" alt="image-20200830175821670"></p>
<p>Emmet (前身为 Zen Coding) 是一个能大幅度提高前端开发效率的一个工具. 在前端开发的过程中，一大部分的工作是写 HTML、CSS 代码。特别是手动编写 HTML 代码的时候，效率会特别低下，因为需要敲打很多尖括号，而且很多标签都需要闭合标签等。于是，就有了 Emmet，它可以极大的提高代码编写的效率，它提供了一种非常简练的语法规则，然后立刻生成对应的 HTML 结构或者 CSS 代码，同时还有多种实用的功能帮助进行前端开发。<br>VsCode 内置了 Emmet 语法,在后缀为.html/.css 中输入缩写后按 Tab 键即会自动生成相应代码。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830213722.gif" alt="screenshot"></p>
<h3 id="泼辣截图"><a href="#泼辣截图" class="headerlink" title="泼辣截图"></a>泼辣截图</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175958.png" alt="image-20200830175956601"></p>
<p>vs code 的一款代码截图美化插件，能够轻松创造出漂亮的代码截图。F1 输入 pola 即可使用！</p>
<h3 id="快速-log-Turbo-Console-log"><a href="#快速-log-Turbo-Console-log" class="headerlink" title="快速 log - Turbo Console log"></a>快速 log - Turbo Console log</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830180341.png" alt="image-20200830180336509"></p>
<p>是否每次调试程序的时候，手动写 console.log 让你感觉厌倦？来来来，这里有一款 vscode 插件适合你，他就是：Turbo Console Log。</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line">`ctrl + alt + l` 选中变量之后，使用这个快捷键生成 console.log</span><br><span class="line">`alt + shift + c` 注释所有 console.log</span><br><span class="line">`alt + shift + u` 启用所有 console.log</span><br><span class="line">`alt + shift + d` 删除所有 console.log</span><br></pre></td></tr></table></figure>

<h3 id="TRailing-Spaces-高亮多余的空格"><a href="#TRailing-Spaces-高亮多余的空格" class="headerlink" title="TRailing Spaces 高亮多余的空格"></a>TRailing Spaces 高亮多余的空格</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830180405.png" alt="image-20200830180404680"></p>
<p>高亮显示多余的空格</p>
<h3 id="TODO-Highlight-TODO-高亮"><a href="#TODO-Highlight-TODO-高亮" class="headerlink" title="TODO Highlight TODO 高亮"></a>TODO Highlight TODO 高亮</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830180429.png" alt="image-20200830180428796"></p>
<p>写代码过程中，突然发现一个 Bug，但是又不想停下来手中的活，以免打断思路，怎么办？按代码编写会规范，都是建议在代码中加个 TODO 注释。比如这样：</p>
<figure class="highlight cpp"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//<span class="doctag">TODO:</span>这里有个bug，但是空间太小了，我的算法写不下。</span></span><br><span class="line"><span class="comment">// FIX：</span></span><br><span class="line"><span class="comment">// TAG：</span></span><br></pre></td></tr></table></figure>

<p>或者</p>
<figure class="highlight cpp"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//<span class="doctag">FIXME:</span>哪个混蛋写的垃圾代码，会崩溃的。</span></span><br></pre></td></tr></table></figure>

<p>这些 TODO 标签散布在项目的每一个角落，用着不清不楚的词汇描绘着 Bug，功能缺失，性能问题等等。时间久了，很难找到具体在哪一个文件的哪一行。现代的 IDE 都有办法来收集这些 TODO 信息，让它们集中显示出来，以便检查待处理的问题所在。VS Code 也不例外，如果在应用扩展商店里搜索下 TODO，可以找出不少类似功能的插件。这个插件会分析代码中的 TODO 标签，并加亮之后，可以集中显示。这样很符合开发过程中，随手记录偶尔迸发的思维火花的情况。</p>
<h3 id="TabNine-代码补全"><a href="#TabNine-代码补全" class="headerlink" title="TabNine 代码补全"></a>TabNine 代码补全</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830180448.png" alt="image-20200830180447646"></p>
<p>TabNine 是一种基于 OpenAI 的语言模型（GPT-2）来实现的智能代码补全技术。它支持 23 种编程语言、5 种编辑器，使用简单，效果惊艳。不少使用过的网友说：TabNine 是他们用过的最好的代码补全工具，这是属于程序员的杀手级应用。</p>
<h3 id="sytlelint-样式代码检测"><a href="#sytlelint-样式代码检测" class="headerlink" title="sytlelint 样式代码检测"></a>sytlelint 样式代码检测</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830180513.png" alt="image-20200830180511998"></p>
<p>CSS / SCSS / Less 语法检查</p>
<h3 id="npm-检测你的-Package-json"><a href="#npm-检测你的-Package-json" class="headerlink" title="npm 检测你的 Package.json"></a>npm 检测你的 Package.json</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175844.png" alt="image-20200830175843774"></p>
<p>每个现代开发人员都已经知道 NPM 是什么以及为什么它很重要。Node Package Manager 是一个扩展，可以帮助您管理 Package.json 文件。如果有依赖项需要但尚未安装，它会给出警告，还有 NPM 包的版本控制。</p>
<h3 id="CSScomb"><a href="#CSScomb" class="headerlink" title="CSScomb"></a>CSScomb</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830213846.png" alt="image-20200830213845485"></p>
<p>看名字应该可以联想到它的功能了吧？没错，正如其名，一把梳理 CSS 属性顺序的 “梳子”。CSS 属性书写顺序非常重要，一个合格的前端 er 一定会有他遵循的 CSS 书写顺序规则。至于 CSS 属性书写顺序，这里我推荐腾讯 AollyTeam 团队的规范：<a target="_blank" rel="noopener external nofollow noreferrer" href="http://alloyteam.github.io/CodeGuide/#css-declaration-order">http://alloyteam.github.io/CodeGuide/#css-declaration-order</a></p>
<p>在项目的根目录下创建一个名为：<code>.csscomb.json</code> / <code>csscomb.json</code> / <code>.csscomb.js</code> / <code>csscomb.js</code> 的文件，然后添加一些配置项。也可以将配置项写入项目的 <code>package.json</code> 文件中的 <code>csscombConfig</code> 字段。</p>
<p>至于添加的配置项，CSScomb 提供了示例配置文件：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/csscomb/csscomb.js/blob/master/config/csscomb.json">https://github.com/csscomb/csscomb.js/blob/master/config/csscomb.json</a></p>
<p>这个配置文件里面各个字段的作用可以戳这里查看：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/csscomb/csscomb.js/blob/master/doc/options.md">https://github.com/csscomb/csscomb.js/blob/master/doc/options.md</a></p>
<h3 id="open-in-browser"><a href="#open-in-browser" class="headerlink" title="open in browser"></a>open in browser</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175916.png" alt="image-20200830175915759"></p>
<p>安装完这个插件就可以在编辑器菜单右键 html，在默认浏览器打开了</p>
<h3 id="CSS-Peek"><a href="#CSS-Peek" class="headerlink" title="CSS Peek"></a>CSS Peek</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830174518.png" alt="image-20200830173630990"></p>
<p>使用此插件，你可以追踪至样式表中 CSS 类和 ids 定义的地方。当你在 HTML 文件中右键单击选择器时，选择“ Go to Definition 和 Peek definition ”选项，它便会给你发送样式设置的 CSS 代码。</p>
<h3 id="CSS-auto-prefix"><a href="#CSS-auto-prefix" class="headerlink" title="CSS-auto-prefix"></a>CSS-auto-prefix</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830174526.png" alt="image-20200830173747185"></p>
<p>自动添加 CSS 私有前缀。</p>
<h3 id="编辑器内置浏览器"><a href="#编辑器内置浏览器" class="headerlink" title="编辑器内置浏览器"></a>编辑器内置浏览器</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830174542.png" alt="image-20200830173107773"></p>
<p>下载这个浏览器预览插件，这样你就可以在你的 VSCode 中完成所有的工作，而不是打开另一个窗口让你的 Chrome 浏览器看到你在代码中所做的改变。这会显示你的代码的浏览器预览，所以不必再切换到你的浏览器去查看哪怕是很小的变化。它帮助你节省时间和空间。</p>
<h3 id="Chrome-调试器"><a href="#Chrome-调试器" class="headerlink" title="Chrome 调试器"></a>Chrome 调试器</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175043.png" alt="image-20200830173808146"></p>
<p>作为一个前端 web 和移动开发者，Debugger for Chrome 帮助了我很多。特别是对于 JavaScript 开发人员来说，这样可以节省大量的时间来做一些小的改变。它对调试非常有帮助ーー它可以帮助你很快地解决和捕捉错误。</p>
<p>从发现错误所在的行和函数开始，直到看到其数据处理，该控制台都很强大。</p>
<h3 id="change-case-改变你的命名格式"><a href="#change-case-改变你的命名格式" class="headerlink" title="change-case 改变你的命名格式"></a>change-case 改变你的命名格式</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175051.png" alt="image-20200830173219927"></p>
<p>使用方法：F1 》Change case ， 然后就会有提示了！</p>
<h3 id="Javascript-ES6-代码片段"><a href="#Javascript-ES6-代码片段" class="headerlink" title="Javascript (ES6)代码片段"></a>Javascript (ES6)代码片段</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175437.png" alt="image-20200830175436555"></p>
<p>这个方便的代码片段是一个轻量级的库扩展，它可以绑定任何标准的 JavaScript 调用，因此只需键入快捷代码，就可以看到自动打印到编辑器的整个通用代码。这个扩展不仅支持 Javascript ES6，还支持 Typescript、 Reactjs、 Vue 和 HTML。</p>
<h3 id="Live-Server"><a href="#Live-Server" class="headerlink" title="Live Server"></a>Live Server</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175706.png" alt="image-20200830175705384"></p>
<p>你安装它之后，他会出现在你编辑器右下角。</p>
<p>在使用 Live Server 时，这个 VSCode 扩展将帮助您打开当前项目的活动 Web 服务器。通常，当使用像 Webpack 这样的构建器时，它通常会完成这项工作，但是这个扩展已经证明自己更加有用。您只需右键单击并运行打开与活动服务器，它会做其它的。</p>
<h3 id="Quokka"><a href="#Quokka" class="headerlink" title="Quokka"></a>Quokka</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830180622.png" alt="image-20200830180621882"></p>
<p>对于 JavaScript 开发者来说，Quokka 被称为现代的暂存器。它的构建是为了帮助每个开发人员进行代码检查。这是一个完美的解决方案。</p>
<p>与其他 VSCode 扩展相比，它非常轻量级、高效和强大。它将提高你的工作流程，它是实时的，并将立即给你反馈。</p>
<p>它所做的是为每个结果使用固定的颜色类型，这样开发人员就可以轻松地理解流的执行。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830214054.gif" alt="main-video"></p>
<h3 id="Codeif-变量命名神器"><a href="#Codeif-变量命名神器" class="headerlink" title="Codeif 变量命名神器"></a>Codeif 变量命名神器</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175057.png" alt="image-20200830173446320"></p>
<p><code>CodeIf</code> 是一个用来给变量命名的网站，你只要输入你想起的中文名，它就会给你提供很多建议的命名：</p>
<h3 id="Color-Picker-颜色选择器"><a href="#Color-Picker-颜色选择器" class="headerlink" title="Color Picker 颜色选择器"></a>Color Picker 颜色选择器</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175101.png" alt="image-20200830173553670"></p>
<p>颜色选择器扩展可以帮助您轻松地在 CSS 文件中选择颜色。它将立即反映或应用于您当前正在处理的属性。如果你喜欢使用 RGBA 颜色，它也是可用的。</p>
<h3 id="快速选择-emoji-表情"><a href="#快速选择-emoji-表情" class="headerlink" title="快速选择 emoji 表情"></a>快速选择 emoji 表情</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830171012.png" alt="image-20200830170741963"></p>
<p>在你的 VSCode 中快速添加 emoji 表情，使用方法：</p>
<ul>
<li>Mac: <code>cmd + i</code></li>
<li>Linux: <code>ctrl + alt + i</code></li>
<li>Windows: <code>ctrl + i</code></li>
</ul>
<h3 id="Dracula-Official"><a href="#Dracula-Official" class="headerlink" title="Dracula Official"></a>Dracula Official</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175107.png" alt="image-20200830173846652"></p>
<p>一款好看的 VSCode 主题！</p>
<h3 id="ES7-React-Redux-React-Native-JS-snippets"><a href="#ES7-React-Redux-React-Native-JS-snippets" class="headerlink" title="ES7 React/Redux/React-Native/JS snippets"></a>ES7 React/Redux/React-Native/JS snippets</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175112.png" alt="image-20200830173956004"></p>
<p>这是为那些正在使用诸如 React 这样的 JavaScript 框架的高级开发人员准备的，同时还有其他与其产品和复杂应用程序兼容的技术。</p>
<p>一遍又一遍地输入标准代码是低效的。有了这个代码片段的帮助，你可以通过输入快捷代码轻松创建基于类的组件、函数组件、导入、生命周期方法等等，这是我使用 Reactjs 和 React Native 以来最常用的扩展之一。</p>
<h3 id="filesize"><a href="#filesize" class="headerlink" title="filesize"></a>filesize</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175114.png" alt="image-20200830174123382"></p>
<p>在底部状态栏显示当前文件大小，点击后还可以看到详细创建、修改时间</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830200115.png" alt="image-20200830200105066"></p>
<h3 id="GitHub"><a href="#GitHub" class="headerlink" title="GitHub"></a>GitHub</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175117.png" alt="image-20200830174142962"></p>
<p>如果你的团队正在使用 Github，或者你正在使用 Github 作为你的项目存储库，或者你想使用其他开发者的源代码存储库，那么这个扩展就是为你准备的。</p>
<p>顺便说一下，Github 现在由微软拥有并管理，为那些不知道的人服务，所以 Github 和 VSCode 现在是微软产品的一部分。</p>
<p>通过使用 Github Extension，您现在可以轻松地连接其他开发人员的存储库、您喜欢的开发人员的存储库，甚至您自己的存储库。如果你经常使用 Github 的话，你可以很容易的做到这一点。</p>
<h3 id="Gitlens-GIt-supercharged"><a href="#Gitlens-GIt-supercharged" class="headerlink" title="Gitlens - GIt supercharged"></a>Gitlens - GIt supercharged</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175120.png" alt="image-20200830174211542"></p>
<p>Gitlens 是另一个伟大的扩展。它增加了当前的 VSCode Git 功能，能够从以前的提交和更改中并行执行代码比较，还有其他很酷的功能。</p>
<p><strong>详细的 Git 提交日志</strong>。<br>Git 重度使用者必备，尤其是多人协作时：哪一行代码，何时、何人提交都有记录。</p>
<p><strong>妈妈再也不用担心我背锅了！</strong></p>
<h3 id="Highlight-Matching-Tag-标签成对高亮"><a href="#Highlight-Matching-Tag-标签成对高亮" class="headerlink" title="Highlight Matching Tag 标签成对高亮"></a>Highlight Matching Tag 标签成对高亮</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175123.png" alt="image-20200830174220950"></p>
<p>让成对的标签标签高亮！</p>
<h3 id="HTML-Boilerplate"><a href="#HTML-Boilerplate" class="headerlink" title="HTML Boilerplate"></a>HTML Boilerplate</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175126.png" alt="image-20200830174320258"></p>
<p>通过使用 HTML 模版插件，你就摆脱了为 HTML 新文件重新编写头部和正文标签的苦恼。你只需在空文件中输入 html，并按 Tab 键，即可生成干净的文档结构。</p>
<h3 id="indent-raibow"><a href="#indent-raibow" class="headerlink" title="indent raibow"></a>indent raibow</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175129.png" alt="image-20200830174352775"></p>
<p>彩虹缩进，让你的缩进更加直观好看！</p>
<h3 id="Vetur"><a href="#Vetur" class="headerlink" title="Vetur"></a>Vetur</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175133.png" alt="image-20200830174421539"></p>
<p>VUE 推荐插件！包含语法高亮，代码提示，代码格式化等！</p>
<h3 id="Markdown-Preview-Enhanced-格式化工具"><a href="#Markdown-Preview-Enhanced-格式化工具" class="headerlink" title="Markdown Preview Enhanced 格式化工具"></a>Markdown Preview Enhanced 格式化工具</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175314.png" alt="image-20200830175313755"></p>
<p>格式化你的 markdown 文档</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830214310.png" alt="28495106-30b3b15e-6f09-11e7-8eb6-ca4ca001ab15"></p>
<h3 id="头部注释生成"><a href="#头部注释生成" class="headerlink" title="头部注释生成"></a>头部注释生成</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175456.png" alt="image-20200830175454928"></p>
<p><strong>文件头部添加注释</strong>:</p>
<ul>
<li>在文件开头添加注释，记录文件信息/文件的传参/出参等</li>
<li>支持用户高度自定义注释选项, 适配各种需求和注释。</li>
<li>保存文件的时候，自动更新最后的编辑时间和编辑人</li>
<li>快捷键：<code>window</code>：<code>ctrl+alt+i</code>,<code>mac</code>：<code>ctrl+cmd+i</code>, <code>linux</code>: <code>ctrl+meta+i</code></li>
</ul>
<h3 id="language-Stylus"><a href="#language-Stylus" class="headerlink" title="language-Stylus"></a>language-Stylus</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://gitee.com/constown/picgo/raw/master/img/20200830175650.png" alt="image-20200830175648797"></p>
<p>添加 stylus 语言的支持！</p>
<h3 id="Git-History"><a href="#Git-History" class="headerlink" title="Git History"></a>Git History</h3><p>Git History 可以帮你迅速查看 Git 历史记录，图形化的界面，使 Git 历史一目了然！</p>
<h3 id="Git-Graph"><a href="#Git-Graph" class="headerlink" title="Git Graph"></a>Git Graph</h3><p>更好的帮助你分析分支之间的关系！</p>
<h3 id="Git-emoji"><a href="#Git-emoji" class="headerlink" title="Git emoji"></a>Git emoji</h3><p>让你的提交更加可爱！</p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/fccd987.html">http://blog.mhy.loc.cc/archives/fccd987.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/%E5%B7%A5%E5%85%B7/">工具</a><a class="post-meta__tags" href="/tags/VSCode/">VSCode</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830205520.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/155e47b.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830221642.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">使用 svg-captcha 插件在 ThinkJS 中实现随机验证码功能</div></div></a></div><div class="next-post pull-right"><a href="/archives/cbcd1946.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200723212429.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">解决jsdelivr缓存问题的几个办法</div></div></a></div></nav></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%87%AA%E5%8A%A8%E9%97%AD%E5%90%88-HTML-%E6%A0%87%E7%AD%BE"><span class="toc-text">自动闭合 HTML 标签</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%87%AA%E5%8A%A8%E9%87%8D%E5%91%BD%E5%90%8D-HTML-%E6%A0%87%E7%AD%BE"><span class="toc-text">自动重命名 HTML 标签</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%88%90%E5%AF%B9%E6%8B%AC%E5%8F%B7%E5%8C%B9%E9%85%8D"><span class="toc-text">成对括号匹配</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#ESLint-TSLint"><span class="toc-text">ESLint | TSLint</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E6%8B%BC%E5%86%99%E6%A3%80%E6%9F%A5%E5%99%A8"><span class="toc-text">代码拼写检查器</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%90%8C%E6%AD%A5-VSCode-%E8%AE%BE%E7%BD%AE"><span class="toc-text">同步 VSCode 设置</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#REST-Client"><span class="toc-text">REST Client</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Prettier-%E4%BB%A3%E7%A0%81%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%B7%A5%E5%85%B7"><span class="toc-text">Prettier 代码格式化工具</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Beautify-%E4%BB%A3%E7%A0%81%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%B7%A5%E5%85%B7"><span class="toc-text">Beautify 代码格式化工具</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Material-Icon-Theme"><span class="toc-text">Material Icon Theme</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%B7%AF%E5%BE%84%E8%87%AA%E5%8A%A8%E8%A1%A5%E5%85%A8"><span class="toc-text">路径自动补全</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#MInify"><span class="toc-text">MInify</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Mithril-Emmet"><span class="toc-text">Mithril Emmet</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%B3%BC%E8%BE%A3%E6%88%AA%E5%9B%BE"><span class="toc-text">泼辣截图</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%BF%AB%E9%80%9F-log-Turbo-Console-log"><span class="toc-text">快速 log - Turbo Console log</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#TRailing-Spaces-%E9%AB%98%E4%BA%AE%E5%A4%9A%E4%BD%99%E7%9A%84%E7%A9%BA%E6%A0%BC"><span class="toc-text">TRailing Spaces 高亮多余的空格</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#TODO-Highlight-TODO-%E9%AB%98%E4%BA%AE"><span class="toc-text">TODO Highlight TODO 高亮</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#TabNine-%E4%BB%A3%E7%A0%81%E8%A1%A5%E5%85%A8"><span class="toc-text">TabNine 代码补全</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#sytlelint-%E6%A0%B7%E5%BC%8F%E4%BB%A3%E7%A0%81%E6%A3%80%E6%B5%8B"><span class="toc-text">sytlelint 样式代码检测</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#npm-%E6%A3%80%E6%B5%8B%E4%BD%A0%E7%9A%84-Package-json"><span class="toc-text">npm 检测你的 Package.json</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#CSScomb"><span class="toc-text">CSScomb</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#open-in-browser"><span class="toc-text">open in browser</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#CSS-Peek"><span class="toc-text">CSS Peek</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#CSS-auto-prefix"><span class="toc-text">CSS-auto-prefix</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%BC%96%E8%BE%91%E5%99%A8%E5%86%85%E7%BD%AE%E6%B5%8F%E8%A7%88%E5%99%A8"><span class="toc-text">编辑器内置浏览器</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Chrome-%E8%B0%83%E8%AF%95%E5%99%A8"><span class="toc-text">Chrome 调试器</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#change-case-%E6%94%B9%E5%8F%98%E4%BD%A0%E7%9A%84%E5%91%BD%E5%90%8D%E6%A0%BC%E5%BC%8F"><span class="toc-text">change-case 改变你的命名格式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Javascript-ES6-%E4%BB%A3%E7%A0%81%E7%89%87%E6%AE%B5"><span class="toc-text">Javascript (ES6)代码片段</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Live-Server"><span class="toc-text">Live Server</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Quokka"><span class="toc-text">Quokka</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Codeif-%E5%8F%98%E9%87%8F%E5%91%BD%E5%90%8D%E7%A5%9E%E5%99%A8"><span class="toc-text">Codeif 变量命名神器</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Color-Picker-%E9%A2%9C%E8%89%B2%E9%80%89%E6%8B%A9%E5%99%A8"><span class="toc-text">Color Picker 颜色选择器</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%BF%AB%E9%80%9F%E9%80%89%E6%8B%A9-emoji-%E8%A1%A8%E6%83%85"><span class="toc-text">快速选择 emoji 表情</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Dracula-Official"><span class="toc-text">Dracula Official</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#ES7-React-Redux-React-Native-JS-snippets"><span class="toc-text">ES7 React&#x2F;Redux&#x2F;React-Native&#x2F;JS snippets</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#filesize"><span class="toc-text">filesize</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#GitHub"><span class="toc-text">GitHub</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Gitlens-GIt-supercharged"><span class="toc-text">Gitlens - GIt supercharged</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Highlight-Matching-Tag-%E6%A0%87%E7%AD%BE%E6%88%90%E5%AF%B9%E9%AB%98%E4%BA%AE"><span class="toc-text">Highlight Matching Tag 标签成对高亮</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#HTML-Boilerplate"><span class="toc-text">HTML Boilerplate</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#indent-raibow"><span class="toc-text">indent raibow</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Vetur"><span class="toc-text">Vetur</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Markdown-Preview-Enhanced-%E6%A0%BC%E5%BC%8F%E5%8C%96%E5%B7%A5%E5%85%B7"><span class="toc-text">Markdown Preview Enhanced 格式化工具</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%A4%B4%E9%83%A8%E6%B3%A8%E9%87%8A%E7%94%9F%E6%88%90"><span class="toc-text">头部注释生成</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#language-Stylus"><span class="toc-text">language-Stylus</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Git-History"><span class="toc-text">Git History</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Git-Graph"><span class="toc-text">Git Graph</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Git-emoji"><span class="toc-text">Git emoji</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>