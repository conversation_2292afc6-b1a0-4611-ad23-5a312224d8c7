<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>MySQL数据库范式 | 你真是一个美好的人类</title><meta name="keywords" content="Node,MySQL,范式"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="按照教材中的定义，范式是“符合某一种级别的关系模式的集合，表示一个关系内部各属性之间的联系的合理化程度”。实际上你可以把它粗略地理解为一张数据表的表结构所符合的某种设计标准的级别。">
<meta property="og:type" content="article">
<meta property="og:title" content="MySQL数据库范式">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/56cec031.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="按照教材中的定义，范式是“符合某一种级别的关系模式的集合，表示一个关系内部各属性之间的联系的合理化程度”。实际上你可以把它粗略地理解为一张数据表的表结构所符合的某种设计标准的级别。">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185454.png">
<meta property="article:published_time" content="2019-07-18T13:30:00.000Z">
<meta property="article:modified_time" content="2019-07-18T13:30:00.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="Node">
<meta property="article:tag" content="MySQL">
<meta property="article:tag" content="范式">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185454.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/56cec031"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'MySQL数据库范式',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2019-07-18 13:30:00'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">MySQL数据库范式</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2019-07-18T13:30:00.000Z" title="发表于 2019-07-18 13:30:00">2019-07-18</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2019-07-18T13:30:00.000Z" title="更新于 2019-07-18 13:30:00">2019-07-18</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%95%B0%E6%8D%AE%E5%BA%93/">数据库</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E6%95%B0%E6%8D%AE%E5%BA%93/MySQL/">MySQL</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><h2 id="示例表数据"><a href="#示例表数据" class="headerlink" title="示例表数据"></a>示例表数据</h2><p>假设有一个名为<code>employee</code>的员工表，它有九个属性：<code>id</code>(员工编号)、<code>name</code>(员工名称)、<code>mobile</code>(电话)、<code>zip</code>(邮编)、<code>province</code>(省份)、<code>city</code>(城市)、<code>district</code>(区县)、<code>deptNo</code>(所属部门编号)、<code>deptName</code>(所属部门名称)、表总数据如下：</p>
<table>
<thead>
<tr>
<th>id</th>
<th>name</th>
<th>mobile</th>
<th>zip</th>
<th>province</th>
<th>city</th>
<th>district</th>
<th>deptNo</th>
<th>deptName</th>
</tr>
</thead>
<tbody><tr>
<td>101</td>
<td>张三</td>
<td>13910000001 13910000002</td>
<td>100001</td>
<td>北京</td>
<td>北京</td>
<td>海淀区</td>
<td>D1</td>
<td>部门 1</td>
</tr>
<tr>
<td>101</td>
<td>张三</td>
<td>13910000001 13910000002</td>
<td>100001</td>
<td>北京</td>
<td>北京</td>
<td>海淀区</td>
<td>D2</td>
<td>部门 2</td>
</tr>
<tr>
<td>102</td>
<td>李四</td>
<td>13910000003</td>
<td>200001</td>
<td>上海</td>
<td>上海</td>
<td>静安区</td>
<td>D3</td>
<td>部门 3</td>
</tr>
<tr>
<td>103</td>
<td>王五</td>
<td>13910000004</td>
<td>510001</td>
<td>广东省</td>
<td>广州</td>
<td>白云区</td>
<td>D4</td>
<td>部门 4</td>
</tr>
<tr>
<td>103</td>
<td>王五</td>
<td>13910000004</td>
<td>510001</td>
<td>广东省</td>
<td>广州</td>
<td>白云区</td>
<td>D5</td>
<td>部门 5</td>
</tr>
</tbody></table>
<p>由于此员工表是非规范化的，我们将面对如下的问题。</p>
<blockquote>
<ul>
<li><strong>修改异常</strong>：上表中张三有两条记录，因为他隶属于两个部门。如果我们要修改张三的地址，必修修改两行记录。假如一个部门得到了张三的新地址并进行了更新，而另一个部门没有，那么此时张三在表中会存在两个不同的地址，导致了数据不一致</li>
<li><strong>新增异常：</strong>假如一个新员工假如公司，他正处于入职培训阶段，还没有被正式分配到某个部门，如果<code>deptNo</code>字段不允许为空，我们就无法向<code>employee</code>表中新增该员工的数据。</li>
<li><strong>删除异常：</strong>假设公司撤销了 D3 部门，那么在删除<code>deptNo</code>为 D3 的行时，会将李四的信息也一并删除。因为他隶属于 D3 这一部门。</li>
</ul>
</blockquote>
<h2 id="第一范式-1NF"><a href="#第一范式-1NF" class="headerlink" title="第一范式(1NF)"></a>第一范式(1NF)</h2><blockquote>
<p><strong>表中的列只能含有原子性(不可再分)的值。</strong></p>
</blockquote>
<p>表中的张三有两个手机号存储在 mobile 列中，违反了 1NF 规则。为了使表满足 1NF，数据应该修改如下：</p>
<table>
<thead>
<tr>
<th>id</th>
<th>name</th>
<th>mobile</th>
<th>zip</th>
<th>province</th>
<th>city</th>
<th>district</th>
<th>deptNo</th>
<th>deptName</th>
</tr>
</thead>
<tbody><tr>
<td>101</td>
<td>张三</td>
<td>13910000001</td>
<td>100001</td>
<td>北京</td>
<td>北京</td>
<td>海淀区</td>
<td>D1</td>
<td>部门 1</td>
</tr>
<tr>
<td>101</td>
<td>张三</td>
<td>13910000002</td>
<td>100001</td>
<td>北京</td>
<td>北京</td>
<td>海淀区</td>
<td>D1</td>
<td>部门 1</td>
</tr>
<tr>
<td>101</td>
<td>张三</td>
<td>13910000001</td>
<td>100001</td>
<td>北京</td>
<td>北京</td>
<td>海淀区</td>
<td>D2</td>
<td>部门 2</td>
</tr>
<tr>
<td>101</td>
<td>张三</td>
<td>13910000002</td>
<td>100001</td>
<td>北京</td>
<td>北京</td>
<td>海淀区</td>
<td>D2</td>
<td>部门 2</td>
</tr>
<tr>
<td>102</td>
<td>李四</td>
<td>13910000003</td>
<td>200001</td>
<td>上海</td>
<td>上海</td>
<td>静安区</td>
<td>D3</td>
<td>部门 3</td>
</tr>
<tr>
<td>103</td>
<td>王五</td>
<td>13910000004</td>
<td>510001</td>
<td>广东省</td>
<td>广州</td>
<td>白云区</td>
<td>D4</td>
<td>部门 4</td>
</tr>
<tr>
<td>103</td>
<td>王五</td>
<td>13910000004</td>
<td>510001</td>
<td>广东省</td>
<td>广州</td>
<td>白云区</td>
<td>D5</td>
<td>部门 5</td>
</tr>
</tbody></table>
<h2 id="第二范式-2NF"><a href="#第二范式-2NF" class="headerlink" title="第二范式(2NF)"></a>第二范式(2NF)</h2><p>第二范式要同时满足下面两个条件</p>
<blockquote>
<ul>
<li><strong>满足第一范式</strong></li>
<li><strong>没有部分依赖</strong></li>
</ul>
</blockquote>
<p>例如，员工表的一个候选键是{id，mobile，deptNo}，而 deptName 依赖于 deptNo，同样 name 依赖于 id，因此不是 2NF 的。为了满足第二范式的条件，需要将这个表拆分成 employee、dept、employee_dept、employee_mobile 四个表。如下：</p>
<p><strong>员工表 employee</strong></p>
<table>
<thead>
<tr>
<th>id</th>
<th>name</th>
<th>zip</th>
<th>province</th>
<th>city</th>
<th>district</th>
</tr>
</thead>
<tbody><tr>
<td>101</td>
<td>张三</td>
<td>100001</td>
<td>北京</td>
<td>北京</td>
<td>海淀区</td>
</tr>
<tr>
<td>102</td>
<td>李四</td>
<td>200001</td>
<td>上海</td>
<td>上海</td>
<td>静安区</td>
</tr>
<tr>
<td>103</td>
<td>王五</td>
<td>510001</td>
<td>广东省</td>
<td>广州</td>
<td>白云区</td>
</tr>
</tbody></table>
<p><strong>部门表 dept</strong></p>
<table>
<thead>
<tr>
<th>deptNo</th>
<th>deptName</th>
</tr>
</thead>
<tbody><tr>
<td>D1</td>
<td>部门 1</td>
</tr>
<tr>
<td>D2</td>
<td>部门 2</td>
</tr>
<tr>
<td>D3</td>
<td>部门 3</td>
</tr>
<tr>
<td>D4</td>
<td>部门 4</td>
</tr>
<tr>
<td>D5</td>
<td>部门 5</td>
</tr>
</tbody></table>
<p><strong>员工部门关系表 employee_dept</strong></p>
<table>
<thead>
<tr>
<th>id</th>
<th>deptNo</th>
</tr>
</thead>
<tbody><tr>
<td>101</td>
<td>D1</td>
</tr>
<tr>
<td>101</td>
<td>D2</td>
</tr>
<tr>
<td>102</td>
<td>D3</td>
</tr>
<tr>
<td>103</td>
<td>D4</td>
</tr>
<tr>
<td>104</td>
<td>D5</td>
</tr>
</tbody></table>
<p><strong>员工电话表 employee_mobile</strong></p>
<table>
<thead>
<tr>
<th>id</th>
<th>mobile</th>
</tr>
</thead>
<tbody><tr>
<td>101</td>
<td>13910000001</td>
</tr>
<tr>
<td>101</td>
<td>13910000002</td>
</tr>
<tr>
<td>102</td>
<td>13910000003</td>
</tr>
<tr>
<td>103</td>
<td>13910000004</td>
</tr>
</tbody></table>
<h2 id="第三范式-3NF"><a href="#第三范式-3NF" class="headerlink" title="第三范式(3NF)"></a>第三范式(3NF)</h2><p>第三范式要同时满足下面两个条件</p>
<blockquote>
<ul>
<li><strong>满足第二范式</strong></li>
<li><strong>没有传递依赖</strong></li>
</ul>
</blockquote>
<p>例如，员工表的 province、city、district 依赖于 zip，而 zip 依赖于 id，换句话说，province、city、district 传递依赖于 id，违反了 3NF 规则。为了满足第三范式的条件，可以将这个表拆分成 employee 和 zip 两个表，如下</p>
<p><strong>employee</strong></p>
<table>
<thead>
<tr>
<th>id</th>
<th>name</th>
<th>zip</th>
</tr>
</thead>
<tbody><tr>
<td>101</td>
<td>张三</td>
<td>100001</td>
</tr>
<tr>
<td>102</td>
<td>李四</td>
<td>200001</td>
</tr>
<tr>
<td>103</td>
<td>王五</td>
<td>510001</td>
</tr>
</tbody></table>
<p><strong>地区表 area</strong></p>
<table>
<thead>
<tr>
<th>zip</th>
<th>province</th>
<th>city</th>
<th>district</th>
</tr>
</thead>
<tbody><tr>
<td>100001</td>
<td>北京</td>
<td>北京</td>
<td>海淀区</td>
</tr>
<tr>
<td>200001</td>
<td>上海</td>
<td>上海</td>
<td>静安区</td>
</tr>
<tr>
<td>51000</td>
<td>广东省</td>
<td>广州</td>
<td>白云区</td>
</tr>
</tbody></table>
<p>在关系数据库模型设计中，一般需要满足第三范式的要求。如果一个表具有良好的主外键设计，就应该是满足 3NF 的表。规范化带来的好处是通过减少数据冗余提高更新数据的效率，同时保证数据完整性。然而，我们在实际应用中也要防止过度规范化的问题。规范化程度越高，划分的表就越多，在查询数据时越有可能使用表连接操作。而如果连接的表过多，会影响查询性能。关键的问题是要依据业务需求，仔细权衡数据查询和数据更新关系，指定最合适的规范化程度。不要为了遵循严格的规范化规则而修改业务需求</p>
<h2 id="数据库一对一、一对多、多对多设计"><a href="#数据库一对一、一对多、多对多设计" class="headerlink" title="数据库一对一、一对多、多对多设计"></a>数据库一对一、一对多、多对多设计</h2><hr>
<p>数据库实体间有三种对应关系：一对一、一对多、多对多</p>
<p>一对一关系示例：</p>
<p>一个学生对应一个学生档案材料 每个人都有唯一的身份证号</p>
<p>一对多关系示例：</p>
<p>一个学生只属于一个班，但这个班有多名学生</p>
<p>多对多关系示例：</p>
<p>一个学生可以选择多门课，一门课也可以有多名学生</p>
<p>一个人可以有多个角色，一个角色可以有多个人</p>
<p>一、一对多关系处理</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200519190642.png" alt="image-20200519190634860"></p>
<p>设计数据库表：只需在 学生表 中多添加一个班级号的 ID 即可</p>
<p>二、多对多关系处理</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200519190703.png" alt="image-20200519190702170"></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200519190801.png" alt="image-20200519190800713"></p>
<h2 id="关系"><a href="#关系" class="headerlink" title="关系"></a>关系</h2><ul>
<li><p>创建成绩表 scores，结构如下</p>
<ul>
<li>id</li>
<li>学生</li>
<li>科目</li>
<li>成绩</li>
</ul>
</li>
<li><p>思考：学生列应该存什么信息呢？</p>
</li>
<li><p>答：学生列的数据不是在这里新建的，而应该从学生表引用过来，关系也是一条数据；根据范式要求应该存储学生的编号，而不是学生的姓名等其它信息</p>
</li>
<li><p>同理，科目表也是关系列，引用科目表中的数据</p>
</li>
<li><p>创建表的语句如下</p>
</li>
</ul>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">create table scores(</span><br><span class="line">id int primary key auto_increment,</span><br><span class="line">stuid int,</span><br><span class="line">subid int,</span><br><span class="line">score decimal(5,2)</span><br><span class="line">);</span><br></pre></td></tr></table></figure>

<h2 id="外键"><a href="#外键" class="headerlink" title="外键"></a>外键</h2><ul>
<li>思考：怎么保证关系列数据的有效性呢？任何整数都可以吗？</li>
<li>答：必须是学生表中 id 列存在的数据，可以通过外键约束进行数据的有效性验证</li>
<li>为 stuid 添加外键约束</li>
</ul>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">alter table scores add constraint stu_sco foreign key(stuid) references students(id);</span><br></pre></td></tr></table></figure>

<ul>
<li>此时插入或者修改数据时，如果 stuid 的值在 students 表中不存在则会报错</li>
<li>在创建表时可以直接创建约束</li>
</ul>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">create table scores(</span><br><span class="line">id int primary key auto_increment,</span><br><span class="line">stuid int,</span><br><span class="line">subid int,</span><br><span class="line">score decimal(5,2),</span><br><span class="line">foreign key(stuid) references students(id),</span><br><span class="line">foreign key(subid) references subjects(id)</span><br><span class="line">);</span><br></pre></td></tr></table></figure>

<h3 id="外键的级联操作"><a href="#外键的级联操作" class="headerlink" title="外键的级联操作"></a>外键的级联操作</h3><ul>
<li>在删除 students 表的数据时，如果这个 id 值在 scores 中已经存在，则会抛异常</li>
<li>推荐使用逻辑删除，还可以解决这个问题</li>
<li>可以创建表时指定级联操作，也可以在创建表后再修改外键的级联操作</li>
<li>语法</li>
</ul>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">alter table scores add constraint stu_sco foreign key(stuid) references students(id) on delete cascade;</span><br></pre></td></tr></table></figure>

<ul>
<li>级联操作的类型包括：<ul>
<li>restrict（限制）：默认值，抛异常</li>
<li>cascade（级联）：如果主表的记录删掉，则从表中相关联的记录都将被删除</li>
<li>set null：将外键设置为空</li>
<li>no action：什么都不做</li>
</ul>
</li>
</ul>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/56cec031.html">http://blog.mhy.loc.cc/archives/56cec031.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/Node/">Node</a><a class="post-meta__tags" href="/tags/MySQL/">MySQL</a><a class="post-meta__tags" href="/tags/%E8%8C%83%E5%BC%8F/">范式</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185454.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/738bac5d.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718133733.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">本地连接云服务器数据库</div></div></a></div><div class="next-post pull-right"><a href="/archives/25109d5c.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185454.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">MySQL数据库基本操作</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/1567847a.html" title="Node爬取数据到数据库练习"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-23</div><div class="title">Node爬取数据到数据库练习</div></div></a></div><div><a href="/archives/1212afb3.html" title="Node爬取数据到数据库实战代码笔记"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-20</div><div class="title">Node爬取数据到数据库实战代码笔记</div></div></a></div><div><a href="/archives/44b2b83c.html" title="Node学习总结笔记"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184621.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-13</div><div class="title">Node学习总结笔记</div></div></a></div><div><a href="/archives/628e35f6.html" title="Node连接MySQL数据库"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185454.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-08-30</div><div class="title">Node连接MySQL数据库</div></div></a></div><div><a href="/archives/9da3e761.html" title="MySQL子查询"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185454.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-08-25</div><div class="title">MySQL子查询</div></div></a></div><div><a href="/archives/cc98362f.html" title="MySQL视图"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185454.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-08-20</div><div class="title">MySQL视图</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%A4%BA%E4%BE%8B%E8%A1%A8%E6%95%B0%E6%8D%AE"><span class="toc-text">示例表数据</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E8%8C%83%E5%BC%8F-1NF"><span class="toc-text">第一范式(1NF)</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E8%8C%83%E5%BC%8F-2NF"><span class="toc-text">第二范式(2NF)</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E8%8C%83%E5%BC%8F-3NF"><span class="toc-text">第三范式(3NF)</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%95%B0%E6%8D%AE%E5%BA%93%E4%B8%80%E5%AF%B9%E4%B8%80%E3%80%81%E4%B8%80%E5%AF%B9%E5%A4%9A%E3%80%81%E5%A4%9A%E5%AF%B9%E5%A4%9A%E8%AE%BE%E8%AE%A1"><span class="toc-text">数据库一对一、一对多、多对多设计</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%85%B3%E7%B3%BB"><span class="toc-text">关系</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%A4%96%E9%94%AE"><span class="toc-text">外键</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%A4%96%E9%94%AE%E7%9A%84%E7%BA%A7%E8%81%94%E6%93%8D%E4%BD%9C"><span class="toc-text">外键的级联操作</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>