#loading-box
  .loading-left-bg
  .loading-right-bg
  .spinner-box
    .configure-border-1
      .configure-core
    .configure-border-2
      .configure-core
    .loading-word= _p('loading')

script.
  (()=>{
    const $loadingBox = document.getElementById('loading-box')
    const $body = document.body
    const preloader = {
      endLoading: () => {
        $body.style.overflow = ''
        $loadingBox.classList.add('loaded')
      },
      initLoading: () => {
        $body.style.overflow = 'hidden'
        $loadingBox.classList.remove('loaded')
      }
    }

    preloader.initLoading()
    window.addEventListener('load', preloader.endLoading)

    if (!{theme.pjax && theme.pjax.enable}) {
      btf.addGlobalFn('pjaxSend', preloader.initLoading, 'preloader_init')
      btf.addGlobalFn('pjaxComplete', preloader.endLoading, 'preloader_end')
    }
  })()