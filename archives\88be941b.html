<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>前端框架热门框架整理 | 你真是一个美好的人类</title><meta name="keywords" content="前端工具,前端框架"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="收集整理了一些目前比较热门新鲜度靠前的50款前端工具。">
<meta property="og:type" content="article">
<meta property="og:title" content="前端框架热门框架整理">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/88be941b.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="收集整理了一些目前比较热门新鲜度靠前的50款前端工具。">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184502.png">
<meta property="article:published_time" content="2020-05-01T13:11:02.000Z">
<meta property="article:modified_time" content="2020-05-01T13:11:02.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="前端工具">
<meta property="article:tag" content="前端框架">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184502.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/88be941b"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: '前端框架热门框架整理',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-05-01 13:11:02'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">前端框架热门框架整理</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-05-01T13:11:02.000Z" title="发表于 2020-05-01 13:11:02">2020-05-01</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-05-01T13:11:02.000Z" title="更新于 2020-05-01 13:11:02">2020-05-01</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/">前端</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/%E5%B7%A5%E5%85%B7/">工具</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><h2 id="构建工具"><a href="#构建工具" class="headerlink" title="构建工具"></a>构建工具</h2><h3 id="Parcel"><a href="#Parcel" class="headerlink" title="Parcel"></a>Parcel</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://parceljs.org/">https://parceljs.org</a></p>
<p>Parcel 是一款极速零配置 WEB 应用打包工具，快速、几乎零配置是它最大的特点，开箱即用。相比 webpack，Parcel 对于新手来说未尝不是一个很好的选择。</p>
<h3 id="Critters"><a href="#Critters" class="headerlink" title="Critters"></a>Critters</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/GoogleChromeLabs/critters">https://github.com/GoogleChromeLabs/critters</a></p>
<p>一款 webpack 的插件，它可以很方便的配置内联关键 css( critical CSS ),其余的 css 部分则会异步加载，由于它不使用无头浏览器(headless browser)呈现内容，因此快速轻巧。</p>
<h3 id="sucrase"><a href="#sucrase" class="headerlink" title="sucrase"></a>sucrase</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://sucrase.io/">https://sucrase.io/</a></p>
<p>如果你用 typscript 构建 React 项目，sucrase 将是一个不错的选择，它的编译速度将是 Babel 的 20 倍。sucrase——一款 ES6+编译器，重点关注非标准语言，例如 Typescript，JSX 和 Flow。</p>
<h3 id="Webpack-Config-Tool"><a href="#Webpack-Config-Tool" class="headerlink" title="Webpack Config Tool"></a>Webpack Config Tool</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://createapp.dev/">https://createapp.dev/</a></p>
<p>一款可视化的在线工具网站，你只需要选择前端项目运用到技术和相关配置，就能一键帮你生成 webpack.config.js，省去你不少的麻烦。</p>
<h3 id="JSUI"><a href="#JSUI" class="headerlink" title="JSUI"></a>JSUI</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/kitze/JSUI">https://github.com/kitze/JSUI</a></p>
<p>JSUI 是一个可视化分类、构建和管理 JavaScript 项目的工具。不管是前端应用还是后端应用，也不论使用的是哪种框架，只要项目有一个 package.json ，即可进行管理。</p>
<h3 id="PWA-Universal-Builder"><a href="#PWA-Universal-Builder" class="headerlink" title="PWA Universal Builder"></a>PWA Universal Builder</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://pwa.cafe/">https://pwa.cafe/</a></p>
<p>一款脚手架构建工具，方便创建基于 Preact，React，Vue 和 Svelte 的项目，开箱及支持 Babel，Bublé，Browserlist，TypeScript，PostCSS，ESLint，Prettier 和 Service Workers！</p>
<h3 id="VuePress"><a href="#VuePress" class="headerlink" title="VuePress"></a>VuePress</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://vuepress.vuejs.org/">https://vuepress.vuejs.org/</a></p>
<p>VuePress 由两部分组成：第一部分是一个极简静态网站生成器，它包含由 Vue 驱动的主题系统和插件 API，另一个部分是为书写技术文档而优化的默认主题，它的诞生初衷是为了支持 Vue 及其子项目的文档需求。</p>
<p>每一个由 VuePress 生成的页面都带有预渲染好的 HTML，也因此具有非常好的加载性能和搜索引擎优化（SEO）。同时，一旦页面被加载，Vue 将接管这些静态内容，并将其转换成一个完整的单页应用（SPA），其他的页面则会只在用户浏览到的时候才按需加载。</p>
<h2 id="框架和库"><a href="#框架和库" class="headerlink" title="框架和库"></a>框架和库</h2><h3 id="PWA-Starter-Kit"><a href="#PWA-Starter-Kit" class="headerlink" title="PWA Starter Kit"></a>PWA Starter Kit</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://pwa-starter-kit.polymer-project.org/">https://pwa-starter-kit.polymer-project.org/</a></p>
<p>通过功能丰富的 WEB 组件快速帮你构建功能齐全的 PWA 网站项目，几乎零配置，完成了构建、测试和快速部署。</p>
<h3 id="PaperCSS"><a href="#PaperCSS" class="headerlink" title="PaperCSS"></a>PaperCSS</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="http://www.getpapercss.com/">www.getpapercss.com/</a></p>
<p>一个不太常规的 CSS 框架，如果你希望你的网站有手绘风格感觉，选择它准没错。</p>
<h3 id="boardgame-io"><a href="#boardgame-io" class="headerlink" title="boardgame.io"></a>boardgame.io</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://boardgame.io/">https://boardgame.io/</a></p>
<p>BOARDAME.IO 是 Google 开源的一个游戏框架，旨在允许游戏作者将游戏规则从本质上转化为一系列简单的函数，这些函数用于描述当一个指定动作发生时游戏的状态变化，框架负责处理表述性状态传递。无需再手动编写任何网络或后端代码。</p>
<p>功能特性</p>
<p>状态管理：自动跨浏览器、服务器和存储器无缝管理游戏状态；</p>
<p>快速成型：在渲染游戏之前调试界面以模拟更改。</p>
<p>多人游戏：所有连接到同一游戏的浏览器都实时同步，无需刷新。</p>
<p>私密状态：私密信息可从客户端隐藏。</p>
<p>日志：游戏日志可查看任意时间的信息。</p>
<p>UI 工具包：常用于游戏中的 React 组件。</p>
<h3 id="Stimulus"><a href="#Stimulus" class="headerlink" title="Stimulus"></a>Stimulus</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://stimulusjs.org/">https://stimulusjs.org/</a></p>
<p>Stimulus 是一个适度的前端框架，它并不试图接管整个前端的方方面面，不关心如何渲染 HTML，相反用来增强 HTML 的相关行为。如果你的团队规模较小，但又想要和那些使用比较费力的主流方案的较大团队竞争，那么这是一个比较适合的前端框架方案。</p>
<h3 id="sapper"><a href="#sapper" class="headerlink" title="sapper"></a>sapper</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://sapper.svelte.dev/">https://sapper.svelte.dev/</a></p>
<p>Sapper 是一个类似 Next.js 的框架，具有极高的性能和内存效率，具备代码分割，服务端渲染的现代框架功能，是一款军工级别的框架。</p>
<h3 id="Reakit"><a href="#Reakit" class="headerlink" title="Reakit"></a>Reakit</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://reakit.io/">https://reakit.io/</a></p>
<p>使用这个框架能让你快速搭建漂亮的 React UI 交互站点。</p>
<h3 id="Evergreen"><a href="#Evergreen" class="headerlink" title="Evergreen"></a>Evergreen</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://evergreen.segment.com/">https://evergreen.segment.com/</a></p>
<p>更为强大的 React UI 框架，有一套非常标准的 UI 设计语言帮你构建企业级的具有国际范设计风格的 WEB 应用，这个框架类似我们国内的 ant.design（<a target="_blank" rel="noopener external nofollow noreferrer" href="https://ant.design/docs/spec/colors-cn%EF%BC%89">https://ant.design/docs/spec/colors-cn）</a></p>
<h2 id="HTML-和-CSS-工具"><a href="#HTML-和-CSS-工具" class="headerlink" title="HTML 和 CSS 工具"></a>HTML 和 CSS 工具</h2><h3 id="keyframes-app"><a href="#keyframes-app" class="headerlink" title="keyframes.app"></a>keyframes.app</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://keyframes.app/">https://keyframes.app/</a></p>
<p>一款基于时间关键帧，在线制作网页动画的网站，你无需在编辑器和浏览器直接互相切换，及所见即所得。keyframes.app 提供在线制作和谷歌浏览器扩展插件两种形式。制作完成后，你能很方便的将自动产生的 CSS 代码复制到你的项目中。</p>
<h3 id="Emotion"><a href="#Emotion" class="headerlink" title="Emotion"></a>Emotion</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://emotion.sh/docs/introduction">https://emotion.sh/docs/introduction</a></p>
<p>Emotion 是一款用 JavaScript 编写 css 的库，支持字符串和对象两种方式声明 CSS 变量，如果你在使用 React，试用这个库将让你以更加优雅的方式用 JavaScript 编写 CSS。</p>
<h3 id="modern-normalize"><a href="#modern-normalize" class="headerlink" title="modern-normalize"></a>modern-normalize</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/sindresorhus/modern-normalize">https://github.com/sindresorhus/modern-normalize</a></p>
<p>normalize.css 可以让浏览器以接近标准的方式一致地渲染所有元素，而且不同于 cssrest，只针对需要正常化的元素。modern-normalize 只针对现代浏览器，而且足够现代，甚至 IE 和 Edge 都已经放弃。</p>
<h3 id="layerJS"><a href="#layerJS" class="headerlink" title="layerJS"></a>layerJS</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://layerjs.org/">https://layerjs.org/</a></p>
<p>一款你只需要编写 HTML 就能很轻松实现菜单、画框、弹出层、滚动视察、缩放、触摸手势等众多效果的框架，这个框架代码压缩版只有 30KB,很方便与各种前端框架集成(Angular,VueJS,React),支持响应式和触摸，并且不依赖任何库就能实现。</p>
<h3 id="css-blocks"><a href="#css-blocks" class="headerlink" title="css-blocks"></a>css-blocks</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://css-blocks.com/">https://css-blocks.com/</a></p>
<p>一款受 CSS Modules, BEM 和 Atomic CSS 框架启发，为你的 web 应用组件提供完美的 CSS 模块方案。</p>
<h3 id="usebasin"><a href="#usebasin" class="headerlink" title="usebasin"></a>usebasin</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://usebasin.com/">https://usebasin.com/</a></p>
<p>一款你只需要设计表单，无需编写后端代码，就能很方便的将表单应用集成到你的项目里。</p>
<h3 id="mustard"><a href="#mustard" class="headerlink" title="mustard"></a>mustard</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="http://mustard-ui.com/">http://mustard-ui.com/</a></p>
<p>一款适合初学者的 CSS 框架，但是看起来还蛮不错，模块化，开源，压缩版只有 6KB，支持 FLEX,Grid 布局和自带一些漂亮 UI,比如进度条，表单、按钮等，虽然小，但功能齐全。</p>
<h2 id="javascript-工具"><a href="#javascript-工具" class="headerlink" title="javascript 工具"></a>javascript 工具</h2><h3 id="ScrollHint"><a href="#ScrollHint" class="headerlink" title="ScrollHint"></a>ScrollHint</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://appleple.github.io/">https://appleple.github.io/</a></p>
<p>一个 JS 库，用于指示元素可以水平滚动，并带有指针图标，如果你在做一个新手引导，这个工具将会是一个不错的选择。</p>
<h3 id="ToastUI-editor"><a href="#ToastUI-editor" class="headerlink" title="ToastUI editor"></a>ToastUI editor</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/TOAST-DOCS/ToastUI-Editor">https://github.com/TOAST-DOCS/ToastUI-Editor</a></p>
<p>强大的 Markdown 编辑器 tui.editor,方便集成到你的项目里，这款强大的富媒体编辑器有以下特点：</p>
<p>支持 CommonMark 与 GFM（GitHub Flavored Markdown）两种标准；</p>
<p>支持丰富的扩展插件，如颜色选择器、图表、UML、表格合并</p>
<p>提供了所见即所得与 Markdown 这两种模式，在编辑过程中可以随时切换，非常方便。在所见即所得模式下，可以直接从浏览器、 Excel、PPT 等复制文本，并且保留原来的格式。</p>
<h3 id="FilePond"><a href="#FilePond" class="headerlink" title="FilePond"></a>FilePond</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/pqina/filepond">https://github.com/pqina/filepond</a></p>
<p>Filepond 是一个用于文件上传的 JavaScript 库，可以上传任何内容，优化图像以获得更快的上传速度，并提供一个出色的，可访问的，流畅的用户体验。</p>
<p>Filepond 提供了多种上传方式：拖放，复制和粘贴文件，浏览文件系统或仅使用库的 API。gzip 压缩后仅有 21KB ，并且内置了图像优化和图像自动调整功能。</p>
<p>Filepond 适用于 React ， Vue ， Angular 和 jQuery 。</p>
<h3 id="Dinero-js"><a href="#Dinero-js" class="headerlink" title="Dinero.js"></a>Dinero.js</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://dinerojs.com/">https://dinerojs.com/</a></p>
<p>一个用来创建、计算和格式化货币价值的不可变的框架。</p>
<p>无论在银行应用程序、电子商务网站还是证券交易所平台，我们每天都在与金钱互动。我们也越来越依赖技术来处理问题。</p>
<p>然而，关于如何以编程处理货币价值尚无共识。虽然金钱是现代社会中普遍存在的概念，但相较于日期和时间之类的东西，它并不是任何主流语言中的一流数据类型。结果，每一种软件都有自己的处理方式，且伴随着陷阱。</p>
<p>Dinero.js 遵循 Fowler 的模式更多一点儿。它允许你在 JavaScript 中创建、计算和格式化货币值。你可以进行数学运算、解析和格式化对象，使你的开发过程更加轻松。</p>
<p>该库设计为不可变和可链接的模式。它支持全局设置，具有扩展格式选项，并提供本机国际化支持。</p>
<h3 id="Swup"><a href="#Swup" class="headerlink" title="Swup"></a>Swup</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/swup/swup">https://github.com/swup/swup</a></p>
<p>一款适合初学者的框架，方便灵活易用，让你能快速制作专业级的页面转场动画效果。</p>
<h3 id="Selection-js"><a href="#Selection-js" class="headerlink" title="Selection.js"></a>Selection.js</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://simonwep.github.io/selection/">https://simonwep.github.io/selection/</a></p>
<p>简单易用的可视化，支持鼠标拖拽、使用 Cmd/Ctrl+click 选择页面元素的库（支持分组选择），大大节省了你的开发时间。只有 3KB 大小，不依赖 jQuery。</p>
<h3 id="Glider-js"><a href="#Glider-js" class="headerlink" title="Glider.js"></a>Glider.js</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://nickpiscitelli.github.io/Glider.js/">https://nickpiscitelli.github.io/Glider.js/</a></p>
<p>一个超快速(25 毫秒加载)，轻量级(小于 3KB)，无依赖性(不需要 jQuery)的制作幻灯效果的前端库，支持响应式，易于扩展，方便自定义事件等…，更多特性等待你的发现！</p>
<h3 id="ScrollOut"><a href="#ScrollOut" class="headerlink" title="ScrollOut"></a>ScrollOut</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://scroll-out.github.io/">https://scroll-out.github.io/</a></p>
<p>一款帮你制作专业级 Scroll 滚动效果（滚动视差）的框架，框架大小不到 1KB,使用回调的方式将相关动画元素的属性进行实时分配，方便你做出个性化的动态效果。</p>
<h2 id="图标、图表工具"><a href="#图标、图表工具" class="headerlink" title="图标、图表工具"></a>图标、图表工具</h2><h3 id="Orion-Icon-Library"><a href="#Orion-Icon-Library" class="headerlink" title="Orion Icon Library"></a>Orion Icon Library</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://orioniconlibrary.com/">https://orioniconlibrary.com/</a></p>
<p>多达 6000 专业免费的 SVG 矢量图标，还支持深度的定制，比如更换配色，更改线条粗细，变换图标风格(细线条、粗线条、扁平)，一键生成相关代码。</p>
<h3 id="Frappe-Charts"><a href="#Frappe-Charts" class="headerlink" title="Frappe Charts"></a>Frappe Charts</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://frappe.io/charts">https://frappe.io/charts</a></p>
<p>一款简单、专业、开源、现代风格的 SVG 报表工具,不需要任何依赖库，代码风格简单，简单易用。支持一键导出 svg 代码。</p>
<h3 id="SVGator"><a href="#SVGator" class="headerlink" title="SVGator"></a>SVGator</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://www.svgator.com/">https://www.svgator.com/</a></p>
<p>如果您希望将 Web 图形提升到一个新的水平，那么动画 SVG 就是您的选择，而 SVGator 是您可以用来创建它们的最简单的工具之一。</p>
<p>一款专业级的 SVG 动画制在线制作工具。SVGator 还具有代码管理器面板，因此您可以准确地看到应用程序生成的代码。SVGator 导出干净，格式良好的代码。</p>
<h3 id="ApexCharts"><a href="#ApexCharts" class="headerlink" title="ApexCharts"></a>ApexCharts</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://apexcharts.com/">https://apexcharts.com/</a></p>
<p>ApexCharts.JS 是一个现代化 JavaScript 图表库，用于使用简单的 API 构建交互式图表和可视化，功能十分强大。方便你将图表嵌入到你的 Vue、React 项目中。</p>
<h3 id="MapKit-JS"><a href="#MapKit-JS" class="headerlink" title="MapKit JS"></a>MapKit JS</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://developer.apple.com/">https://developer.apple.com/</a></p>
<p>一款苹果公司提供的地图工具，如果想制作和苹果官方网站一样的地图风格，这个工具将是一个不错的选择，允许你在地图上添加交互事件，丰富你的地图应用。</p>
<h3 id="Img2"><a href="#Img2" class="headerlink" title="Img2"></a>Img2</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/javierbyte/img2css">https://github.com/javierbyte/img2css</a></p>
<p>一款图片自动预加载和缓存工具，防止图片闪烁，并使用模糊滤镜预先显示图片延迟图片加载，提高网页加载速度，使用起来非常简单，你只需要使用<img-2>替代<img/>标签即可，使用起来就是这么简单！</p>
<h3 id="Lozad"><a href="#Lozad" class="headerlink" title="Lozad"></a>Lozad</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/ApoorvSaxena/lozad.js">https://github.com/ApoorvSaxena/lozad.js</a></p>
<p>Lozad.js 是基于 IntersectionObserver API 的轻量级、高性能、可配置的纯 JavaScript 并且无依赖的懒加载器，其能够被用于进行图片、iframe 等多种形式的元素。</p>
<p>通过 gzip 压缩过后，仅仅 1kb 大小，相对于常用的 jquery.lazyload.js 来说，lozad.js 实力碾压，虽然 jquery.lazyload.js 也才几 kb 大小。在 github 上，已经收获了 4800+的 star。</p>
<h2 id="React-工具"><a href="#React-工具" class="headerlink" title="React 工具"></a>React 工具</h2><h3 id="RSUITE"><a href="#RSUITE" class="headerlink" title="RSUITE"></a>RSUITE</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://rsuitejs.com/">https://rsuitejs.com/</a></p>
<p>React Suite 是一套 React 组件库，为后台产品而生。由 HYPERS 前端团队与 UX 团队打造，主要服务于公司大数据产品线。</p>
<p>经历了三次大的版本更新后，累积了大量的组件和丰富的功能。并支持在线定制个性化主题，更重要的是有中文版，方便我们学习使用。</p>
<h3 id="Pagedraw"><a href="#Pagedraw" class="headerlink" title="Pagedraw"></a>Pagedraw</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://pagedraw.io/">https://pagedraw.io/</a></p>
<p>一款神奇的在线 UI 设计制作工具，你只需要拖动和布局页面，这个工具就会给你自动生成质量高的 React 组件代码，更多功能等待你的发掘。</p>
<h3 id="react-smooth-dnd"><a href="#react-smooth-dnd" class="headerlink" title="react-smooth-dnd"></a>react-smooth-dnd</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/kutlugsahin/react-smooth-dnd">https://github.com/kutlugsahin/react-smooth-dnd</a></p>
<p>一款拖拽页面元素的 React 工具，拖拽效果平滑，让你轻松就能实现卡片、列表、表单组件的的拖拽。</p>
<h3 id="Unstated"><a href="#Unstated" class="headerlink" title="Unstated"></a>Unstated</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/jamiebuilds/unstated">https://github.com/jamiebuilds/unstated</a></p>
<p>一个新的状态管理类库 unstated.js：简单易用/合理。和之前的 state 管理库思路完全不同，这个 unstated 主打 local state（不是全局 store，一个小改动导致整棵树 rerender），多个 local state 之间也可以共享， 兼具了 redux 的易用性与 flux 的合理性，令人耳目一新；unstated 完全就是为 React 设计的，“足够 React”，让你感觉不到在用第三方组件。</p>
<h3 id="Reach-Router"><a href="#Reach-Router" class="headerlink" title="Reach Router"></a>Reach Router</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://reach.tech/router">https://reach.tech/router</a></p>
<p>Reach-Router 是前 ReactRouter 成员 Ryan Florence 开发的一套基于 react 的路由控件。</p>
<h3 id="SVGR"><a href="#SVGR" class="headerlink" title="SVGR"></a>SVGR</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="http://www.smooth-code.com/">http://www.smooth-code.com/</a></p>
<p>svgr 是一个将 SVG 转换为 React 组件的工具，svgr 由 JavaScript 实现。整个文档也非常的小，已开源在 github 上。</p>
<h3 id="React-Spreadsheet-Grid"><a href="#React-Spreadsheet-Grid" class="headerlink" title="React Spreadsheet Grid"></a>React Spreadsheet Grid</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/denisraslov/react-spreadsheet-grid">https://github.com/denisraslov/react-spreadsheet-grid</a></p>
<p>用于 React 类似于 Excel 的网格组件，具有自定义单元格编辑器，高性能滚动和可调整大小的列。</p>
<h2 id="测试和数据工具"><a href="#测试和数据工具" class="headerlink" title="测试和数据工具"></a>测试和数据工具</h2><h3 id="webhint"><a href="#webhint" class="headerlink" title="webhint"></a>webhint</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://webhint.io/">https://webhint.io/</a></p>
<p>Webhint 项目提供了一种用于检查代码的可访问性、性能和安全的开源检查（Linting）工具。在创建 Web 站点和应用中，有越来越多的细节问题亟待完善。为此，Webhint 力图帮助开发人员标记这些细节。</p>
<p>Webhint 以命令行接口（CLI）工具和在线扫描器两种形式提供，使用在线扫描器是最快的上手方式。使用在线扫描器需要为其提供一个公开的 URL，用于运行报告并洞悉应用的运行情况。</p>
<p>在测试应用时，Webhint 提供三种运行环境：jsdom、Chrome 和 Edge。后两种运行环境使用了 Chrome DevTools 协议，第一种运行环境使用 Node.js 环境快速地执行有限次数的检查，无需浏览器的支持。</p>
<p>还有更多强大的功能，还有待你试用挖掘。</p>
<h3 id="Airtap"><a href="#Airtap" class="headerlink" title="Airtap"></a>Airtap</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/airtap/airtap">https://github.com/airtap/airtap</a></p>
<p>Airtap 是一种在浏览器中测试 JavaScript 的简单方法，号称能覆盖 800 多种浏览器，能够在数秒内开始在本地测试你的代码，并无缝转移到由 Sauce Labs 提供的基于云的浏览器上，以获得更好的覆盖测试。</p>
<p>Airtap 与其他跨浏览器测试运行器的不同之处在于其简单性，以及能够在许多浏览器中轻松运行测试套件而无需在本地安装它们。它可以让你在开发过程中快速迭代，并在发布前提供良好的浏览器覆盖率，而不用担心缺少浏览器支持。</p>
<p>不要只是声称你的 JavaScript 支持“所有浏览器”，用测试证明它！</p>
<h3 id="mkcert"><a href="#mkcert" class="headerlink" title="mkcert"></a>mkcert</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/FiloSottile/mkcert">https://github.com/FiloSottile/mkcert</a></p>
<p>HTTPS 是 Web 发展的趋势，用于提高网站的安全性。使用 HTTPS 需要配置 TLS 证书，得益于 ACME 协议和 Let’s Encrypt 证书，远程环境可以很容易部署。但是对于本地环境，还没有普遍有效的证书。</p>
<p>mkcert 被设计的足够简单，隐藏了几乎所有生成 TLS 证书所必须的知识。它适用于任何主机名或者 IP，包括 localhost ，因为它只在你的本地环境使用。</p>
<p>证书是由你的私有 CA 签发，当你运行 mkcert-install 会自动配置这些信任，因此，当浏览器访问时，就会显示安全标识。目前支持 MacOS、Linux 和 Windows，以及 Firefox、Chrome 和 Java。甚至支持一些手机设备。</p>
<h3 id="Puppeteer-Recorder"><a href="#Puppeteer-Recorder" class="headerlink" title="Puppeteer Recorder"></a>Puppeteer Recorder</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/checkly/puppeteer-recorder">https://github.com/checkly/puppeteer-recorder</a></p>
<p>Puppeteer 是一个 Node 库，它提供了一个高级 API 来控制 DevTools 协议上的 Chrome 或 Chromium，常用于爬虫、自动化测试等，你在浏览器手动完成的大多数事情都可以使用它来完成。</p>
<h3 id="jsonstore-io"><a href="#jsonstore-io" class="headerlink" title="jsonstore.io"></a>jsonstore.io</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="http://www.jsonstore.io/">http://www.jsonstore.io/</a></p>
<p>jsonstore.io 为小型项目提供免费，安全且基于 JSON 的云数据存储。只需输入<a target="_blank" rel="noopener external nofollow noreferrer" href="https://www.jsonstore.io/%EF%BC%8C%E5%A4%8D%E5%88%B6URL%E5%B0%B1%E5%8F%AF%E4%BB%A5%E5%BC%80%E5%A7%8B%E5%8F%91%E9%80%81HTTP%E6%95%B0%E6%8D%AE%E8%AF%B7%E6%B1%82%E3%80%82POST%E8%AF%B7%E6%B1%82%E5%B0%86%E4%BF%9D%E5%AD%98%E6%95%B0%E6%8D%AE%EF%BC%8CPUT%E8%AF%B7%E6%B1%82%E4%BF%AE%E6%94%B9%E6%95%B0%E6%8D%AE%EF%BC%8CDELETE%E8%AF%B7%E6%B1%82%E5%88%A0%E9%99%A4%E6%95%B0%E6%8D%AE%E5%92%8CGET%E8%AF%B7%E6%B1%82%E6%A3%80%E7%B4%A2%E6%95%B0%E6%8D%AE%E3%80%82%E5%A4%A7%E5%A4%A7%E6%96%B9%E4%BE%BF%E4%BA%86%E5%89%8D%E7%AB%AF%E5%BC%80%E5%8F%91%E4%BA%BA%E5%91%98%E8%BF%9B%E8%A1%8C%E6%B5%8B%E8%AF%95%E6%8E%A5%E5%8F%A3%E7%9A%84%E9%9B%86%E6%88%90%EF%BC%8C%E5%89%8D%E7%AB%AF%E9%A1%B5%E9%9D%A2%E5%88%B6%E4%BD%9C%E5%AE%8C%E6%88%90%E5%B0%B1%E8%83%BD%E8%BF%9B%E8%A1%8C%E6%8E%A5%E5%8F%A3%E6%B5%8B%E8%AF%95%EF%BC%8C%E4%BD%BF%E7%94%A8%E8%B5%B7%E6%9D%A5%E5%B0%B1%E6%98%AF%E8%BF%99%E4%B9%88%E7%AE%80%E5%8D%95%E3%80%82">https://www.jsonstore.io/，复制URL就可以开始发送HTTP数据请求。POST请求将保存数据，PUT请求修改数据，DELETE请求删除数据和GET请求检索数据。大大方便了前端开发人员进行测试接口的集成，前端页面制作完成就能进行接口测试，使用起来就是这么简单。</a></p>
<h3 id="Initab"><a href="#Initab" class="headerlink" title="Initab"></a>Initab</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="http://initab.com/">http://initab.com/</a></p>
<p>一款为开发人员定制打造的工作台，通过谷歌浏览器插件安装即可使用，通过此工作台你可以轻松订阅你感兴趣的 git 项目、跟进相关问题、pull 相关操作，查看版本历史，订阅 Stack Overflow 相关的内容，管理查看 Gists 相关内容，可以说一个主流技术平台聚合工作台。</p>
<h3 id="lambdatest"><a href="#lambdatest" class="headerlink" title="lambdatest"></a>lambdatest</h3><p>地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://www.lambdatest.com/">https://www.lambdatest.com/</a></p>
<p>一款在线自动化测试云端平台，号称在 2000 多个真实浏览器和设备进行测试，可以根据你的测试需求进行深度定制，并形成相关记录，方便团队的协作，帮你发现跨平台不同浏览器版本的各种问题。</p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/88be941b.html">http://blog.mhy.loc.cc/archives/88be941b.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/%E5%89%8D%E7%AB%AF%E5%B7%A5%E5%85%B7/">前端工具</a><a class="post-meta__tags" href="/tags/%E5%89%8D%E7%AB%AF%E6%A1%86%E6%9E%B6/">前端框架</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184502.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/b1e33f9d.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">sitemeta渐变背景实现</div></div></a></div><div class="next-post pull-right"><a href="/archives/285695a6.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184106.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">解决滚动条导致页面跳动的问题</div></div></a></div></nav></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%9E%84%E5%BB%BA%E5%B7%A5%E5%85%B7"><span class="toc-text">构建工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#Parcel"><span class="toc-text">Parcel</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Critters"><span class="toc-text">Critters</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#sucrase"><span class="toc-text">sucrase</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Webpack-Config-Tool"><span class="toc-text">Webpack Config Tool</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#JSUI"><span class="toc-text">JSUI</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#PWA-Universal-Builder"><span class="toc-text">PWA Universal Builder</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#VuePress"><span class="toc-text">VuePress</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%A1%86%E6%9E%B6%E5%92%8C%E5%BA%93"><span class="toc-text">框架和库</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#PWA-Starter-Kit"><span class="toc-text">PWA Starter Kit</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#PaperCSS"><span class="toc-text">PaperCSS</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#boardgame-io"><span class="toc-text">boardgame.io</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Stimulus"><span class="toc-text">Stimulus</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#sapper"><span class="toc-text">sapper</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Reakit"><span class="toc-text">Reakit</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Evergreen"><span class="toc-text">Evergreen</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#HTML-%E5%92%8C-CSS-%E5%B7%A5%E5%85%B7"><span class="toc-text">HTML 和 CSS 工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#keyframes-app"><span class="toc-text">keyframes.app</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Emotion"><span class="toc-text">Emotion</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#modern-normalize"><span class="toc-text">modern-normalize</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#layerJS"><span class="toc-text">layerJS</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#css-blocks"><span class="toc-text">css-blocks</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#usebasin"><span class="toc-text">usebasin</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#mustard"><span class="toc-text">mustard</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#javascript-%E5%B7%A5%E5%85%B7"><span class="toc-text">javascript 工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#ScrollHint"><span class="toc-text">ScrollHint</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#ToastUI-editor"><span class="toc-text">ToastUI editor</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#FilePond"><span class="toc-text">FilePond</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Dinero-js"><span class="toc-text">Dinero.js</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Swup"><span class="toc-text">Swup</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Selection-js"><span class="toc-text">Selection.js</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Glider-js"><span class="toc-text">Glider.js</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#ScrollOut"><span class="toc-text">ScrollOut</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%9B%BE%E6%A0%87%E3%80%81%E5%9B%BE%E8%A1%A8%E5%B7%A5%E5%85%B7"><span class="toc-text">图标、图表工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#Orion-Icon-Library"><span class="toc-text">Orion Icon Library</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Frappe-Charts"><span class="toc-text">Frappe Charts</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#SVGator"><span class="toc-text">SVGator</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#ApexCharts"><span class="toc-text">ApexCharts</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#MapKit-JS"><span class="toc-text">MapKit JS</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Img2"><span class="toc-text">Img2</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Lozad"><span class="toc-text">Lozad</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#React-%E5%B7%A5%E5%85%B7"><span class="toc-text">React 工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#RSUITE"><span class="toc-text">RSUITE</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Pagedraw"><span class="toc-text">Pagedraw</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#react-smooth-dnd"><span class="toc-text">react-smooth-dnd</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Unstated"><span class="toc-text">Unstated</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Reach-Router"><span class="toc-text">Reach Router</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#SVGR"><span class="toc-text">SVGR</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#React-Spreadsheet-Grid"><span class="toc-text">React Spreadsheet Grid</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%B5%8B%E8%AF%95%E5%92%8C%E6%95%B0%E6%8D%AE%E5%B7%A5%E5%85%B7"><span class="toc-text">测试和数据工具</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#webhint"><span class="toc-text">webhint</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Airtap"><span class="toc-text">Airtap</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#mkcert"><span class="toc-text">mkcert</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Puppeteer-Recorder"><span class="toc-text">Puppeteer Recorder</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#jsonstore-io"><span class="toc-text">jsonstore.io</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Initab"><span class="toc-text">Initab</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#lambdatest"><span class="toc-text">lambdatest</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>