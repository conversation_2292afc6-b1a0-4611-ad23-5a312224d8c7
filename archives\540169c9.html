<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Hexo框架(四)：Next主题文章置顶和公告效果 | 你真是一个美好的人类</title><meta name="keywords" content="博客搭建,Hexo"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="（1）文章置顶；（2）公告效果； （3）从侧边栏去掉文章计数； （4）从归档中去掉文章； （5）一个bug">
<meta property="og:type" content="article">
<meta property="og:title" content="Hexo框架(四)：Next主题文章置顶和公告效果">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/540169c9.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="（1）文章置顶；（2）公告效果； （3）从侧边栏去掉文章计数； （4）从归档中去掉文章； （5）一个bug">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png">
<meta property="article:published_time" content="2020-02-13T08:25:13.000Z">
<meta property="article:modified_time" content="2020-02-13T08:25:13.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="博客搭建">
<meta property="article:tag" content="Hexo">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/540169c9"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Hexo框架(四)：Next主题文章置顶和公告效果',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-02-13 08:25:13'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Hexo框架(四)：Next主题文章置顶和公告效果</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-02-13T08:25:13.000Z" title="发表于 2020-02-13 08:25:13">2020-02-13</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-02-13T08:25:13.000Z" title="更新于 2020-02-13 08:25:13">2020-02-13</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><div class="note danger flat"><h2 id="前言："><a href="#前言：" class="headerlink" title="前言："></a>前言：</h2><p>本篇设置基于 NEXT 主题 7.7.2 版本！！<strong>注意：目前我已经更新至最新版本，目前采用了全新的配置方式，这里的主题配置文件，均指 hexo/source/_data 目录下的 next.yml 文件，你可以点击这里查看：<a href="/archives/5b20fbd0.html">关于博客主题持续更新的问题和我的新配置方式</a></strong></p>
<p>此外，由于对于要修改源码的配置方式，目前我均已放弃。</p>
</div>

<h2 id="文章置顶"><a href="#文章置顶" class="headerlink" title="文章置顶"></a>文章置顶</h2><p>Hexo 本身并没有内置文章置顶功能，因此需要自行安装。不过 Hexo 本身有一个对文章排序的组件，也就是在站点配置文件内的 <code>index_generator</code> 选项，置顶功能其实就是每次排序的时候，把其中的置顶文章排在最前，本质上是一个排序组件，Hexo 默认的是 <code>hexo-generator-index</code>，所以先卸载再重新安装一个可以置顶的排序组件：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"># 先卸载</span><br><span class="line">npm uninstall --save hexo-generator-index</span><br><span class="line"></span><br><span class="line"># 再安装</span><br><span class="line">npm install --save hexo-generator-index-pin-top</span><br></pre></td></tr></table></figure>

<p>从插件名字上就能看得出来支持置顶了。该插件的 GitHub 地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/netcan/hexo-generator-index-pin-top">hexo-generator-index-pin-top</a>。插件安装完之后，只需要在文章头部信息栏内设置 <code>top</code> 属性即可：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">---</span><br><span class="line">title: Hexo博客文章置顶</span><br><span class="line">date: 2020-03-31 07:31:04</span><br><span class="line">top: true</span><br><span class="line">---</span><br></pre></td></tr></table></figure>

<p>这样这篇文章就具有置顶效果了。不过，仅仅只是这么做，文章虽然确实置顶了，但是从文章列表上来看，和普通的文章没什么不同。如果不特意去对比文章发布时间，可能会以为只是最新的文章而已。例如一些说明、通知之类的，为了能有个比较突出的标志，可以在 <code>next/layout/_macro/post.swig</code> 文件中找到以下位置并添加代码：</p>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="operator">&lt;</span>div <span class="keyword">class</span>=&quot;<span class="title class_">post</span>-<span class="title class_">meta</span>&quot;&gt;</span><br><span class="line">  &#123;<span class="operator">%========</span> 加在这里<span class="operator">============%</span>&#125;</span><br><span class="line">  &#123;<span class="operator">%</span> <span class="keyword">if</span> post.top <span class="operator">%</span>&#125;</span><br><span class="line">    <span class="operator">&lt;</span>i <span class="keyword">class</span>=&quot;<span class="title class_">fa</span> <span class="title class_">fa</span>-<span class="title class_">thumb</span>-<span class="title class_">tack</span>&quot; <span class="title class_">style</span>=&quot;<span class="title class_">color</span>: #<span class="title class_">EB6D39</span>&quot;&gt;&lt;/<span class="title class_">i</span>&gt;</span><br><span class="line">    &lt;<span class="title class_">font</span> <span class="title class_">color</span>=<span class="title class_">EB6D39</span>&gt;置顶&lt;/<span class="title class_">font</span>&gt;</span><br><span class="line">    &lt;<span class="title class_">span</span> <span class="title class_">class</span>=&quot;<span class="title class_">post</span>-<span class="title class_">meta</span>-<span class="title class_">divider</span>&quot;&gt;|&lt;/<span class="title class_">span</span>&gt;</span><br><span class="line">  &#123;<span class="operator">%</span> endif <span class="operator">%</span>&#125;</span><br><span class="line">  &#123;<span class="operator">%</span> <span class="operator">============================</span> <span class="operator">%</span>&#125;</span><br><span class="line">&#123;<span class="operator">%-</span> <span class="keyword">set</span> date_diff <span class="operator">=</span> date(post.date) <span class="operator">!=</span> date(post.updated) <span class="operator">%</span>&#125;</span><br></pre></td></tr></table></figure>

<p>这里的图标、文字、以及各自对应的颜色都可以自定义。完成后的效果就是：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200331204310.png" alt="image-20200331204301994"></p>
<h2 id="公告效果"><a href="#公告效果" class="headerlink" title="公告效果"></a>公告效果</h2><p>现在文章置顶已经成功实现了，但是还有个问题，比如像我的博客，文章置顶是类似于一个窗格的形式，更符合“置顶消息”这么一个设定，没有标题、没有分类和标签、没有日期等等，但是如果不填写 title 栏，仍然会显示一个默认的“未命名”标题：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200331204715.png" alt="image-20200331204713823"></p>
<p>而且会导致搜索无法使用，因此如果想实现这种：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200331204739.png" alt="文章置顶"></p>
<p>就需要在文章的头部信息栏加入一个 <code>header</code> 属性：</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">---</span><br><span class="line"><span class="attr">title</span>: 谢谢你来看我的博客</span><br><span class="line"><span class="attr">date</span>: <span class="number">2020</span>-<span class="number">01</span>-<span class="number">31</span> <span class="number">17</span>:<span class="number">09</span>:<span class="number">13</span></span><br><span class="line"><span class="attr">top</span>: <span class="literal">true</span></span><br><span class="line"><span class="attr">header</span>: <span class="literal">false</span></span><br></pre></td></tr></table></figure>

<p>这样文章就会变成一个完全没有标题和各种属性的引用块了。</p>
<div class="note danger flat"><p>注意：下面的内容，因为涉及到修改源码，目前我已经不再使用了！</p>
</div>

<h2 id="从侧边栏去掉文章计数"><a href="#从侧边栏去掉文章计数" class="headerlink" title="从侧边栏去掉文章计数"></a>从侧边栏去掉文章计数</h2><p>正常情况下，Next 主题侧栏会有一个显示文章、分类、标签的计数：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200331205046.png" alt="侧边栏"></p>
<p>但是如果我们创建了一个上文所示的“公告”，而只是用来做一些通知、信息展示等功能，可能就并不想把这些文章算进去。想要减掉这部分的文章数量，可以在 <code>themes/next/layout/_partials/header/menu-item.swig</code> 文件里找到<code>archives:site.posts.length</code>部分并修改,将数量减 1 即可。</p>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">&#123;<span class="operator">%-</span> <span class="keyword">set</span> badges <span class="operator">=</span> &#123;</span><br><span class="line">  archives  : site.posts.length <span class="operator">-</span> <span class="number">1</span>,</span><br><span class="line">  categories: site.categories.length,</span><br><span class="line">  tags      : site.tags.length</span><br><span class="line">	&#125;</span><br><span class="line"><span class="operator">%</span>&#125;</span><br></pre></td></tr></table></figure>

<p>然后找到<code>themes/next/layout/_partials/sidebar/site-overview.swig </code> 文件里找到<code>site.posts.length</code> 进行修改，将 length - 1 即可</p>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line">&#123;<span class="operator">%-</span> <span class="keyword">if</span> theme.site_state <span class="operator">%</span>&#125;</span><br><span class="line"><span class="operator">&lt;</span>div <span class="keyword">class</span>=&quot;<span class="title class_">site</span>-<span class="title class_">state</span>-<span class="title class_">wrap</span> <span class="title class_">motion</span>-<span class="title class_">element</span>&quot;&gt;</span><br><span class="line">  &lt;<span class="title class_">nav</span> <span class="title class_">class</span>=&quot;<span class="title class_">site</span>-<span class="title class_">state</span>&quot;&gt;</span><br><span class="line">    &#123;<span class="operator">%-</span> <span class="keyword">if</span> config.archive_dir <span class="operator">!=</span> &#x27;<span class="operator">/</span>&#x27; and site.posts.length <span class="operator">&gt;</span> <span class="number">0</span> <span class="operator">%</span>&#125;</span><br><span class="line">      <span class="operator">&lt;</span>div <span class="keyword">class</span>=&quot;<span class="title class_">site</span>-<span class="title class_">state</span>-<span class="title class_">item</span> <span class="title class_">site</span>-<span class="title class_">state</span>-<span class="title class_">posts</span>&quot;&gt;</span><br><span class="line">        &#123;<span class="operator">%-</span> <span class="keyword">if</span> theme.menu.archives <span class="operator">%</span>&#125;</span><br><span class="line">          <span class="operator">&lt;</span>a href<span class="operator">=</span><span class="string">&quot;&#123;&#123; url_for(theme.menu.archives.split(&#x27;||&#x27;)[0] | trim) &#125;&#125;&quot;</span><span class="operator">&gt;</span></span><br><span class="line">        &#123;<span class="operator">%</span> <span class="keyword">else</span> <span class="operator">%</span>&#125;</span><br><span class="line">          <span class="operator">&lt;</span>a href<span class="operator">=</span><span class="string">&quot;&#123;&#123; url_for(config.archive_dir) &#125;&#125;&quot;</span><span class="operator">&gt;</span></span><br><span class="line">        &#123;<span class="operator">%-</span> endif <span class="operator">%</span>&#125;</span><br><span class="line">					&#123;<span class="operator">%=============================</span>就是这里<span class="operator">↓===============================%</span>&#125;</span><br><span class="line">          <span class="operator">&lt;</span>span <span class="keyword">class</span>=&quot;<span class="title class_">site</span>-<span class="title class_">state</span>-<span class="title class_">item</span>-<span class="title class_">count</span>&quot;&gt;&#123;&#123; site.posts.length <span class="operator">-</span> <span class="number">1</span>&#125;&#125;<span class="operator">&lt;/</span>span<span class="operator">&gt;</span></span><br><span class="line">					&#123;<span class="operator">%=============================</span>就是这里<span class="operator">↑===============================%</span>&#125;</span><br><span class="line">          <span class="operator">&lt;</span>span <span class="keyword">class</span>=&quot;<span class="title class_">site</span>-<span class="title class_">state</span>-<span class="title class_">item</span>-<span class="title class_">name</span>&quot;&gt;&#123;&#123; __(&#x27;state.posts&#x27;) &#125;&#125;<span class="operator">&lt;/</span>span<span class="operator">&gt;</span></span><br><span class="line">        <span class="operator">&lt;/</span>a<span class="operator">&gt;</span></span><br><span class="line">      <span class="operator">&lt;/</span>div<span class="operator">&gt;</span></span><br><span class="line">    &#123;<span class="operator">%-</span> endif <span class="operator">%</span>&#125;</span><br></pre></td></tr></table></figure>

<p>其实就是直接把 <code>site.posts.length</code> 减掉了 1 而已，方法是笨方法，因为如果删掉了那篇文章，或者新增了一篇，还要再改一次源码，不过这是个备用方法，看看有没有办法能找到 <code>site.posts</code> 统计的地方然后直接不读取指定属性的文章。</p>
<h2 id="从‘归档’中去掉文章"><a href="#从‘归档’中去掉文章" class="headerlink" title="从‘归档’中去掉文章"></a>从‘归档’中去掉文章</h2><p>如果选择从侧栏的文章计数中去掉了某篇文章，可能也会希望“归档”里同样不记录，同样，归档里也有一个文章计数，还另有一个所有文章的列表，可以看到我的归档里并不包括置顶那个“信息栏”的文章，修改的方式也很类似，找到 <code>next/layout/archive.swig</code> 文件中 <code>ARCHIVE BLOCK</code> 部分并修改：</p>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br></pre></td><td class="code"><pre><span class="line"><span class="operator">&lt;</span>div <span class="keyword">class</span>=&quot;<span class="title class_">post</span>-<span class="title class_">block</span>&quot;&gt;</span><br><span class="line">  &lt;<span class="title class_">div</span> <span class="title class_">class</span>=&quot;<span class="title class_">posts</span>-<span class="title class_">collapse</span>&quot;&gt;</span><br><span class="line">    &lt;<span class="title class_">div</span> <span class="title class_">class</span>=&quot;<span class="title class_">collection</span>-<span class="title class_">title</span>&quot;&gt;</span><br><span class="line">		&#123;<span class="operator">%================</span>下面这行site.posts.length<span class="operator">-</span><span class="number">1</span>即可<span class="operator">======================%</span>&#125;</span><br><span class="line">      &#123;<span class="operator">%-</span> <span class="keyword">set</span> posts_length <span class="operator">=</span> site.posts.length <span class="operator">-</span> <span class="number">1</span> <span class="operator">%</span>&#125;</span><br><span class="line">      &#123;<span class="operator">%-</span> <span class="keyword">if</span> posts_length <span class="operator">&gt;</span> <span class="number">210</span> <span class="operator">%</span>&#125;</span><br><span class="line">        &#123;<span class="operator">%-</span> <span class="keyword">set</span> cheers <span class="operator">=</span> &#x27;excellent&#x27; <span class="operator">%</span>&#125;</span><br><span class="line">      &#123;<span class="operator">%</span> elif posts_length <span class="operator">&gt;</span> <span class="number">130</span> <span class="operator">%</span>&#125;</span><br><span class="line">        &#123;<span class="operator">%-</span> <span class="keyword">set</span> cheers <span class="operator">=</span> &#x27;great&#x27; <span class="operator">%</span>&#125;</span><br><span class="line">      &#123;<span class="operator">%</span> elif posts_length <span class="operator">&gt;</span> <span class="number">80</span> <span class="operator">%</span>&#125;</span><br><span class="line">        &#123;<span class="operator">%-</span> <span class="keyword">set</span> cheers <span class="operator">=</span> &#x27;good&#x27; <span class="operator">%</span>&#125;</span><br><span class="line">      &#123;<span class="operator">%</span> elif posts_length <span class="operator">&gt;</span> <span class="number">50</span> <span class="operator">%</span>&#125;</span><br><span class="line">        &#123;<span class="operator">%-</span> <span class="keyword">set</span> cheers <span class="operator">=</span> &#x27;nice&#x27; <span class="operator">%</span>&#125;</span><br><span class="line">      &#123;<span class="operator">%</span> elif posts_length <span class="operator">&gt;</span> <span class="number">30</span> <span class="operator">%</span>&#125;</span><br><span class="line">        &#123;<span class="operator">%-</span> <span class="keyword">set</span> cheers <span class="operator">=</span> &#x27;ok&#x27; <span class="operator">%</span>&#125;</span><br><span class="line">      &#123;<span class="operator">%</span> <span class="keyword">else</span> <span class="operator">%</span>&#125;</span><br><span class="line">        &#123;<span class="operator">%-</span> <span class="keyword">set</span> cheers <span class="operator">=</span> &#x27;um&#x27; <span class="operator">%</span>&#125;</span><br><span class="line">      &#123;<span class="operator">%-</span> endif <span class="operator">%</span>&#125;</span><br><span class="line">       &#123;<span class="operator">%================</span>下面这行site.posts.length<span class="operator">-</span><span class="number">1</span>即可<span class="operator">======================%</span>&#125;</span><br><span class="line">      <span class="operator">&lt;</span>span <span class="keyword">class</span>=&quot;<span class="title class_">collection</span>-<span class="title class_">header</span>&quot;&gt;&#123;&#123; __(&#x27;cheers.&#x27; <span class="operator">+</span> cheers) &#125;&#125;<span class="operator">!</span> &#123;&#123; _p(&#x27;counter.archive_posts&#x27;, site.posts.length <span class="operator">-</span> <span class="number">1</span>) &#125;&#125; &#123;&#123; __(&#x27;keep_on&#x27;) &#125;&#125;<span class="operator">&lt;/</span>span<span class="operator">&gt;</span></span><br><span class="line">    <span class="operator">&lt;/</span>div<span class="operator">&gt;</span></span><br><span class="line"></span><br><span class="line">    &#123;&#123; post_template.render(page.posts) &#125;&#125;</span><br><span class="line"></span><br><span class="line">  <span class="operator">&lt;/</span>div<span class="operator">&gt;</span></span><br><span class="line"><span class="operator">&lt;/</span>div<span class="operator">&gt;</span></span><br></pre></td></tr></table></figure>

<p>同样，直接把 <code>site.posts.length</code> 减掉了 1 来去掉计数。</p>
<p>同样在<code>themes/next/layout/_macro/post-collapse.swig</code>文件中，找到循环遍历所有文章的部分进行修改：</p>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br></pre></td><td class="code"><pre><span class="line">&#123;<span class="operator">%</span> macro render(posts) <span class="operator">%</span>&#125;</span><br><span class="line">&#123;<span class="operator">%-</span> <span class="keyword">set</span> current_year <span class="operator">=</span> &#x27;<span class="number">1970</span>&#x27; <span class="operator">%</span>&#125;</span><br><span class="line">&#123;<span class="operator">%-</span> <span class="keyword">for</span> post <span class="keyword">in</span> posts.toArray() <span class="operator">%</span>&#125;</span><br><span class="line">&#123;<span class="operator">%=============</span>添加这一行<span class="operator">↓↓↓↓↓↓↓===============%</span>&#125;</span><br><span class="line">&#123;<span class="operator">%</span> <span class="keyword">if</span> not post.topic <span class="operator">%</span>&#125;</span><br><span class="line">&#123;<span class="operator">%=============</span>添加这一行<span class="operator">↑↑↑↑↑↑===============%</span>&#125;</span><br><span class="line">  &#123;<span class="operator">%-</span> <span class="keyword">set</span> year <span class="operator">=</span> date(post.date, &#x27;<span class="type">YYYY</span>&#x27;) <span class="operator">%</span>&#125;</span><br><span class="line"></span><br><span class="line">  &#123;<span class="operator">%-</span> <span class="keyword">if</span> year <span class="operator">!==</span> current_year <span class="operator">%</span>&#125;</span><br><span class="line">    &#123;<span class="operator">%-</span> <span class="keyword">set</span> current_year <span class="operator">=</span> year <span class="operator">%</span>&#125;</span><br><span class="line">    <span class="operator">&lt;</span>div <span class="keyword">class</span>=&quot;<span class="title class_">collection</span>-<span class="title class_">year</span>&quot;&gt;</span><br><span class="line">      &lt;<span class="title class_">span</span> <span class="title class_">class</span>=&quot;<span class="title class_">collection</span>-<span class="title class_">header</span>&quot;&gt;&#123;&#123; current_year &#125;&#125;<span class="operator">&lt;/</span>span<span class="operator">&gt;</span></span><br><span class="line">    <span class="operator">&lt;/</span>div<span class="operator">&gt;</span></span><br><span class="line">  &#123;<span class="operator">%-</span> endif <span class="operator">%</span>&#125;</span><br><span class="line"></span><br><span class="line">  <span class="operator">&lt;</span>article itemscope itemtype<span class="operator">=</span><span class="string">&quot;http://schema.org/Article&quot;</span><span class="operator">&gt;</span></span><br><span class="line">    <span class="operator">&lt;</span>header <span class="keyword">class</span>=&quot;<span class="title class_">post</span>-<span class="title class_">header</span>&quot;&gt;</span><br><span class="line"></span><br><span class="line">      &lt;<span class="title class_">div</span> <span class="title class_">class</span>=&quot;<span class="title class_">post</span>-<span class="title class_">meta</span>&quot;&gt;</span><br><span class="line">        &lt;<span class="title class_">time</span> <span class="title class_">itemprop</span>=&quot;<span class="title class_">dateCreated</span>&quot;</span><br><span class="line">              <span class="title class_">datetime</span>=&quot;&#123;&#123; moment(post.date).format() &#125;&#125;<span class="string">&quot;</span></span><br><span class="line"><span class="string">              content=&quot;</span>&#123;&#123; date(post.date, config.date_format) &#125;&#125;<span class="string">&quot;&gt;</span></span><br><span class="line"><span class="string">          &#123;&#123; date(post.date, &#x27;MM-DD&#x27;) &#125;&#125;</span></span><br><span class="line"><span class="string">        &lt;/time&gt;</span></span><br><span class="line"><span class="string">      &lt;/div&gt;</span></span><br><span class="line"><span class="string"></span></span><br><span class="line"><span class="string">      &lt;div class=&quot;</span>post<span class="operator">-</span>title<span class="string">&quot;&gt;</span></span><br><span class="line"><span class="string">        &#123;%- if post.link %&#125;&#123;# Link posts #&#125;</span></span><br><span class="line"><span class="string">          &#123;%- set postTitleIcon = &#x27;&lt;i class=&quot;</span>fa fa<span class="operator">-</span>external<span class="operator">-</span>link<span class="string">&quot;&gt;&lt;/i&gt;&#x27; %&#125;</span></span><br><span class="line"><span class="string">          &#123;%- set postText = post.title or post.link %&#125;</span></span><br><span class="line"><span class="string">          &#123;&#123; next_url(post.link, postText + postTitleIcon, &#123;class: &#x27;post-title-link post-title-link-external&#x27;, itemprop: &#x27;url&#x27;&#125;) &#125;&#125;</span></span><br><span class="line"><span class="string">        &#123;% else %&#125;</span></span><br><span class="line"><span class="string">          &lt;a class=&quot;</span>post<span class="operator">-</span>title<span class="operator">-</span>link<span class="string">&quot; href=&quot;</span>&#123;&#123; url_for(post.path) &#125;&#125;<span class="string">&quot; itemprop=&quot;</span>url<span class="string">&quot;&gt;</span></span><br><span class="line"><span class="string">            &lt;span itemprop=&quot;</span>name<span class="string">&quot;&gt;&#123;&#123; post.title or __(&#x27;post.untitled&#x27;) &#125;&#125;&lt;/span&gt;</span></span><br><span class="line"><span class="string">          &lt;/a&gt;</span></span><br><span class="line"><span class="string">        &#123;%- endif %&#125;</span></span><br><span class="line"><span class="string">      &lt;/div&gt;</span></span><br><span class="line"><span class="string">    &lt;/header&gt;</span></span><br><span class="line"><span class="string">  &lt;/article&gt;</span></span><br><span class="line"><span class="string">&#123;%=============添加这一行↓↓↓↓↓↓↓===============%&#125;</span></span><br><span class="line"><span class="string">&#123;% endif %&#125;</span></span><br><span class="line"><span class="string">&#123;%=============添加这一行↑↑↑↑↑↑===============%&#125;</span></span><br><span class="line"><span class="string">&#123;%- endfor %&#125;</span></span><br><span class="line"><span class="string">&#123;% endmacro %&#125;</span></span><br></pre></td></tr></table></figure>

<p>这样，只要我们在文章的头部信息栏内增加一个 <code>topic</code> 标签（可以自定义，只要代码里和文章里一致即可），就能从“归档”的列表中去掉这篇文章！同理，也可以增加任意约束，比如去掉日期早于某个时候的文章、去掉带有某个标签的文章等等，只要把约束全部加进 <code>if</code> 里去即可。不过这里的修改只影响到“归档”中的列表，博客首页的所有文章还是会显示的。</p>
<h2 id="一个-bug"><a href="#一个-bug" class="headerlink" title="一个 bug"></a>一个 bug</h2><p>因为我这是的归档页面，每 10 篇文章一页，当我的的日志为整数，比如 30 页的时候，本来只应该显示有 3 页文章，但是他会显示有 4 页，且第 4 页什么也没有。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200407182711.png" alt="bug"></p>
<p>目前，我还没有找解决办法【囧】</p>
<h2 id="参考资料"><a href="#参考资料" class="headerlink" title="参考资料"></a>参考资料</h2><p><a target="_blank" rel="noopener external nofollow noreferrer" href="https://www.liushulun.cn/post/blogdiy/blogdiy-7-top/">Hexo 博客 DIY：文章置顶和引用式文章</a></p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/540169c9.html">http://blog.mhy.loc.cc/archives/540169c9.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a><a class="post-meta__tags" href="/tags/Hexo/">Hexo</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/e3dc5cbb.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Hexo框架(一)：使用Hexo快速搭建个人博客</div></div></a></div><div class="next-post pull-right"><a href="/archives/264a3045.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Hexo框架(三)：Next主题配置及美化</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2022-02-10</div><div class="title">使用GitHub Actions 实现自动化部署和部署到服务器</div></div></a></div><div><a href="/archives/353666f0.html" title="给你的博客添加一个收藏页"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200719133158.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-19</div><div class="title">给你的博客添加一个收藏页</div></div></a></div><div><a href="/archives/f36eea83.html" title="Hexo博客添加emoji表情支持"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">Hexo博客添加emoji表情支持</div></div></a></div><div><a href="/archives/2798a2b1.html" title="给文章标题添加一个emoji表情"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">给文章标题添加一个emoji表情</div></div></a></div><div><a href="/archives/f06684a1.html" title="NexT版本更新V8.0记录"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-19</div><div class="title">NexT版本更新V8.0记录</div></div></a></div><div><a href="/archives/b1e33f9d.html" title="sitemeta渐变背景实现"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-03</div><div class="title">sitemeta渐变背景实现</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A"><span class="toc-text">前言：</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%96%87%E7%AB%A0%E7%BD%AE%E9%A1%B6"><span class="toc-text">文章置顶</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%85%AC%E5%91%8A%E6%95%88%E6%9E%9C"><span class="toc-text">公告效果</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%BB%8E%E4%BE%A7%E8%BE%B9%E6%A0%8F%E5%8E%BB%E6%8E%89%E6%96%87%E7%AB%A0%E8%AE%A1%E6%95%B0"><span class="toc-text">从侧边栏去掉文章计数</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%BB%8E%E2%80%98%E5%BD%92%E6%A1%A3%E2%80%99%E4%B8%AD%E5%8E%BB%E6%8E%89%E6%96%87%E7%AB%A0"><span class="toc-text">从‘归档’中去掉文章</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%B8%80%E4%B8%AA-bug"><span class="toc-text">一个 bug</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%8F%82%E8%80%83%E8%B5%84%E6%96%99"><span class="toc-text">参考资料</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>