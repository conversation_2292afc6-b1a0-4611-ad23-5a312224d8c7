script.
  (async () => {
    const showKatex = () => {
      document.querySelectorAll('#article-container .katex').forEach(el => el.classList.add('katex-show'))
    }

    if (!window.katex_js_css) {
      window.katex_js_css = true
      await btf.getCSS('!{url_for(theme.asset.katex)}')
      if (!{theme.math.katex.copy_tex}) {
        await btf.getScript('!{url_for(theme.asset.katex_copytex)}')
      }
    }

    showKatex()
  })()