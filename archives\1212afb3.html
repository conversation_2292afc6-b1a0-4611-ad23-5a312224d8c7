<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Node爬取数据到数据库实战代码笔记 | 你真是一个美好的人类</title><meta name="keywords" content="Node,Node爬虫,数据库,MySQL"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="Node爬取数据到数据库实战代码笔记">
<meta property="og:type" content="article">
<meta property="og:title" content="Node爬取数据到数据库实战代码笔记">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/1212afb3.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="Node爬取数据到数据库实战代码笔记">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png">
<meta property="article:published_time" content="2019-12-20T16:02:11.000Z">
<meta property="article:modified_time" content="2019-12-20T16:02:11.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="Node">
<meta property="article:tag" content="Node爬虫">
<meta property="article:tag" content="数据库">
<meta property="article:tag" content="MySQL">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/1212afb3"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Node爬取数据到数据库实战代码笔记',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: false,
  postUpdate: '2019-12-20 16:02:11'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Node爬取数据到数据库实战代码笔记</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2019-12-20T16:02:11.000Z" title="发表于 2019-12-20 16:02:11">2019-12-20</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2019-12-20T16:02:11.000Z" title="更新于 2019-12-20 16:02:11">2019-12-20</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/MySQL/">MySQL</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/Node/">Node</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/Node/Node%E7%88%AC%E8%99%AB/">Node爬虫</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><p>这个网站有反爬机制，使用 axios 请求直接返回 403，用 puppeteer 爬取也会很快就给你报超时，我就爬取了 80 多条数据，就请求不下来了。这里记录一下我的代码,基本和之前的代码差不多，只是多做了一些处理：</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br><span class="line">197</span><br><span class="line">198</span><br><span class="line">199</span><br><span class="line">200</span><br><span class="line">201</span><br><span class="line">202</span><br><span class="line">203</span><br><span class="line">204</span><br><span class="line">205</span><br><span class="line">206</span><br><span class="line">207</span><br><span class="line">208</span><br><span class="line">209</span><br><span class="line">210</span><br><span class="line">211</span><br><span class="line">212</span><br><span class="line">213</span><br><span class="line">214</span><br><span class="line">215</span><br><span class="line">216</span><br><span class="line">217</span><br><span class="line">218</span><br><span class="line">219</span><br><span class="line">220</span><br><span class="line">221</span><br><span class="line">222</span><br><span class="line">223</span><br><span class="line">224</span><br><span class="line">225</span><br><span class="line">226</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">let</span> puppeteer = <span class="built_in">require</span>(<span class="string">&#x27;puppeteer&#x27;</span>)</span><br><span class="line"><span class="keyword">let</span> url = <span class="built_in">require</span>(<span class="string">&#x27;url&#x27;</span>)</span><br><span class="line"><span class="keyword">let</span> mysql = <span class="built_in">require</span>(<span class="string">&#x27;mysql&#x27;</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment">// 目标：抓取https://sobooks.cc/网站下 所有电子书的书名和下载链接</span></span><br><span class="line"><span class="comment">// 实现思路：</span></span><br><span class="line"><span class="comment">// 1.进入网站，获取整个网站的列表页的页数</span></span><br><span class="line"><span class="comment">// 2.获取列表页所有的连接</span></span><br><span class="line"><span class="comment">// 3.进入每个电子书的详情页获取下载电子书的网盘地址</span></span><br><span class="line"><span class="comment">// 4.将获取的数据保存到book.txt</span></span><br><span class="line"></span><br><span class="line"><span class="comment">// 目标地址：</span></span><br><span class="line"><span class="keyword">let</span> httpUrl = <span class="string">&#x27;https://sobooks.cc/&#x27;</span></span><br><span class="line"><span class="keyword">let</span> sqlOptions = &#123;</span><br><span class="line">  <span class="attr">host</span>: <span class="string">&#x27;localhost&#x27;</span>,</span><br><span class="line">  <span class="attr">port</span>: <span class="string">&#x27;3306&#x27;</span>,</span><br><span class="line">  <span class="attr">user</span>: <span class="string">&#x27;root&#x27;</span>,</span><br><span class="line">  <span class="attr">password</span>: <span class="string">&#x27;123456&#x27;</span>,</span><br><span class="line">  <span class="attr">database</span>: <span class="string">&#x27;book&#x27;</span>,</span><br><span class="line">&#125;</span><br><span class="line"><span class="keyword">let</span> con = mysql.<span class="title function_">createConnection</span>(sqlOptions)</span><br><span class="line">con.<span class="title function_">connect</span>()</span><br><span class="line"><span class="comment">// 浏览器</span></span><br><span class="line">;(<span class="keyword">async</span> <span class="keyword">function</span> (<span class="params"></span>) &#123;</span><br><span class="line">  <span class="comment">// 配置options</span></span><br><span class="line">  <span class="keyword">let</span> debugOptions = &#123;</span><br><span class="line">    <span class="comment">// 设置视窗宽高</span></span><br><span class="line">    <span class="attr">defaultViewport</span>: &#123;</span><br><span class="line">      <span class="attr">width</span>: <span class="number">1400</span>,</span><br><span class="line">      <span class="attr">height</span>: <span class="number">720</span>,</span><br><span class="line">    &#125;,</span><br><span class="line">    <span class="comment">// 设置为有界面，无界面为true</span></span><br><span class="line">    <span class="attr">headless</span>: <span class="literal">false</span>,</span><br><span class="line">    <span class="comment">// 设置每个每个步骤的延迟时间</span></span><br><span class="line">    <span class="attr">slowMo</span>: <span class="number">300</span>,</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">let</span> options = &#123; <span class="attr">headless</span>: <span class="literal">true</span> &#125;</span><br><span class="line">  <span class="comment">// 启动浏览器</span></span><br><span class="line">  <span class="keyword">let</span> browser = <span class="keyword">await</span> puppeteer.<span class="title function_">launch</span>(options)</span><br><span class="line"></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">wait</span>(<span class="params">millSeconds</span>) &#123;</span><br><span class="line">    <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">Promise</span>(<span class="keyword">function</span> (<span class="params">resolve, reject</span>) &#123;</span><br><span class="line">      <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> &#123;</span><br><span class="line">        <span class="title function_">resolve</span>(<span class="string">&#x27;成功执行延迟函数，延迟时间：&#x27;</span> + millSeconds)</span><br><span class="line">      &#125;, millSeconds)</span><br><span class="line">    &#125;)</span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">getAllNum</span>(<span class="params">link</span>) &#123;</span><br><span class="line">    <span class="keyword">let</span> page = <span class="keyword">await</span> browser.<span class="title function_">newPage</span>()</span><br><span class="line">    <span class="keyword">await</span> page.<span class="title function_">goto</span>(link)</span><br><span class="line">    <span class="keyword">await</span> page.<span class="title function_">setRequestInterception</span>(<span class="literal">true</span>)</span><br><span class="line">    <span class="comment">// 监听请求事件，并对请求进行拦截</span></span><br><span class="line">    page.<span class="title function_">on</span>(<span class="string">&#x27;request&#x27;</span>, <span class="function">(<span class="params">interceptedRequest</span>) =&gt;</span> &#123;</span><br><span class="line">      <span class="comment">// 通过URL模块对请求的地址进行解析</span></span><br><span class="line">      <span class="keyword">let</span> urlObj = url.<span class="title function_">parse</span>(interceptedRequest.<span class="title function_">url</span>())</span><br><span class="line">      <span class="keyword">if</span> (urlObj.<span class="property">hostname</span> == <span class="string">&#x27;googleads.g.doubleclick.net&#x27;</span>) &#123;</span><br><span class="line">        <span class="comment">// 如果是谷歌的广告太慢了</span></span><br><span class="line">        interceptedRequest.<span class="title function_">abort</span>()</span><br><span class="line">      &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">        interceptedRequest.<span class="title function_">continue</span>()</span><br><span class="line">      &#125;</span><br><span class="line">    &#125;)</span><br><span class="line">    <span class="comment">// 获取总页数</span></span><br><span class="line">    <span class="keyword">let</span> pageNum = <span class="keyword">await</span> page.$eval(</span><br><span class="line">      <span class="string">&#x27;.pagination li:last-child span&#x27;</span>,</span><br><span class="line">      <span class="function">(<span class="params">element</span>) =&gt;</span> &#123;</span><br><span class="line">        <span class="keyword">let</span> text = element.<span class="property">innerHTML</span></span><br><span class="line">          .<span class="title function_">substring</span>(<span class="number">1</span>, element.<span class="property">innerHTML</span>.<span class="property">length</span> - <span class="number">2</span>)</span><br><span class="line">          .<span class="title function_">trim</span>()</span><br><span class="line">        <span class="keyword">return</span> text</span><br><span class="line">      &#125;</span><br><span class="line">    )</span><br><span class="line">    <span class="keyword">return</span> pageNum</span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 进入列表页</span></span><br><span class="line">  <span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">pageList</span>(<span class="params">num</span>) &#123;</span><br><span class="line">    <span class="keyword">let</span> pageListUrl = <span class="string">&#x27;https://sobooks.cc/page/&#x27;</span> + num</span><br><span class="line">    <span class="comment">// 打开一个新页面</span></span><br><span class="line">    <span class="keyword">let</span> page = <span class="keyword">await</span> browser.<span class="title function_">newPage</span>()</span><br><span class="line">    <span class="comment">// 在新页面中访问列表页地址</span></span><br><span class="line">    <span class="keyword">await</span> page.<span class="title function_">goto</span>(pageListUrl)</span><br><span class="line">    <span class="keyword">await</span> page.<span class="title function_">setRequestInterception</span>(<span class="literal">true</span>)</span><br><span class="line">    <span class="comment">// 监听请求事件，并对请求进行拦截</span></span><br><span class="line">    page.<span class="title function_">on</span>(<span class="string">&#x27;request&#x27;</span>, <span class="function">(<span class="params">interceptedRequest</span>) =&gt;</span> &#123;</span><br><span class="line">      <span class="comment">// 通过URL模块对请求的地址进行解析</span></span><br><span class="line">      <span class="keyword">let</span> urlObj = url.<span class="title function_">parse</span>(interceptedRequest.<span class="title function_">url</span>())</span><br><span class="line">      <span class="keyword">if</span> (urlObj.<span class="property">hostname</span> == <span class="string">&#x27;googleads.g.doubleclick.net&#x27;</span>) &#123;</span><br><span class="line">        <span class="comment">// 如果是谷歌的广告太慢了</span></span><br><span class="line">        interceptedRequest.<span class="title function_">abort</span>()</span><br><span class="line">      &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">        interceptedRequest.<span class="title function_">continue</span>()</span><br><span class="line">      &#125;</span><br><span class="line">    &#125;)</span><br><span class="line">    <span class="keyword">let</span> arrPage = <span class="keyword">await</span> page.$$eval(</span><br><span class="line">      <span class="string">&#x27;.card .card-item .thumb-img&gt;a&#x27;</span>,</span><br><span class="line">      <span class="function">(<span class="params">elements</span>) =&gt;</span> &#123;</span><br><span class="line">        <span class="keyword">let</span> arr = []</span><br><span class="line">        elements.<span class="title function_">forEach</span>(<span class="function">(<span class="params">element, i</span>) =&gt;</span> &#123;</span><br><span class="line">          <span class="keyword">var</span> obj = &#123;</span><br><span class="line">            <span class="attr">href</span>: element.<span class="title function_">getAttribute</span>(<span class="string">&#x27;href&#x27;</span>),</span><br><span class="line">            <span class="attr">title</span>: element.<span class="title function_">getAttribute</span>(<span class="string">&#x27;title&#x27;</span>),</span><br><span class="line">          &#125;</span><br><span class="line">          arr.<span class="title function_">push</span>(obj)</span><br><span class="line">        &#125;)</span><br><span class="line">        <span class="keyword">return</span> arr</span><br><span class="line">      &#125;</span><br><span class="line">    )</span><br><span class="line">    page.<span class="title function_">close</span>()</span><br><span class="line">    <span class="comment">// 通过获取的数组的地址和标题请求详情页</span></span><br><span class="line">    arrPage.<span class="title function_">forEach</span>(<span class="keyword">async</span> (pageObj, i) =&gt; &#123;</span><br><span class="line">      <span class="keyword">await</span> <span class="title function_">wait</span>(<span class="number">500</span> * i)</span><br><span class="line">      <span class="title function_">getPageInfo</span>(pageObj)</span><br><span class="line">    &#125;)</span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">getPageInfo</span>(<span class="params">pageObj</span>) &#123;</span><br><span class="line">    <span class="keyword">let</span> page = <span class="keyword">await</span> browser.<span class="title function_">newPage</span>()</span><br><span class="line">    <span class="keyword">await</span> page.<span class="title function_">goto</span>(pageObj.<span class="property">href</span>)</span><br><span class="line">    <span class="comment">// 截取谷歌请求</span></span><br><span class="line">    <span class="keyword">await</span> page.<span class="title function_">setRequestInterception</span>(<span class="literal">true</span>)</span><br><span class="line">    <span class="comment">// 监听请求事件，并对请求进行拦截</span></span><br><span class="line">    page.<span class="title function_">on</span>(<span class="string">&#x27;request&#x27;</span>, <span class="function">(<span class="params">interceptedRequest</span>) =&gt;</span> &#123;</span><br><span class="line">      <span class="comment">// 通过URL模块对请求的地址进行解析</span></span><br><span class="line">      <span class="keyword">let</span> urlObj = url.<span class="title function_">parse</span>(interceptedRequest.<span class="title function_">url</span>())</span><br><span class="line">      <span class="keyword">if</span> (urlObj.<span class="property">hostname</span> == <span class="string">&#x27;googleads.g.doubleclick.net&#x27;</span>) &#123;</span><br><span class="line">        <span class="comment">// 如果是谷歌的广告太慢了</span></span><br><span class="line">        interceptedRequest.<span class="title function_">abort</span>()</span><br><span class="line">      &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">        interceptedRequest.<span class="title function_">continue</span>()</span><br><span class="line">      &#125;</span><br><span class="line">    &#125;)</span><br><span class="line">    <span class="keyword">let</span> eleA = <span class="keyword">await</span> page.$(<span class="string">&#x27;.dltable tr:nth-child(3) a:last-child&#x27;</span>)</span><br><span class="line">    <span class="keyword">let</span> aHref = <span class="keyword">await</span> eleA.<span class="title function_">getProperty</span>(<span class="string">&#x27;href&#x27;</span>)</span><br><span class="line">    aHref = aHref.<span class="property">_remoteObject</span>.<span class="property">value</span></span><br><span class="line">    <span class="comment">// 书籍链接地址</span></span><br><span class="line">    <span class="keyword">let</span> bookUrl = pageObj.<span class="property">href</span></span><br><span class="line">    <span class="comment">// 书籍下载地址</span></span><br><span class="line">    <span class="keyword">let</span> download = aHref.<span class="title function_">split</span>(<span class="string">&#x27;?url=&#x27;</span>)[<span class="number">1</span>]</span><br><span class="line">    <span class="comment">// 书籍名字</span></span><br><span class="line">    <span class="keyword">let</span> bookname = pageObj.<span class="property">title</span></span><br><span class="line">    <span class="comment">// 分类</span></span><br><span class="line">    <span class="keyword">let</span> cataory = <span class="keyword">await</span> page.$eval(<span class="string">&#x27;.meta .muted:nth-child(1) a&#x27;</span>, <span class="function">(<span class="params">element</span>) =&gt;</span> &#123;</span><br><span class="line">      <span class="keyword">let</span> cataory = element.<span class="property">innerHTML</span></span><br><span class="line">      <span class="keyword">return</span> cataory</span><br><span class="line">    &#125;)</span><br><span class="line">    <span class="comment">// 作者</span></span><br><span class="line">    <span class="keyword">let</span> author = <span class="keyword">await</span> page.$eval(<span class="string">&#x27;.meta .muted:nth-child(2) a&#x27;</span>, <span class="function">(<span class="params">element</span>) =&gt;</span> &#123;</span><br><span class="line">      <span class="keyword">let</span> author = element.<span class="property">innerHTML</span></span><br><span class="line">      <span class="keyword">return</span> author</span><br><span class="line">    &#125;)</span><br><span class="line">    <span class="comment">// 书籍图片</span></span><br><span class="line">    <span class="keyword">let</span> bookimg = <span class="keyword">await</span> page.$eval(</span><br><span class="line">      <span class="string">&#x27;.article-content .bookpic img&#x27;</span>,</span><br><span class="line">      <span class="function">(<span class="params">element</span>) =&gt;</span> &#123;</span><br><span class="line">        <span class="keyword">let</span> bookimg = element.<span class="title function_">getAttribute</span>(<span class="string">&#x27;src&#x27;</span>)</span><br><span class="line">        <span class="keyword">return</span> bookimg</span><br><span class="line">      &#125;</span><br><span class="line">    )</span><br><span class="line">    <span class="comment">// 浏览次数</span></span><br><span class="line">    <span class="keyword">let</span> viewcount = <span class="keyword">await</span> page.$eval(</span><br><span class="line">      <span class="string">&#x27;.article-content .bookinfo li:nth-child(3)&#x27;</span>,</span><br><span class="line">      <span class="function">(<span class="params">element</span>) =&gt;</span> &#123;</span><br><span class="line">        <span class="keyword">let</span> viewcount = element.<span class="property">innerHTML</span></span><br><span class="line">        <span class="keyword">let</span> reg = <span class="regexp">/&lt;strong&gt;浏览：&lt;\/strong&gt;(.*?)次/gi</span>s</span><br><span class="line">        viewcount = reg.<span class="title function_">exec</span>(viewcount)[<span class="number">1</span>]</span><br><span class="line">        <span class="keyword">return</span> viewcount</span><br><span class="line">      &#125;</span><br><span class="line">    )</span><br><span class="line">    <span class="comment">// 标签</span></span><br><span class="line">    <span class="keyword">let</span> tag = <span class="keyword">await</span> page.$$eval(</span><br><span class="line">      <span class="string">&#x27;.article-content .bookinfo li:nth-child(4) a&#x27;</span>,</span><br><span class="line">      <span class="function">(<span class="params">elements</span>) =&gt;</span> &#123;</span><br><span class="line">        <span class="keyword">let</span> arr = []</span><br><span class="line">        elements.<span class="title function_">forEach</span>(<span class="function">(<span class="params">element, i</span>) =&gt;</span> &#123;</span><br><span class="line">          <span class="keyword">let</span> tag = element.<span class="property">innerHTML</span></span><br><span class="line">          arr.<span class="title function_">push</span>(tag)</span><br><span class="line">        &#125;)</span><br><span class="line">        <span class="keyword">return</span> arr</span><br><span class="line">      &#125;</span><br><span class="line">    )</span><br><span class="line">    <span class="comment">// 时间</span></span><br><span class="line">    <span class="keyword">let</span> pubtime = <span class="keyword">await</span> page.$eval(</span><br><span class="line">      <span class="string">&#x27;.article-content .bookinfo li:nth-child(5)&#x27;</span>,</span><br><span class="line">      <span class="function">(<span class="params">element</span>) =&gt;</span> &#123;</span><br><span class="line">        <span class="keyword">let</span> pubtime = element.<span class="property">innerHTML</span>.<span class="title function_">split</span>(<span class="string">&#x27;&lt;strong&gt;时间：&lt;/strong&gt;&#x27;</span>)[<span class="number">1</span>]</span><br><span class="line">        <span class="keyword">return</span> pubtime</span><br><span class="line">      &#125;</span><br><span class="line">    )</span><br><span class="line">    <span class="comment">// 简介</span></span><br><span class="line">    <span class="keyword">let</span> brief = <span class="keyword">await</span> page.$eval(<span class="string">&#x27;.article-content&gt;p&#x27;</span>, <span class="function">(<span class="params">element</span>) =&gt;</span> &#123;</span><br><span class="line">      <span class="keyword">let</span> brief = element.<span class="property">innerHTML</span></span><br><span class="line">      <span class="keyword">return</span> brief</span><br><span class="line">    &#125;)</span><br><span class="line">    <span class="keyword">let</span> infoList = [</span><br><span class="line">      bookname,</span><br><span class="line">      author,</span><br><span class="line">      viewcount,</span><br><span class="line">      <span class="string">`<span class="subst">$&#123;tag&#125;</span>`</span>,</span><br><span class="line">      pubtime,</span><br><span class="line">      bookimg,</span><br><span class="line">      cataory,</span><br><span class="line">      brief,</span><br><span class="line">      bookUrl,</span><br><span class="line">      download,</span><br><span class="line">    ]</span><br><span class="line">    <span class="comment">// 插入数据库</span></span><br><span class="line">    <span class="keyword">let</span> strSql =</span><br><span class="line">      <span class="string">&#x27;insert into book (bookname,author,viewcount,tag,pubtime,bookimg,cataory,brief,bookUrl,download) values (?,?,?,?,?,?,?,?,?,?)&#x27;</span></span><br><span class="line">    con.<span class="title function_">query</span>(strSql, infoList, <span class="function">(<span class="params">err, res</span>) =&gt;</span> &#123;</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(err)</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(res)</span><br><span class="line">    &#125;)</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">spider</span>(<span class="params">link</span>) &#123;</span><br><span class="line">    <span class="keyword">let</span> allPageNum = <span class="keyword">await</span> <span class="title function_">getAllNum</span>(link)</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">&#x27;成功获取页面总数：&#x27;</span> + allPageNum)</span><br><span class="line"></span><br><span class="line">    <span class="keyword">for</span> (<span class="keyword">let</span> i = <span class="number">1</span>; i &lt;= allPageNum; i++) &#123;</span><br><span class="line">      <span class="keyword">await</span> <span class="title function_">wait</span>(<span class="number">4000</span> * i) <span class="comment">// 每个页面延迟3秒</span></span><br><span class="line">      <span class="title function_">pageList</span>(i)</span><br><span class="line">    &#125;</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="title function_">spider</span>(httpUrl)</span><br><span class="line">&#125;)()</span><br></pre></td></tr></table></figure>

<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200427181748.png" alt="b2dd5cb1d44b978a4ddb35e1556f4df"></p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/1212afb3.html">http://blog.mhy.loc.cc/archives/1212afb3.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/Node/">Node</a><a class="post-meta__tags" href="/tags/Node%E7%88%AC%E8%99%AB/">Node爬虫</a><a class="post-meta__tags" href="/tags/%E6%95%B0%E6%8D%AE%E5%BA%93/">数据库</a><a class="post-meta__tags" href="/tags/MySQL/">MySQL</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/1567847a.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Node爬取数据到数据库练习</div></div></a></div><div class="next-post pull-right"><a href="/archives/a0f8eb24.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">cheerio</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/1567847a.html" title="Node爬取数据到数据库练习"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-23</div><div class="title">Node爬取数据到数据库练习</div></div></a></div><div><a href="/archives/7e878b19.html" title="爬虫总结笔记"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-01-02</div><div class="title">爬虫总结笔记</div></div></a></div><div><a href="/archives/b07ae32c.html" title="Node爬虫实战"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-26</div><div class="title">Node爬虫实战</div></div></a></div><div><a href="/archives/44b2b83c.html" title="Node学习总结笔记"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184621.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-13</div><div class="title">Node学习总结笔记</div></div></a></div><div><a href="/archives/a26302a1.html" title="Node原生fs模块的promise封装"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184621.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-11-20</div><div class="title">Node原生fs模块的promise封装</div></div></a></div><div><a href="/archives/628e35f6.html" title="Node连接MySQL数据库"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185454.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-08-30</div><div class="title">Node连接MySQL数据库</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>