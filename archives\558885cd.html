<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化 | 你真是一个美好的人类</title><meta name="keywords" content="html2canvas，HTML页面生成为图片，下载，图片加载不全，文本丢失"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="在Vue中基于html2canvas和Canvas2Image实现HTML页面生成为图片并下载的功能，并解决了一些文本丢失或者图片加载不全的问题，跨域配置，以及清晰度优化等等。">
<meta property="og:type" content="article">
<meta property="og:title" content="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/558885cd.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="在Vue中基于html2canvas和Canvas2Image实现HTML页面生成为图片并下载的功能，并解决了一些文本丢失或者图片加载不全的问题，跨域配置，以及清晰度优化等等。">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png">
<meta property="article:published_time" content="2020-11-13T11:36:33.000Z">
<meta property="article:modified_time" content="2020-11-13T11:36:33.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="CSS">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/558885cd"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-11-13 11:36:33'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-11-13T11:36:33.000Z" title="更新于 2020-11-13 11:36:33">2020-11-13</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/">前端</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/%E5%89%8D%E7%AB%AF%E7%AC%94%E8%AE%B0/">前端笔记</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><h2 id="前言"><a href="#前言" class="headerlink" title="前言"></a>前言</h2><p>最近遇到这样一个需求：<br><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112203423.png" alt="需求"></p>
<p>点击下载海报的时候，生成一张海报，并且可以下载。经过一番摸索和踩坑，终于实现了这个功能。再这里记录一下！</p>
<h2 id="代码结构"><a href="#代码结构" class="headerlink" title="代码结构"></a>代码结构</h2><h3 id="HTML"><a href="#HTML" class="headerlink" title="HTML"></a>HTML</h3><figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br></pre></td><td class="code"><pre><span class="line">&lt;div</span><br><span class="line">  id=&quot;poster-pic&quot;</span><br><span class="line">&gt;</span><br><span class="line">  &lt;div class=&quot;poster-pic-title&quot;&gt;&lt;/div&gt;</span><br><span class="line">  &lt;img</span><br><span class="line">    class=&quot;goods-img&quot;</span><br><span class="line">    :src=&quot;picUrl&quot;</span><br><span class="line">    alt=&quot;加载失败&quot;</span><br><span class="line">  &gt;</span><br><span class="line">  &lt;div class=&quot;poster-pic-subtitle&quot;&gt;</span><br><span class="line">    活动名字</span><br><span class="line">  &lt;/div&gt;</span><br><span class="line">  &lt;div class=&quot;poster-pic-footer&quot;&gt;</span><br><span class="line">    &lt;div class=&quot;left&quot;&gt;</span><br><span class="line">      &lt;div class=&quot;top&quot;&gt;</span><br><span class="line">        &lt;span&gt;$999&lt;/span&gt;</span><br><span class="line">        &lt;span&gt;$999&lt;/span&gt;</span><br><span class="line">      &lt;/div&gt;</span><br><span class="line">      &lt;div class=&quot;buttom&quot;&gt;</span><br><span class="line">        &lt;div&gt;活动时间&lt;/div&gt;</span><br><span class="line">        &lt;div&gt;2020.11.11-2020.11.11&lt;/div&gt;</span><br><span class="line">      &lt;/div&gt;</span><br><span class="line">    &lt;/div&gt;</span><br><span class="line">    &lt;div class=&quot;right&quot;&gt;</span><br><span class="line">      &lt;img</span><br><span class="line">        class=&quot;code-img&quot;</span><br><span class="line">        :src=&quot;qrcodeUrl&quot;</span><br><span class="line">        alt=&quot;加载失败&quot;</span><br><span class="line">      &gt;</span><br><span class="line">    &lt;/div&gt;</span><br><span class="line">  &lt;/div&gt;</span><br><span class="line">&lt;/div&gt;</span><br><span class="line">&lt;span</span><br><span class="line">  slot=&quot;footer&quot;</span><br><span class="line">  class=&quot;dialog-footer&quot;</span><br><span class="line">&gt;</span><br><span class="line">  &lt;el-button @click=&quot;download&quot;&gt;下载二维码&lt;/el-button&gt;</span><br><span class="line">  &lt;el-button</span><br><span class="line">    type=&quot;primary&quot;</span><br><span class="line">    @click=&quot;downloadPosters&quot;</span><br><span class="line">  &gt;下载海报&lt;/el-button&gt;</span><br><span class="line">&lt;/span&gt;</span><br></pre></td></tr></table></figure>

<p>页面大致就这样，其实页面怎么样并不重要，主要是在JS实现。</p>
<h2 id="实现HTML页面保存为图片"><a href="#实现HTML页面保存为图片" class="headerlink" title="实现HTML页面保存为图片"></a>实现HTML页面保存为图片</h2><h3 id="html2canvas的用法"><a href="#html2canvas的用法" class="headerlink" title="html2canvas的用法"></a>html2canvas的用法</h3><p><a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/niklasvh/html2canvas">GitHub-Html2Canvas</a></p>
<h4 id="实现保存为图片的第一步：HTML转换为Canvas"><a href="#实现保存为图片的第一步：HTML转换为Canvas" class="headerlink" title="实现保存为图片的第一步：HTML转换为Canvas"></a>实现保存为图片的第一步：HTML转换为Canvas</h4><p>基于 <code>html2canvas</code> 可将一个元素渲染为 Canvas, 只需要简单的调用 <code>html2canvas(element[, options])</code> 即可，然后返回一个含有 <code>&lt;canvas&gt;</code> 元素的 <code>promise</code>.</p>
<figure class="highlight js"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="title function_">html2canvas</span>(<span class="variable language_">document</span>.<span class="property">body</span>.<span class="title function_">then</span>((canvas)=&#123;</span><br><span class="line">  <span class="variable language_">document</span>.<span class="property">body</span>.<span class="title function_">appendChild</span>(canvas)</span><br><span class="line">&#125;))</span><br></pre></td></tr></table></figure>

<h3 id="实现保存为图片的第二步：Canvas转Image"><a href="#实现保存为图片的第二步：Canvas转Image" class="headerlink" title="实现保存为图片的第二步：Canvas转Image"></a>实现保存为图片的第二步：Canvas转Image</h3><p>上一步生成的 <code>Canvas</code> 即为包含目标元素的 <code>&lt;canvas&gt;</code> 元素对象，实现保存图片的目标只需要将 <code>canvas</code> 转 <code>image</code> 即可。我们选择使用 <code>Canvas2Image.js</code> 进行转换，但它实际上也只是 <code>canvas.toDataUrl</code> 的一个封装。</p>
<p><a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/hongru/canvas2image">GitHub-Canvas2Image</a></p>
<p><strong>注意事项！！重点：</strong></p>
<ul>
<li>这个包请不要使用 <code>npm</code> 的方式引入，npm包没有更新，存在很多bug，包括模块未导出，下载图片没有后缀和名字的问题！</li>
<li>请直接下载压缩包，把源代码文件放入项目中，导入使用！</li>
</ul>
<h2 id="生成图片清晰度优化"><a href="#生成图片清晰度优化" class="headerlink" title="生成图片清晰度优化"></a>生成图片清晰度优化</h2><h3 id="第一步"><a href="#第一步" class="headerlink" title="第一步"></a>第一步</h3><p>最终图片的清晰度 取决于 上面第一步中 <code>html</code> 转换 <code>Canvas</code> 的清晰度。</p>
<p>这里提高清晰度的基本原理是将 <code>canvas</code> 的属性 <code>width</code> 和 <code>height</code> 属性放大，最后将 <code>canvas</code> 的 CSS样式 <code>width</code> 和 <code>height</code> 设置为原来的1倍大小。</p>
<figure class="highlight js"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br></pre></td><td class="code"><pre><span class="line">downloadImg (params) &#123;</span><br><span class="line">  <span class="keyword">let</span> cntElem = <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(params)</span><br><span class="line">  <span class="keyword">let</span> shareContent = cntElem</span><br><span class="line">  <span class="keyword">let</span> width = shareContent.<span class="property">offsetWidth</span></span><br><span class="line">  <span class="keyword">let</span> height = shareContent.<span class="property">offsetHeight</span></span><br><span class="line">  <span class="keyword">let</span> canvas = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">&#x27;canvas&#x27;</span>)</span><br><span class="line">  <span class="keyword">var</span> scale = <span class="number">2</span></span><br><span class="line">  canvas.<span class="property">width</span> = width * scale</span><br><span class="line">  canvas.<span class="property">height</span> = height * scale</span><br><span class="line">  canvas.<span class="title function_">getContext</span>(<span class="string">&#x27;2d&#x27;</span>).<span class="title function_">scale</span>(scale, scale)</span><br><span class="line">  <span class="keyword">const</span> ops = &#123;</span><br><span class="line">    <span class="attr">scale</span>: scale,</span><br><span class="line">    <span class="attr">width</span>: width,</span><br><span class="line">    <span class="attr">height</span>: height,</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="title function_">html2canvas</span>(shareContent, ops).<span class="title function_">then</span>(<span class="function"><span class="params">canvas</span> =&gt;</span> &#123;</span><br><span class="line">    <span class="keyword">let</span> context = canvas.<span class="title function_">getContext</span>(<span class="string">&#x27;2d&#x27;</span>)</span><br><span class="line">    <span class="keyword">let</span> img <span class="title class_">Canvas2Image</span>.<span class="title function_">converToImage</span>(canvas, canvas.<span class="property">width</span>, canvas.<span class="property">height</span>)</span><br><span class="line">    <span class="variable language_">document</span>.<span class="property">body</span>.<span class="title function_">appendChild</span>(imgs)</span><br><span class="line">  &#125;)</span><br><span class="line">&#125;,</span><br><span class="line"></span><br></pre></td></tr></table></figure>

<h3 id="第二步"><a href="#第二步" class="headerlink" title="第二步"></a>第二步</h3><p>在第一步我们就可以解决通常情况下图片不清晰的问题，但是在我们的实际项目中，可能仍然存在一些十分尴尬的问题，比如大果粒一般的渲染效果。</p>
<p>这里我们采用一些优化策略：</p>
<ul>
<li>更改 <code>百分比布局</code> 为 <code>px</code> 布局</li>
<li>关闭 Canvas 默认的 抗锯齿 设置</li>
<li>设置模糊元素的 <code>width</code> 和 <code>height</code> 为素材的原有宽高，通过 <code>scale</code> 缩放。</li>
</ul>
<p>基本原理：</p>
<ul>
<li>设置 <code>px</code> 为单位，避免样式二次计算导致的模糊</li>
<li>默认情况下，Canvas 的抗锯齿是开启的，需要手动关闭来实现图像的锐化</li>
</ul>
<figure class="highlight js"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br></pre></td><td class="code"><pre><span class="line">downloadImg (params) &#123;</span><br><span class="line">  <span class="keyword">let</span> cntElem = <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(params)</span><br><span class="line">  <span class="keyword">let</span> shareContent = cntElem</span><br><span class="line">  <span class="keyword">let</span> width = shareContent.<span class="property">offsetWidth</span></span><br><span class="line">  <span class="keyword">let</span> height = shareContent.<span class="property">offsetHeight</span></span><br><span class="line">  <span class="keyword">let</span> canvas = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">&#x27;canvas&#x27;</span>)</span><br><span class="line">  <span class="keyword">var</span> scale = <span class="number">2</span></span><br><span class="line">  canvas.<span class="property">width</span> = width * scale</span><br><span class="line">  canvas.<span class="property">height</span> = height * scale</span><br><span class="line">  canvas.<span class="title function_">getContext</span>(<span class="string">&#x27;2d&#x27;</span>).<span class="title function_">scale</span>(scale, scale)</span><br><span class="line">  <span class="keyword">const</span> opts = &#123;</span><br><span class="line">    <span class="attr">scale</span>: scale,</span><br><span class="line">    <span class="attr">width</span>: width,</span><br><span class="line">    <span class="attr">height</span>: height,</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="title function_">html2canvas</span>(shareContent, opts).<span class="title function_">then</span>(<span class="function"><span class="params">canvas</span> =&gt;</span> &#123;</span><br><span class="line">    <span class="keyword">let</span> context = canvas.<span class="title function_">getContext</span>(<span class="string">&#x27;2d&#x27;</span>)</span><br><span class="line">    <span class="comment">// 重点：关闭抗锯齿</span></span><br><span class="line">    context.<span class="property">mozImageSmoothingEnabled</span> = <span class="literal">false</span></span><br><span class="line">    context.<span class="property">webkitImageSmoothingEnabled</span> = <span class="literal">false</span></span><br><span class="line">    context.<span class="property">msImageSmoothingEnabled</span> = <span class="literal">false</span></span><br><span class="line">    context.<span class="property">ImgSmoothingEnabled</span> = <span class="literal">false</span></span><br><span class="line">  &#125;)</span><br><span class="line">&#125;,</span><br></pre></td></tr></table></figure>

<h2 id="含有跨域图片的配置（部分图片丢失的问题解决）"><a href="#含有跨域图片的配置（部分图片丢失的问题解决）" class="headerlink" title="含有跨域图片的配置（部分图片丢失的问题解决）"></a>含有跨域图片的配置（部分图片丢失的问题解决）</h2><p>由于Canvas对于图片资源的同源限制，如果画布中包含跨域的图片资源会污染画布，造成生成图片样式混乱或者 <code>html2canvas</code> 方法不执行等问题。</p>
<p>我们需要在 配置中 开启跨域和禁止污染即可：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line">const opts = &#123;</span><br><span class="line">  scale: scale,</span><br><span class="line">  width: width,</span><br><span class="line">  height: height,</span><br><span class="line">  useCORS: true, // 使用跨域</span><br><span class="line">  allowTaint: true, // 允许使用跨域资源</span><br><span class="line">  tainTest: false,</span><br><span class="line">&#125;</span><br><span class="line">html2canvs(element,opts)</span><br></pre></td></tr></table></figure>

<h2 id="其他问题"><a href="#其他问题" class="headerlink" title="其他问题"></a>其他问题</h2><h3 id="文本丢失"><a href="#文本丢失" class="headerlink" title="文本丢失"></a>文本丢失</h3><p>可能使用了 <code>html2canvas</code>  不支持的CSS样式, 例如：<code>display: -webkit-box</code></p>
<h2 id="完整代码-流程演示"><a href="#完整代码-流程演示" class="headerlink" title="完整代码/流程演示"></a>完整代码/流程演示</h2><ul>
<li>安装 <code>html2canvas</code></li>
</ul>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">yarn add html2canvas</span><br></pre></td></tr></table></figure>

<p>也可以直接下载 源文件 再进行导入。</p>
<ul>
<li>下载 <code>canvas2image.js</code></li>
</ul>
<p>在git仓库下载源文件后，进行导入，我这边是挂载到了全局方法</p>
<p><code>utils/index</code></p>
<figure class="highlight js"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> <span class="title class_">Canvas2Image</span> <span class="keyword">from</span> <span class="string">&#x27;./canvas2image.js&#x27;</span></span><br><span class="line"><span class="comment">// ………………</span></span><br><span class="line"><span class="keyword">export</span> <span class="keyword">default</span> &#123;</span><br><span class="line">  <span class="comment">// …………………………</span></span><br><span class="line">  <span class="title class_">Canvas2Image</span></span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p><code>mani.js</code></p>
<figure class="highlight js"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">import</span> util <span class="keyword">from</span> <span class="string">&#x27;@/utils/index.js&#x27;</span></span><br><span class="line"><span class="title class_">Vue</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">util</span> = util</span><br></pre></td></tr></table></figure>

<ul>
<li>JS代码</li>
</ul>
<figure class="highlight js"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// npm 安装直接导入</span></span><br><span class="line"><span class="keyword">import</span> html2canvas <span class="keyword">from</span> <span class="string">&#x27;html2canvas&#x27;</span></span><br><span class="line"></span><br><span class="line"><span class="comment">/*</span></span><br><span class="line"><span class="comment">@params: dom节点-class/id</span></span><br><span class="line"><span class="comment">@name：下载之后的名字</span></span><br><span class="line"><span class="comment">*/</span></span><br><span class="line">downloadImg (params, name) &#123;</span><br><span class="line">  <span class="keyword">let</span> cntElem = <span class="variable language_">document</span>.<span class="title function_">querySelector</span>(params)</span><br><span class="line">  <span class="keyword">let</span> shareContent = cntElem</span><br><span class="line">  <span class="keyword">let</span> width = shareContent.<span class="property">offsetWidth</span></span><br><span class="line">  <span class="keyword">let</span> height = shareContent.<span class="property">offsetHeight</span></span><br><span class="line">  <span class="keyword">let</span> canvas = <span class="variable language_">document</span>.<span class="title function_">createElement</span>(<span class="string">&#x27;canvas&#x27;</span>)</span><br><span class="line">  <span class="keyword">var</span> scale = <span class="number">2</span></span><br><span class="line">  canvas.<span class="property">width</span> = width * scale</span><br><span class="line">  canvas.<span class="property">height</span> = height * scale</span><br><span class="line">  canvas.<span class="title function_">getContext</span>(<span class="string">&#x27;2d&#x27;</span>).<span class="title function_">scale</span>(scale, scale)</span><br><span class="line">  <span class="keyword">const</span> ops = &#123;</span><br><span class="line">    <span class="attr">scale</span>: scale,</span><br><span class="line">    <span class="attr">width</span>: width,</span><br><span class="line">    <span class="attr">height</span>: height,</span><br><span class="line">    <span class="comment">// 跨域配置</span></span><br><span class="line">    <span class="attr">useCORS</span>: <span class="literal">true</span>, <span class="comment">// 使用跨域</span></span><br><span class="line">    <span class="attr">allowTaint</span>: <span class="literal">true</span>, <span class="comment">// 允许使用跨域资源</span></span><br><span class="line">    <span class="attr">tainTest</span>: <span class="literal">false</span>,</span><br><span class="line">    <span class="attr">backgroundColor</span>: <span class="literal">null</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="title function_">html2canvas</span>(shareContent, ops).<span class="title function_">then</span>(<span class="function"><span class="params">canvas</span> =&gt;</span> &#123;</span><br><span class="line">    <span class="keyword">let</span> context = canvas.<span class="title function_">getContext</span>(<span class="string">&#x27;2d&#x27;</span>)</span><br><span class="line">    context.<span class="property">mozImageSmoothingEnabled</span> = <span class="literal">false</span></span><br><span class="line">    context.<span class="property">webkitImageSmoothingEnabled</span> = <span class="literal">false</span></span><br><span class="line">    context.<span class="property">msImageSmoothingEnabled</span> = <span class="literal">false</span></span><br><span class="line">    context.<span class="property">ImgSmoothingEnabled</span> = <span class="literal">false</span></span><br><span class="line">    <span class="comment">// 保存图片，可以保存PNG，JPEG等，调用对应api即可</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">util</span>.<span class="property">Canvas2Image</span>.<span class="title function_">saveAsPNG</span>(canvas, canvas.<span class="property">width</span>, canvas.<span class="property">height</span>, name)</span><br><span class="line">  &#125;)</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 调用方法</span></span><br><span class="line">downloadCode () &#123;</span><br><span class="line">  <span class="variable language_">this</span>.<span class="title function_">downloadImg</span>(<span class="string">&#x27;.code-img&#x27;</span>, <span class="string">&#x27;二维码&#x27;</span>)</span><br><span class="line">&#125;,</span><br><span class="line">downloadPost () &#123;</span><br><span class="line">  <span class="variable language_">this</span>.<span class="title function_">downloadImg</span>(<span class="string">&#x27;#poster-pic&#x27;</span>, <span class="string">&#x27;海报&#x27;</span>)</span><br><span class="line">&#125;,</span><br></pre></td></tr></table></figure>

</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/558885cd.html">http://blog.mhy.loc.cc/archives/558885cd.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/CSS/">CSS</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/9ac5350.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">img标签访问图片返回403 forbidden的解决方法</div></div></a></div><div class="next-post pull-right"><a href="/archives/59df034f.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">那些你总是要用却又死活记不住的css属性</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-11-12</div><div class="title">那些你总是要用却又死活记不住的css属性</div></div></a></div><div><a href="/archives/b47882fe.html" title="CSS修改滚动条样式"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718125548.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-05-26</div><div class="title">CSS修改滚动条样式</div></div></a></div><div><a href="/archives/4df97024.html" title="什么是BFC、IFC、GFC、FFC"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718125548.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-05-22</div><div class="title">什么是BFC、IFC、GFC、FFC</div></div></a></div><div><a href="/archives/ae14df56.html" title="BFC及其应用"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718125548.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-05-18</div><div class="title">BFC及其应用</div></div></a></div><div><a href="/archives/40a53b67.html" title="div水平垂直居中的方法"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718125548.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-05-10</div><div class="title">div水平垂直居中的方法</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%89%8D%E8%A8%80"><span class="toc-text">前言</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%BB%93%E6%9E%84"><span class="toc-text">代码结构</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#HTML"><span class="toc-text">HTML</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%AE%9E%E7%8E%B0HTML%E9%A1%B5%E9%9D%A2%E4%BF%9D%E5%AD%98%E4%B8%BA%E5%9B%BE%E7%89%87"><span class="toc-text">实现HTML页面保存为图片</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#html2canvas%E7%9A%84%E7%94%A8%E6%B3%95"><span class="toc-text">html2canvas的用法</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%AE%9E%E7%8E%B0%E4%BF%9D%E5%AD%98%E4%B8%BA%E5%9B%BE%E7%89%87%E7%9A%84%E7%AC%AC%E4%B8%80%E6%AD%A5%EF%BC%9AHTML%E8%BD%AC%E6%8D%A2%E4%B8%BACanvas"><span class="toc-text">实现保存为图片的第一步：HTML转换为Canvas</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AE%9E%E7%8E%B0%E4%BF%9D%E5%AD%98%E4%B8%BA%E5%9B%BE%E7%89%87%E7%9A%84%E7%AC%AC%E4%BA%8C%E6%AD%A5%EF%BC%9ACanvas%E8%BD%ACImage"><span class="toc-text">实现保存为图片的第二步：Canvas转Image</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%94%9F%E6%88%90%E5%9B%BE%E7%89%87%E6%B8%85%E6%99%B0%E5%BA%A6%E4%BC%98%E5%8C%96"><span class="toc-text">生成图片清晰度优化</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%AC%AC%E4%B8%80%E6%AD%A5"><span class="toc-text">第一步</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%AC%AC%E4%BA%8C%E6%AD%A5"><span class="toc-text">第二步</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%90%AB%E6%9C%89%E8%B7%A8%E5%9F%9F%E5%9B%BE%E7%89%87%E7%9A%84%E9%85%8D%E7%BD%AE%EF%BC%88%E9%83%A8%E5%88%86%E5%9B%BE%E7%89%87%E4%B8%A2%E5%A4%B1%E7%9A%84%E9%97%AE%E9%A2%98%E8%A7%A3%E5%86%B3%EF%BC%89"><span class="toc-text">含有跨域图片的配置（部分图片丢失的问题解决）</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%85%B6%E4%BB%96%E9%97%AE%E9%A2%98"><span class="toc-text">其他问题</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%96%87%E6%9C%AC%E4%B8%A2%E5%A4%B1"><span class="toc-text">文本丢失</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%AE%8C%E6%95%B4%E4%BB%A3%E7%A0%81-%E6%B5%81%E7%A8%8B%E6%BC%94%E7%A4%BA"><span class="toc-text">完整代码&#x2F;流程演示</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>