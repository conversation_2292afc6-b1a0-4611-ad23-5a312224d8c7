#article-container
  .shuoshuo-item
    @extend .cardHover
    margin-bottom: 20px
    padding: 35px 30px 30px

    +maxWidth768()
      padding: 25px 20px 20px

  .shuoshuo-item-header
    display: flex
    align-items: center
    cursor: default

  .shuoshuo-avatar
    overflow: hidden
    width: 40px
    height: 40px
    border-radius: 40px

    img
      margin: 0
      width: 100%
      height: 100%

  .shuoshuo-info
    margin-left: 10px
    line-height: 1.5

  .shuoshuo-date
    color: #858585
    font-size: .8em

  .shuoshuo-content
    padding: 15px 0 10px

    & > *:last-child
      margin-bottom: 0

  .shuoshuo-footer
    display: flex
    align-items: center

    &.flex-between
      justify-content: space-between

    &.flex-end
      justify-content: flex-end

    .shuoshuo-tag
      display: inline-block
      margin-right: 8px
      padding: 0 8px
      width: fit-content
      border: 1px solid $light-blue
      border-radius: 12px
      color: $light-blue
      font-size: .85em
      cursor: default
      transition: all .2s ease-in-out

      &:hover
        background: $light-blue
        color: var(--white)

    .shuoshuo-comment-btn
      padding: 2px
      color: #90a4ae
      cursor: pointer

      &:hover
        color: $light-blue

  .shuoshuo-comment
    padding-top: 10px

    &.no-comment
      display: none
