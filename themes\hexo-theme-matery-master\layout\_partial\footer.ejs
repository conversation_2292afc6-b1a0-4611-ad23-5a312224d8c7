<footer class="page-footer bg-color">
    <%if(theme.music.enable&&theme.music.fixed){ %>
        <%- partial('_widget/music') %>
    <% } %>
    <div class="container row center-align" style="margin-bottom: <% if (theme.time.enable) { %>15<% } else { %>0<% } %>px !important;">
        <div class="col s12 m8 l8 copy-right">
            Copyright&nbsp;&copy;
            <% if (theme.time.year !== new Date().getFullYear()) { %>
                <span id="year"><%- theme.time.year %>-<%- new Date().getFullYear() %></span>
            <% } else { %>
                <span id="year"><%- theme.time.year %></span>
            <% } %>
            <span id="year"><%- theme.time.year %></span>
            <a href="<%- url_for('/about') %>" target="_blank"><%- config.author %></a>
            |&nbsp;Powered by&nbsp;<a href="https://hexo.io/" target="_blank">Hexo</a>
            |&nbsp;Theme&nbsp;<a href="https://github.com/blinkfox/hexo-theme-matery" target="_blank">Matery</a>
            <br>
            <% if (theme.postInfo.totalCount) { %>
            &nbsp;<i class="fas fa-chart-area"></i>&nbsp;站点总字数:&nbsp;<span
                class="white-color"><%= totalcount(site) %></span>&nbsp;字
            <% } %>
            <% let socialClass = '' %>
            <% if (theme.busuanziStatistics && theme.busuanziStatistics.enable) { %>
            <% socialClass = 'social-statis' %>
            <% } %>
            <% if (theme.busuanziStatistics && theme.busuanziStatistics.totalTraffic) { %>
            <span id="busuanzi_container_site_pv">
                |&nbsp;<i class="far fa-eye"></i>&nbsp;总访问量:&nbsp;<span id="busuanzi_value_site_pv"
                    class="white-color"></span>&nbsp;次
            </span>
            <% } %>
            <% if (theme.busuanziStatistics && theme.busuanziStatistics.totalNumberOfvisitors) { %>
            <span id="busuanzi_container_site_uv">
                |&nbsp;<i class="fas fa-users"></i>&nbsp;总访问人数:&nbsp;<span id="busuanzi_value_site_uv"
                    class="white-color"></span>&nbsp;人
            </span>
            <% } %>
            <br>
            <% if (theme.time.enable) { %>
            <span id="sitetime">载入运行时间...</span>
            <script>
                function siteTime() {
                    var seconds = 1000;
                    var minutes = seconds * 60;
                    var hours = minutes * 60;
                    var days = hours * 24;
                    var years = days * 365;
                    var today = new Date();
                    var startYear = "<%- theme.time.year %>";
                    var startMonth = "<%- theme.time.month %>";
                    var startDate = "<%- theme.time.date %>";
                    var startHour = "<%- theme.time.hour %>";
                    var startMinute = "<%- theme.time.minute %>";
                    var startSecond = "<%- theme.time.second %>";
                    var todayYear = today.getFullYear();
                    var todayMonth = today.getMonth() + 1;
                    var todayDate = today.getDate();
                    var todayHour = today.getHours();
                    var todayMinute = today.getMinutes();
                    var todaySecond = today.getSeconds();
                    var t1 = Date.UTC(startYear, startMonth, startDate, startHour, startMinute, startSecond);
                    var t2 = Date.UTC(todayYear, todayMonth, todayDate, todayHour, todayMinute, todaySecond);
                    var diff = t2 - t1;
                    var diffYears = Math.floor(diff / years);
                    var diffDays = Math.floor((diff / days) - diffYears * 365);
                    var diffHours = Math.floor((diff - (diffYears * 365 + diffDays) * days) / hours);
                    var diffMinutes = Math.floor((diff - (diffYears * 365 + diffDays) * days - diffHours * hours) /
                        minutes);
                    var diffSeconds = Math.floor((diff - (diffYears * 365 + diffDays) * days - diffHours * hours -
                        diffMinutes * minutes) / seconds);
                    if (startYear == todayYear) {
                        document.getElementById("year").innerHTML = todayYear;
                        document.getElementById("sitetime").innerHTML = "本站已安全运行 " + diffDays + " 天 " + diffHours +
                            " 小时 " + diffMinutes + " 分钟 " + diffSeconds + " 秒";
                    } else {
                        document.getElementById("year").innerHTML = startYear + " - " + todayYear;
                        document.getElementById("sitetime").innerHTML = "本站已安全运行 " + diffYears + " 年 " + diffDays +
                            " 天 " + diffHours + " 小时 " + diffMinutes + " 分钟 " + diffSeconds + " 秒";
                    }
                }
                setInterval(siteTime, 1000);
            </script>
            <% } %>
            <br>
            <% if (theme.icp.enable) { %>
            <span id="icp"><img src="<%- theme.jsDelivr.url %><%- url_for('/medias/icp.png') %>" style="vertical-align: text-bottom;" />
                <a href="<%- url_for(theme.icp.url) %>" target="_blank"><%= theme.icp.text %></a>
            </span>
            <% } %>
        </div>
        <div class="col s12 m4 l4 social-link <%- socialClass %>"><%- partial('_partial/social-link') %></div>
    </div>
</footer>

<div class="progress-bar"></div>
