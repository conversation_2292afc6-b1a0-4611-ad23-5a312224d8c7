#footer
  position: relative
  background-color: $light-blue
  background-attachment: scroll
  background-position: bottom
  background-size: cover

  if hexo-config('footer_img') != false && hexo-config('mask.footer')
    &:before
      position: absolute
      width: 100%
      height: 100%
      background-color: var(--mark-bg)
      content: ''

  & > *
    position: relative
    color: var(--light-grey)

  a
    color: var(--light-grey)
    transition: all .3s ease-in-out

    &:hover
      color: $light-blue

  .footer-separator
    margin: 0 4px

  .icp-icon
    padding: 0 4px
    max-height: 1.4em
    width: auto
    vertical-align: text-bottom

  .footer-flex
    display: flex
    flex-direction: row
    flex-wrap: wrap
    justify-content: space-between
    margin: 0 auto
    padding: 40px 60px
    max-width: 1200px
    width: 100%
    text-align: left
    gap: 13px

    +maxWidth768()
      padding: 30px
      gap: 10px

    .footer-flex-items
      flex-shrink: 0
      min-width: 100px
      text-align: left
      white-space: nowrap

    .footer-flex-title
      margin-bottom: 5px
      white-space: nowrap
      font-weight: 600
      font-size: 1.4em

    .footer-flex-item
      margin: 10px 0
      white-space: nowrap

    a
      display: block
      white-space: nowrap

  .footer-other
    padding: 40px 20px
    width: 100%
    text-align: center

    if hexo-config('footer.nav')
      padding: 10px 8px
      background-color: rgba(0, 0, 0, .1)

      .copyright,
      .framework-info,
      .footer_custom_text
        font-size: .9em
    else
      .framework-info
        display: block