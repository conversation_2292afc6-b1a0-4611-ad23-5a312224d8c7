<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>cheerio | 你真是一个美好的人类</title><meta name="keywords" content="Node,Node.js模块,cheerio"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="cheerio是nodejs的抓取页面模块，为服务器特别定制的，快速、灵活、实施的jQuery核心实现。适合各种Web爬虫程序。这里简单介绍一下cheerio的使用方法和常用API。">
<meta property="og:type" content="article">
<meta property="og:title" content="cheerio">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/a0f8eb24.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="cheerio是nodejs的抓取页面模块，为服务器特别定制的，快速、灵活、实施的jQuery核心实现。适合各种Web爬虫程序。这里简单介绍一下cheerio的使用方法和常用API。">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png">
<meta property="article:published_time" content="2019-12-18T10:02:11.000Z">
<meta property="article:modified_time" content="2019-12-18T10:02:11.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="Node">
<meta property="article:tag" content="Node.js模块">
<meta property="article:tag" content="cheerio">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/a0f8eb24"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'cheerio',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2019-12-18 10:02:11'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">cheerio</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2019-12-18T10:02:11.000Z" title="发表于 2019-12-18 10:02:11">2019-12-18</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2019-12-18T10:02:11.000Z" title="更新于 2019-12-18 10:02:11">2019-12-18</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/Node/">Node</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/Node/Node%E7%88%AC%E8%99%AB/">Node爬虫</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><blockquote>
<p>cheerio 是 jquery 核心功能的一个快速灵活而又简洁的实现，主要是为了用在服务器端需要对 DOM 进行操作的地方</p>
</blockquote>
<h3 id="简介"><a href="#简介" class="headerlink" title="简介"></a>简介</h3><p>cheerio 是 nodejs 的抓取页面模块，为服务器特别定制的，快速、灵活、实施的 jQuery 核心实现。适合各种 Web 爬虫程序。</p>
<p>让你在服务器端和 html 愉快的玩耍</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">var cheerio = require(&#x27;cheerio&#x27;),</span><br><span class="line">  $ = cheerio.load(&#x27;&lt;h2 class = &quot;title&quot;&gt;Hello world&lt;/h2&gt;&#x27;);</span><br><span class="line"></span><br><span class="line">$(&#x27;h2.title&#x27;).text(&#x27;Hello there!&#x27;);</span><br><span class="line">$(&#x27;h2&#x27;).addClass(&#x27;welcome&#x27;);</span><br><span class="line"></span><br><span class="line">$.html();</span><br><span class="line">//=&gt; &lt;h2 class = &quot;title welcome&quot;&gt;Hello there!&lt;/h2&gt;</span><br></pre></td></tr></table></figure>

<h3 id="安装"><a href="#安装" class="headerlink" title="安装"></a>安装</h3><figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install cheerio</span><br></pre></td></tr></table></figure>

<h3 id="特点"><a href="#特点" class="headerlink" title="特点"></a>特点</h3><ul>
<li><strong>熟悉的语法*</strong>：cheerio 实现了 jQuery 的一个子集，去掉了 jQuery 中所有与 DOM 不一致或者是用来填浏览器的坑的东西，重现了 jQuery 最美妙的 API</li>
<li><strong>快到没朋友</strong>：cheerio 使用了及其简洁而又标准的 DOM 模型， 因此对文档的转换，操作，渲染都极其的高效。基本的端到端测试显示它的速度至少是 JSDOM 的 8 倍</li>
<li><strong>极其灵活</strong>：cheerio 使用了<a target="_blank" rel="noopener external nofollow noreferrer" href="https://link.jianshu.com/?t=https://github.com/FB55">@FB55</a>编写的非常兼容的<a target="_blank" rel="noopener external nofollow noreferrer" href="https://link.jianshu.com/?t=https://github.com/fb55/htmlparser2">htmlparser2</a>，因此它可以解析几乎所有的 HTML 和 XML</li>
</ul>
<h3 id="关于-JSDOM"><a href="#关于-JSDOM" class="headerlink" title="关于 JSDOM"></a>关于 JSDOM</h3><p>cheerio 产生的原因是出于对 JSDOM 的失望，主要体现在以下三点：</p>
<ul>
<li><strong>JSDOM 的解析规则太过于严格</strong>：JSDOM 的解析器无法处理现在许多的流行网站的内容</li>
<li><strong>JSDOM 太慢了</strong>：解析大的网站甚至可以产生可察觉的延迟</li>
<li><strong>JSDOM 太过于重量级</strong>：JSDOM 的目标是提供与浏览器一样的 DOM 环境，但是我们往往不需要这样。我们需要的只是一种简单，熟悉的方式来操作我们的 HTML</li>
</ul>
<h3 id="什么时候你应该用-JSDOM"><a href="#什么时候你应该用-JSDOM" class="headerlink" title="什么时候你应该用 JSDOM"></a>什么时候你应该用 JSDOM</h3><p>cheerio 并非万能，当你需要一个浏览器一样的环境时，你最好还是用 JSDOM，尤其是你需要进行自动化的功能测试时</p>
<h3 id="API"><a href="#API" class="headerlink" title="API"></a>API</h3><p>后面的例子中用到的 HTML 模板如下：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">&lt;ul id=&quot;fruits&quot;&gt;</span><br><span class="line">  &lt;li class=&quot;apple&quot;&gt;Apple&lt;/li&gt;</span><br><span class="line">  &lt;li class=&quot;orange&quot;&gt;Orange&lt;/li&gt;</span><br><span class="line">  &lt;li class=&quot;pear&quot;&gt;Pear&lt;/li&gt;</span><br><span class="line">&lt;/ul&gt;</span><br></pre></td></tr></table></figure>

<h4 id="解析-html（load）"><a href="#解析-html（load）" class="headerlink" title="解析 html（load）"></a>解析 html（load）</h4><p>首先你需要先加载你的 HTML。jQuery 会自动完成这一步，因为 jQuery 操作的 DOM 是固定的。但是在使用 cheerio 时我们要手动加载我们的 HTML 文档</p>
<p>首选的方式如下：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">var cheerio = require(&#x27;cheerio&#x27;),</span><br><span class="line">$ = cheerio.load(&#x27;&lt;ul id = &quot;fruits&quot;&gt;...&lt;/ul&gt;&#x27;);</span><br></pre></td></tr></table></figure>

<p>其次，直接把 HTML 字符串作为上下文也是可以的：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$ = require(&#x27;cheerio&#x27;);</span><br><span class="line">$(&#x27;ul&#x27;, &#x27;&lt;ul id = &quot;fruits&quot;&gt;...&lt;/ul&gt;&#x27;);</span><br></pre></td></tr></table></figure>

<p>或者把 HTML 字符串作为 root</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$ = require(&#x27;cheerio&#x27;);</span><br><span class="line">$(&#x27;li&#x27;, &#x27;ul&#x27;, &#x27;&lt;ul id = &quot;fruits&quot;&gt;...&lt;/ul&gt;&#x27;);</span><br></pre></td></tr></table></figure>

<p>如果你需要自定义一些解析选项，你可以多传递一个对象给 load 方法：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line">$ = cheerio.load(&#x27;&lt;ul id = &quot;fruits&quot;&gt;...&lt;/ul&gt;&#x27;, &#123;</span><br><span class="line">  ignoreWhitespace: true,</span><br><span class="line">  xmlMode: true</span><br><span class="line">&#125;);</span><br></pre></td></tr></table></figure>

<p>更多的解析选项可以参考<a target="_blank" rel="noopener external nofollow noreferrer" href="https://link.jianshu.com/?t=https://github.com/fb55/domhandler">domhandler</a>和<a target="_blank" rel="noopener external nofollow noreferrer" href="https://link.jianshu.com/?t=https://github.com/fb55/htmlparser2/wiki/Parser-options">parser-options</a></p>
<h4 id="选择器（selectors）"><a href="#选择器（selectors）" class="headerlink" title="选择器（selectors）"></a>选择器（selectors）</h4><p>cheerio 的选择器几乎和 jQuery 一模一样，所以语法上十分相像</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">$( selector, [context], [root] )</span><br></pre></td></tr></table></figure>

<p><strong>selector</strong>在<strong>context</strong>的范围内搜索，<strong>context</strong>的范围又包含在<strong>root</strong>的范围内。<strong>selector</strong>和<strong>context</strong>可以是一个字符串，DOM 元素，DOM 数组或者 cheerio 实例。<strong>root</strong>一般是一个 HTML 文档字符串</p>
<p>选择器是文档遍历和操作的起点。如同在 jQuery 中一样，它是选择元素节点最重要的方法，但是在 jQuery 中选择器建立在 CSS 选择器标准库上。cheerio 的选择器实现了大部分的方法</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.apple&#x27;, &#x27;#fruits&#x27;).text()</span><br><span class="line">//=&gt; Apple</span><br><span class="line"></span><br><span class="line">$(&#x27;ul .pear&#x27;).attr(&#x27;class&#x27;)</span><br><span class="line">//=&gt; pear</span><br><span class="line"></span><br><span class="line">$(&#x27;li[class=orange]&#x27;).html()</span><br><span class="line">//=&gt; &lt;li class = &quot;orange&quot;&gt;Orange&lt;/li&gt;</span><br></pre></td></tr></table></figure>

<h4 id="属性操作（atrributes）"><a href="#属性操作（atrributes）" class="headerlink" title="属性操作（atrributes）"></a>属性操作（atrributes）</h4><p>用来获取和更改属性的方法：</p>
<p><strong>.attr(name, value)</strong></p>
<p>这个方法用来获取和设置属性。获取第一个符合匹配的元素的属性值。如果某个属性值被设置成 null，那么该属性会被移除。你也可以把<strong>map</strong>和<strong>function</strong>作为参数传递进去，就像在 jQuery 中一样</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;ul&#x27;).attr(&#x27;id&#x27;)</span><br><span class="line">//=&gt; fruits</span><br><span class="line"></span><br><span class="line">$(&#x27;.apple&#x27;).attr(&#x27;id&#x27;, &#x27;favorite&#x27;).html()</span><br><span class="line">//=&gt; &lt;li class = &quot;apple&quot; id = &quot;favorite&quot;&gt;Apple&lt;/li&gt;</span><br></pre></td></tr></table></figure>

<blockquote>
<p>更多信息请查看 <a target="_blank" rel="noopener external nofollow noreferrer" href="https://link.jianshu.com/?t=http://api.jquery.com/attr/">http://api.jquery.com/attr/</a></p>
</blockquote>
<p><strong>.removeAtrr(name)</strong></p>
<p>移除名为 name 的属性</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.pear&#x27;).removeAttr(&#x27;class&#x27;).html()</span><br><span class="line">//=&gt; &lt;li&gt;Pear&lt;/li&gt;</span><br></pre></td></tr></table></figure>

<p><strong>.hasClass(className)</strong></p>
<p>检查元素是否含有此类名</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.pear&#x27;).hasClass(&#x27;pear&#x27;)</span><br><span class="line">//=&gt; true</span><br><span class="line"></span><br><span class="line">$(&#x27;apple&#x27;).hasClass(&#x27;fruit&#x27;)</span><br><span class="line">//=&gt; false</span><br><span class="line"></span><br><span class="line">$(&#x27;li&#x27;).hasClass(&#x27;pear&#x27;)</span><br><span class="line">//=&gt; true</span><br></pre></td></tr></table></figure>

<p><strong>.addClass(className)</strong></p>
<p>添加类名到所有的匹配元素，可以用函数作为参数</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.pear&#x27;).addClass(&#x27;fruit&#x27;).html()</span><br><span class="line">//=&gt; &lt;li class = &quot;pear fruit&quot;&gt;Pear&lt;/li&gt;</span><br><span class="line"></span><br><span class="line">$(&#x27;.apple&#x27;).addClass(&#x27;fruit red&#x27;).html()</span><br><span class="line">//=&gt; &lt;li class = &quot;apple fruit red&quot;&gt;Apple&lt;/li&gt;</span><br></pre></td></tr></table></figure>

<blockquote>
<p>参见 <a target="_blank" rel="noopener external nofollow noreferrer" href="https://link.jianshu.com/?t=http://api.jquery.com/addClass/">http://api.jquery.com/addClass/</a></p>
</blockquote>
<p><strong>.remoteClass([className])</strong></p>
<p>移除一个或者多个（空格分隔）的类名，如果 className 为空，则所有的类名都会被移除，可以传递函数作为参数</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.pear&#x27;).removeClass(&#x27;pear&#x27;).html()</span><br><span class="line">//=&gt; &lt;li class = &quot;&quot;&gt;Pear&lt;/li&gt;</span><br><span class="line"></span><br><span class="line">$(&#x27;.apple&#x27;).addClass(&#x27;red&#x27;).removeClass().html()</span><br><span class="line">//=&gt; &lt;li class = &quot;&quot;&gt;Apple&lt;/li&gt;</span><br></pre></td></tr></table></figure>

<blockquote>
<p>参见 <a target="_blank" rel="noopener external nofollow noreferrer" href="https://link.jianshu.com/?t=http://api.jquery.com/removeClass/">http://api.jquery.com/removeClass/</a></p>
</blockquote>
<h4 id="遍历"><a href="#遍历" class="headerlink" title="遍历"></a>遍历</h4><p><strong>.find(selector)</strong></p>
<p>在当前元素集合中选择符合选择器规则的元素集合</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;#fruits&#x27;).find(&#x27;li&#x27;).length</span><br><span class="line">//=&gt; 3</span><br></pre></td></tr></table></figure>

<p><strong>.parent()</strong></p>
<p>获取元素集合第一个元素的父元素</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.pear&#x27;).parent().attr(&#x27;id&#x27;)</span><br><span class="line">//=&gt; fruits</span><br></pre></td></tr></table></figure>

<p><strong>.next()</strong></p>
<p>选择当前元素的下一个兄弟元素</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.apple&#x27;).next().hasClass(&#x27;orange&#x27;)</span><br><span class="line">//=&gt; true</span><br></pre></td></tr></table></figure>

<p><strong>.prev()</strong></p>
<p>同**.next()**相反</p>
<p><strong>.siblings()</strong></p>
<p>获取元素集合中第一个元素的所有兄弟元素，不包含它自己</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.pear&#x27;).siblings().length</span><br><span class="line">//=&gt; 2</span><br></pre></td></tr></table></figure>

<p><strong>.children( selector )</strong></p>
<p><strong>.each( function(index, element) )</strong></p>
<p>遍历函数返回 false 即可终止遍历</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">var fruits = [];</span><br><span class="line"></span><br><span class="line">$(&#x27;li&#x27;).each(function(i, elem) &#123;</span><br><span class="line">  fruits[i] = $(this).text();</span><br><span class="line">&#125;);</span><br><span class="line"></span><br><span class="line">fruits.join(&#x27;, &#x27;);</span><br><span class="line">//=&gt; Apple, Orange, Pear</span><br></pre></td></tr></table></figure>

<p><strong>.map( function(index, element) )</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;li&#x27;).map(function(i, el) &#123;</span><br><span class="line">  // this === el</span><br><span class="line">  return $(this).attr(&#x27;class&#x27;);</span><br><span class="line">&#125;).get().join(&#x27;, &#x27;);</span><br><span class="line">//=&gt; apple, orange, pear</span><br></pre></td></tr></table></figure>

<p><strong>.filter( selector )</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;li&#x27;).filter(&#x27;.orange&#x27;).attr(&#x27;class&#x27;);</span><br><span class="line">//=&gt; orange</span><br></pre></td></tr></table></figure>

<p><strong>.filter( function(index) )</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;li&#x27;).filter(function(i, el) &#123;</span><br><span class="line">  // this === el</span><br><span class="line">  return $(this).attr(&#x27;class&#x27;) === &#x27;orange&#x27;;</span><br><span class="line">&#125;).attr(&#x27;class&#x27;)</span><br><span class="line">//=&gt; orange</span><br></pre></td></tr></table></figure>

<p><strong>.first()</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;#fruits&#x27;).children().first().text()</span><br><span class="line">//=&gt; Apple</span><br></pre></td></tr></table></figure>

<p><strong>.last()</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;#fruits&#x27;).children().last().text()</span><br><span class="line">//=&gt; Pear</span><br></pre></td></tr></table></figure>

<p><strong>.eq( i )</strong></p>
<p>缩小元素集合，可以用负数表示倒数第 i 个元素被保留</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;li&#x27;).eq(0).text()</span><br><span class="line">//=&gt; Apple</span><br><span class="line"></span><br><span class="line">$(&#x27;li&#x27;).eq(-1).text()</span><br><span class="line">//=&gt; Pear</span><br></pre></td></tr></table></figure>

<h4 id="操作-DOM"><a href="#操作-DOM" class="headerlink" title="操作 DOM"></a>操作 DOM</h4><p>操作 DOM 结构的方法</p>
<p><strong>.append( content, [content, …] )</strong></p>
<p><strong>.prepend( content, [content, …] )</strong></p>
<p><strong>.after( content, [content, …] )</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.apple&#x27;).after(&#x27;&lt;li class = &quot;plum&quot;&gt;Plum&lt;/li&gt;&#x27;)</span><br><span class="line">$.html()</span><br><span class="line">//=&gt;  &lt;ul id = &quot;fruits&quot;&gt;</span><br><span class="line">//      &lt;li class = &quot;apple&quot;&gt;Apple&lt;/li&gt;</span><br><span class="line">//      &lt;li class = &quot;plum&quot;&gt;Plum&lt;/li&gt;</span><br><span class="line">//      &lt;li class = &quot;orange&quot;&gt;Orange&lt;/li&gt;</span><br><span class="line">//      &lt;li class = &quot;pear&quot;&gt;Pear&lt;/li&gt;</span><br><span class="line">//    &lt;/ul&gt;</span><br></pre></td></tr></table></figure>

<p><strong>.before( content, [content, …] )</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.apple&#x27;).before(&#x27;&lt;li class = &quot;plum&quot;&gt;Plum&lt;/li&gt;&#x27;)</span><br><span class="line">$.html()</span><br><span class="line">//=&gt;  &lt;ul id = &quot;fruits&quot;&gt;</span><br><span class="line">//      &lt;li class = &quot;plum&quot;&gt;Plum&lt;/li&gt;</span><br><span class="line">//      &lt;li class = &quot;apple&quot;&gt;Apple&lt;/li&gt;</span><br><span class="line">//      &lt;li class = &quot;orange&quot;&gt;Orange&lt;/li&gt;</span><br><span class="line">//      &lt;li class = &quot;pear&quot;&gt;Pear&lt;/li&gt;</span><br><span class="line">//    &lt;/ul&gt;</span><br></pre></td></tr></table></figure>

<p><strong>.remove( [selector] )</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.pear&#x27;).remove()</span><br><span class="line">$.html()</span><br><span class="line">//=&gt;  &lt;ul id = &quot;fruits&quot;&gt;</span><br><span class="line">//      &lt;li class = &quot;apple&quot;&gt;Apple&lt;/li&gt;</span><br><span class="line">//      &lt;li class = &quot;orange&quot;&gt;Orange&lt;/li&gt;</span><br><span class="line">//    &lt;/ul&gt;</span><br></pre></td></tr></table></figure>

<p><strong>.replaceWith( content )</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">var plum = $(&#x27;&lt;li class = &quot;plum&quot;&gt;Plum&lt;/li&gt;&#x27;)</span><br><span class="line">$(&#x27;.pear&#x27;).replaceWith(plum)</span><br><span class="line">$.html()</span><br><span class="line">//=&gt; &lt;ul id = &quot;fruits&quot;&gt;</span><br><span class="line">//     &lt;li class = &quot;apple&quot;&gt;Apple&lt;/li&gt;</span><br><span class="line">//     &lt;li class = &quot;orange&quot;&gt;Orange&lt;/li&gt;</span><br><span class="line">//     &lt;li class = &quot;plum&quot;&gt;Plum&lt;/li&gt;</span><br><span class="line">//   &lt;/ul&gt;</span><br></pre></td></tr></table></figure>

<p><strong>.empty()</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;ul&#x27;).empty()</span><br><span class="line">$.html()</span><br><span class="line">//=&gt;  &lt;ul id = &quot;fruits&quot;&gt;&lt;/ul&gt;</span><br></pre></td></tr></table></figure>

<p><strong>.html( [htmlString] )</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.orange&#x27;).html()</span><br><span class="line">//=&gt; Orange</span><br><span class="line"></span><br><span class="line">$(&#x27;#fruits&#x27;).html(&#x27;&lt;li class = &quot;mango&quot;&gt;Mango&lt;/li&gt;&#x27;).html()</span><br><span class="line">//=&gt; &lt;li class=&quot;mango&quot;&gt;Mango&lt;/li&gt;</span><br></pre></td></tr></table></figure>

<p><strong>.text( [textString] )</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;.orange&#x27;).text()</span><br><span class="line">//=&gt; Orange</span><br><span class="line"></span><br><span class="line">$(&#x27;ul&#x27;).text()</span><br><span class="line">//=&gt;  Apple</span><br><span class="line">//    Orange</span><br><span class="line">//    Pear</span><br></pre></td></tr></table></figure>

<h4 id="解析和渲染"><a href="#解析和渲染" class="headerlink" title="解析和渲染"></a>解析和渲染</h4><figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">$.html()</span><br><span class="line">//=&gt;  &lt;ul id = &quot;fruits&quot;&gt;</span><br><span class="line">//      &lt;li class = &quot;apple&quot;&gt;Apple&lt;/li&gt;</span><br><span class="line">//      &lt;li class = &quot;orange&quot;&gt;Orange&lt;/li&gt;</span><br><span class="line">//      &lt;li class = &quot;pear&quot;&gt;Pear&lt;/li&gt;</span><br><span class="line">//    &lt;/ul&gt;</span><br></pre></td></tr></table></figure>

<p>输出包含自己在内的 HTML（outer HTML）</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$.html(&#x27;.pear&#x27;)</span><br><span class="line">//=&gt; &lt;li class = &quot;pear&quot;&gt;Pear&lt;/li&gt;</span><br></pre></td></tr></table></figure>

<h4 id="杂项"><a href="#杂项" class="headerlink" title="杂项"></a>杂项</h4><p><strong>.toArray()</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$(&#x27;li&#x27;).toArray()</span><br><span class="line">//=&gt; [ &#123;...&#125;, &#123;...&#125;, &#123;...&#125; ]</span><br></pre></td></tr></table></figure>

<p><strong>.clone()</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">var moreFruit = $(&#x27;#fruits&#x27;).clone()</span><br></pre></td></tr></table></figure>

<h4 id="常用工具"><a href="#常用工具" class="headerlink" title="常用工具"></a>常用工具</h4><p><strong>$.root()</strong></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line">$.root().append(&#x27;&lt;ul id=&quot;vegetables&quot;&gt;&lt;/ul&gt;&#x27;).html();</span><br><span class="line">//=&gt; &lt;ul id=&quot;fruits&quot;&gt;...&lt;/ul&gt;&lt;ul id=&quot;vegetables&quot;&gt;&lt;/ul&gt;</span><br></pre></td></tr></table></figure>

<p><strong>$.contains( container, contained )</strong></p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/a0f8eb24.html">http://blog.mhy.loc.cc/archives/a0f8eb24.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/Node/">Node</a><a class="post-meta__tags" href="/tags/Node-js%E6%A8%A1%E5%9D%97/">Node.js模块</a><a class="post-meta__tags" href="/tags/cheerio/">cheerio</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/1212afb3.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Node爬取数据到数据库实战代码笔记</div></div></a></div><div class="next-post pull-right"><a href="/archives/44b2b83c.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184621.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Node学习总结笔记</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/44b2b83c.html" title="Node学习总结笔记"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184621.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-13</div><div class="title">Node学习总结笔记</div></div></a></div><div><a href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2023-06-15</div><div class="title">mac OS 配置前端开发环境</div></div></a></div><div><a href="/archives/aef974df.html" title="npm使用奇淫技巧"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184541.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-09-07</div><div class="title">npm使用奇淫技巧</div></div></a></div><div><a href="/archives/4faef664.html" title="JWT 鉴权在 ThinkJS 中的实践"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830221722.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-08-30</div><div class="title">JWT 鉴权在 ThinkJS 中的实践</div></div></a></div><div><a href="/archives/482180fa.html" title="ThinkJS允许跨域处理"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830222115.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-08-30</div><div class="title">ThinkJS允许跨域处理</div></div></a></div><div><a href="/archives/155e47b.html" title="使用 svg-captcha 插件在 ThinkJS 中实现随机验证码功能"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200830221642.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-08-30</div><div class="title">使用 svg-captcha 插件在 ThinkJS 中实现随机验证码功能</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%AE%80%E4%BB%8B"><span class="toc-text">简介</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AE%89%E8%A3%85"><span class="toc-text">安装</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%89%B9%E7%82%B9"><span class="toc-text">特点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%85%B3%E4%BA%8E-JSDOM"><span class="toc-text">关于 JSDOM</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BB%80%E4%B9%88%E6%97%B6%E5%80%99%E4%BD%A0%E5%BA%94%E8%AF%A5%E7%94%A8-JSDOM"><span class="toc-text">什么时候你应该用 JSDOM</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#API"><span class="toc-text">API</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%A7%A3%E6%9E%90-html%EF%BC%88load%EF%BC%89"><span class="toc-text">解析 html（load）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%80%89%E6%8B%A9%E5%99%A8%EF%BC%88selectors%EF%BC%89"><span class="toc-text">选择器（selectors）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%B1%9E%E6%80%A7%E6%93%8D%E4%BD%9C%EF%BC%88atrributes%EF%BC%89"><span class="toc-text">属性操作（atrributes）</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%81%8D%E5%8E%86"><span class="toc-text">遍历</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%93%8D%E4%BD%9C-DOM"><span class="toc-text">操作 DOM</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%A7%A3%E6%9E%90%E5%92%8C%E6%B8%B2%E6%9F%93"><span class="toc-text">解析和渲染</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%9D%82%E9%A1%B9"><span class="toc-text">杂项</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E5%B7%A5%E5%85%B7"><span class="toc-text">常用工具</span></a></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>