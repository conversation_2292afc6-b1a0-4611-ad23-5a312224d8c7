nav#nav
  span#blog-info
    a.nav-site-title(href=url_for('/'))
      if theme.nav.logo
        img.site-icon(src=url_for(theme.nav.logo) alt='Logo')
      if theme.nav.display_title
        span.site-name=config.title
    if globalPageType === 'post' && theme.nav.display_post_title
      a.nav-page-title(href=url_for('/'))
        span.site-name=(page.title || config.title)
        span.site-name
          i.fa-solid.fa-circle-arrow-left
          span= '  ' + _p('post.back_to_home')

  #menus
    if theme.search.use
      #search-button
        span.site-page.social-icon.search
          i.fas.fa-search.fa-fw
          span= ' ' + _p('search.title')
    if theme.menu
      != partial('includes/header/menu_item', {}, {cache: true})

      #toggle-menu
        span.site-page
          i.fas.fa-bars.fa-fw