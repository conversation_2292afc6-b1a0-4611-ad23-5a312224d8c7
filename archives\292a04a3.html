<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>JavaScript数据结构和算法：单向链表 | 你真是一个美好的人类</title><meta name="keywords" content="JavaScript,数据结构,算法,单向链表"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="链表和数组一样, 可以用于存储一系列的元素, 但是链表和数组的实现机制完全不同。这里我们将学习一下链表中的常见操作，并进行一个封装！">
<meta property="og:type" content="article">
<meta property="og:title" content="JavaScript数据结构和算法：单向链表">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/292a04a3.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="链表和数组一样, 可以用于存储一系列的元素, 但是链表和数组的实现机制完全不同。这里我们将学习一下链表中的常见操作，并进行一个封装！">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg">
<meta property="article:published_time" content="2020-07-12T15:18:30.000Z">
<meta property="article:modified_time" content="2020-07-12T15:18:30.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="JavaScript">
<meta property="article:tag" content="数据结构">
<meta property="article:tag" content="算法">
<meta property="article:tag" content="单向链表">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/292a04a3"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'JavaScript数据结构和算法：单向链表',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-07-12 15:18:30'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">JavaScript数据结构和算法：单向链表</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-07-12T15:18:30.000Z" title="发表于 2020-07-12 15:18:30">2020-07-12</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-07-12T15:18:30.000Z" title="更新于 2020-07-12 15:18:30">2020-07-12</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/">前端</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/JavaScript/">JavaScript</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/JavaScript/%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84%E5%92%8C%E7%AE%97%E6%B3%95/">数据结构和算法</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><h2 id="单向链表简介"><a href="#单向链表简介" class="headerlink" title="单向链表简介"></a>单向链表简介</h2><p>链表和数组一样，可以用于<strong>存储一系列的元素</strong>，但是链表和数组的<strong>实现机制完全不同</strong>。链表的每个元素由一个存储<strong>元素本身的节点</strong>和一个<strong>指向下一个元素的引用</strong>（有的语言称为指针或连接）组成。类似于火车头，一节车厢载着乘客（数据），通过节点连接另一节车厢。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707211621.png" alt="image-20200707211620661"></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707211630.png" alt="image-20200707211629678"></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707211642.png" alt="image-20200707211641966"></p>
<ul>
<li>head 属性指向链表的第一个节点；</li>
<li>链表中的最后一个节点指向 null；</li>
<li>当链表中一个节点也没有的时候，head 直接指向 null；</li>
</ul>
<h3 id="数组存在的缺点"><a href="#数组存在的缺点" class="headerlink" title="数组存在的缺点"></a>数组存在的缺点</h3><ul>
<li>数组的创建通常需要申请一段<strong>连续的内存空间</strong>（一整块内存），并且大小是固定的。所以当原数组<strong>不能满足容量需求</strong>时，需要<strong>扩容</strong>（一般情况下是申请一个更大的数组，比如 2 倍，然后将原数组中的元素复制过去）。</li>
<li>在数组的开头或中间位置插入数据的成本很高，需要进行大量元素的位移。</li>
</ul>
<h3 id="链表的优势"><a href="#链表的优势" class="headerlink" title="链表的优势"></a>链表的优势</h3><ul>
<li>链表中的元素在内存中<strong>不必是连续的空间</strong>，可以充分利用计算机的内存，实现灵活的<strong>内存动态管理</strong>。</li>
<li>链表不必在创建时就<strong>确定大小</strong>，并且大小可以<strong>无限地延伸</strong>下去。</li>
<li>链表在<strong>插入和删除</strong>数据时，<strong>时间复杂度</strong>可以达到 O(1)，相对数组效率高很多。</li>
</ul>
<p><strong>链表的缺点：</strong></p>
<ul>
<li>链表访问任何一个位置的元素时，都需要<strong>从头开始访问</strong>（无法跳过第一个元素访问任何一个元素）。</li>
<li>无法通过下标值直接访问元素，需要从头开始一个个访问，直到找到对应的元素。</li>
<li>虽然可以轻松地到达<strong>下一个节点</strong>，但是回到<strong>前一个节点</strong>是很难的。</li>
</ul>
<h3 id="链表中的常见操作"><a href="#链表中的常见操作" class="headerlink" title="链表中的常见操作"></a>链表中的常见操作</h3><ul>
<li>append（element）：向链表尾部添加一个新的项；</li>
<li>insert（position，element）：向链表的特定位置插入一个新的项；</li>
<li>get（position）：获取对应位置的元素；</li>
<li>indexOf（element）：返回元素在链表中的索引。如果链表中没有该元素就返回-1；</li>
<li>update（position，element）：修改某个位置的元素；</li>
<li>removeAt（position）：从链表的特定位置移除一项；</li>
<li>remove（element）：从链表中移除一项；</li>
<li>isEmpty（）：如果链表中不包含任何元素，返回 trun，如果链表长度大于 0 则返回 false；</li>
<li>size（）：返回链表包含的元素个数，与数组的 length 属性类似；</li>
<li>toString（）：由于链表项使用了 Node 类，就需要重写继承自 JavaScript 对象默认的 toString 方法，让其只输出元素的值；</li>
</ul>
<p>首先需要弄清楚：下文中的 position 指的是两个节点之间，并且与 index 的关系如下图所示：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707211659.png" alt="image-20200707211658363"></p>
<p>position 的值一般表示 position 所指位置的下一个节点。当 position 的值与 index 的值相等时，比如 position = index = 1，那么它们都表示 Node2。</p>
<h2 id="封装单向链表类"><a href="#封装单向链表类" class="headerlink" title="封装单向链表类"></a>封装单向链表类</h2><h3 id="创建单向链表类"><a href="#创建单向链表类" class="headerlink" title="创建单向链表类"></a>创建单向链表类</h3><p>先创建单向链表类 Linklist，并添加基本属性，再实现单向链表的常用方法：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718124029.png"></p>
<p>代码解析:</p>
<ul>
<li>封装 LinkedList 的类, 用于表示我们的链表结构. (和 Java 中的链表同名, 不同 Java 中的这个类是一个双向链表, 后面我们会讲解双向链表)</li>
<li>在 LinkedList 类中有一个 Node 类, 用于封装每一个节点上的信息.(和优先级队列的封装一样)</li>
<li>链表中我们保存两个属性, 一个是链表的长度, 一个是链表中第一个节点.</li>
</ul>
<h3 id="append-element"><a href="#append-element" class="headerlink" title="append(element)"></a>append(element)</h3><p>向链表尾部追加数据可能有两种情况:</p>
<ul>
<li>链表本身为空, 新添加的数据时唯一的节点.</li>
<li>链表不为空, 需要向其他节点后面追加节点.</li>
</ul>
<p><strong>代码实现：</strong></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://img.juanertu.com/img/20200718124458.png"></p>
<p><strong>过程详解：</strong></p>
<ul>
<li><p>首先需要做的是将 element 传入方法, 并根据 element 创建一个 Node 节点.</p>
</li>
<li><p>场景一: 链表本身是空的，我们只需要让 head 指向新建的 node 节点即可</p>
</li>
<li><p>场景二: 链表中已经有元素了, 需要向最后的节点的 next 中添加节点.首先让 current 指向第一个节点：</p>
</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707211827.png" alt="image-20200707211826281"></p>
<ul>
<li>通过 while 循环使 current 指向最后一个节点，最后通过 current.next = newNode，让最后一个节点指向新节点 newNode：</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707211844.png" alt="image-20200707211843432"></p>
<p><strong>测试代码：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//测试代码</span></span><br><span class="line"><span class="comment">//1.创建LinkList</span></span><br><span class="line"><span class="keyword">let</span> list = <span class="keyword">new</span> <span class="title class_">LinkList</span>()</span><br><span class="line"></span><br><span class="line"><span class="comment">//2.测试append方法</span></span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;aaa&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;bbb&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;ccc&#x27;</span>)</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list)</span><br></pre></td></tr></table></figure>

<p><strong>测试结果：</strong></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707211859.png" alt="image-20200707211858675"></p>
<h3 id="toString-NaN"><a href="#toString-NaN" class="headerlink" title="toString()"></a>toString()</h3><p><strong>代码实现：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 实现toString方法</span></span><br><span class="line"><span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">toString</span> = <span class="function">() =&gt;</span> &#123;</span><br><span class="line">  <span class="comment">// 1.定义变量</span></span><br><span class="line">  <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">  <span class="keyword">let</span> listString = <span class="string">&#x27;&#x27;</span></span><br><span class="line"></span><br><span class="line">  <span class="comment">// 2.循环获取一个个的节点</span></span><br><span class="line">  <span class="keyword">while</span> (current) &#123;</span><br><span class="line">    listString += current.<span class="property">data</span> + <span class="string">&#x27; &#x27;</span></span><br><span class="line">    current = current.<span class="property">next</span> <span class="comment">//千万不要忘了拼接完一个节点数据之后，让current指向下一个节点</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">return</span> listString</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p>方法解读:</p>
<ul>
<li>该方法比较简单, 主要是获取每一个元素</li>
<li>还是从 head 开头, 因为获取链表的任何元素都必须从第一个节点开头.</li>
<li>循环遍历每一个节点, 并且取出其中的 element, 拼接成字符串.</li>
<li>将最终字符串返回.</li>
</ul>
<p><strong>测试代码：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//测试代码</span></span><br><span class="line"><span class="comment">//1.创建LinkList</span></span><br><span class="line"><span class="keyword">let</span> list = <span class="keyword">new</span> <span class="title class_">LinkList</span>()</span><br><span class="line"></span><br><span class="line"><span class="comment">//2.插入数据</span></span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;aaa&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;bbb&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;ccc&#x27;</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment">//3.测试toString方法</span></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">toString</span>())</span><br></pre></td></tr></table></figure>

<p><strong>测试结果：</strong></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707211946.png" alt="image-20200707211945460"></p>
<h3 id="insert-position-element"><a href="#insert-position-element" class="headerlink" title="insert(position,element)"></a>insert(position,element)</h3><p><strong>代码实现：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 实现insert方法</span></span><br><span class="line"><span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">insert</span> = <span class="function">(<span class="params">position, data</span>) =&gt;</span> &#123;</span><br><span class="line">  <span class="comment">//理解positon的含义：position=0表示新界点插入后要成为第1个节点，position=2表示新界点插入后要成为第3个节点</span></span><br><span class="line">  <span class="comment">//1.对position进行越界判断:要求传入的position不能是负数且不能超过LinkList的length</span></span><br><span class="line">  <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt; <span class="variable language_">this</span>.<span class="property">length</span>) &#123;</span><br><span class="line">    <span class="keyword">return</span> <span class="literal">false</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="comment">//2.根据data创建newNode</span></span><br><span class="line">  <span class="keyword">let</span> newNode = <span class="keyword">new</span> <span class="title class_">Node</span>(data)</span><br><span class="line"></span><br><span class="line">  <span class="comment">//3.插入新节点</span></span><br><span class="line">  <span class="comment">//情况1：插入位置position=0</span></span><br><span class="line">  <span class="keyword">if</span> (position == <span class="number">0</span>) &#123;</span><br><span class="line">    <span class="comment">// 让新节点指向第一个节点</span></span><br><span class="line">    newNode.<span class="property">next</span> = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="comment">// 让head指向新节点</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">head</span> = newNode</span><br><span class="line">    <span class="comment">//情况2：插入位置position&gt;0(该情况包含position=length)</span></span><br><span class="line">  &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">    <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">    <span class="keyword">let</span> previous = <span class="literal">null</span></span><br><span class="line">    <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="comment">//步骤1：通过while循环使变量current指向position位置的后一个节点(注意while循环的写法)</span></span><br><span class="line">    <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">      <span class="comment">//步骤2：在current指向下一个节点之前，让previous指向current当前指向的节点</span></span><br><span class="line">      previous = current</span><br><span class="line">      current = current.<span class="property">next</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">// 步骤3：通过变量current(此时current已经指向position位置的后一个节点)，使newNode指向position位置的后一个节点</span></span><br><span class="line">    newNode.<span class="property">next</span> = current</span><br><span class="line">    <span class="comment">//步骤4：通过变量previous，使position位置的前一个节点指向newNode</span></span><br><span class="line">    previous.<span class="property">next</span> = newNode</span><br><span class="line">    <span class="comment">/*</span></span><br><span class="line"><span class="comment">            启示：</span></span><br><span class="line"><span class="comment">            1.我们无法直接操作链表中的节点，但是可以通过变量指向这些节点，以此间接地操作节点(替身使者)；</span></span><br><span class="line"><span class="comment">            比如current指向节点3，想要节点3指向节点4只需要：current.next = 4即可。</span></span><br><span class="line"><span class="comment">            2.两个节点间是双向的，想要节点2的前一个节点为节点1，可以通过：1.next=2，来实现；</span></span><br><span class="line"><span class="comment">          */</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="comment">//4.新节点插入后要length+1</span></span><br><span class="line">  <span class="variable language_">this</span>.<span class="property">length</span> += <span class="number">1</span></span><br><span class="line"></span><br><span class="line">  <span class="keyword">return</span> <span class="literal">true</span></span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p><strong>过程详解：</strong></p>
<p>inset 方法实现的过程：根据插入节点位置的不同可分为多种情况：</p>
<ul>
<li><strong>情况 1：position = 0</strong>：</li>
</ul>
<p>通过： newNode.next = this.head，建立连接 1；</p>
<p>通过： this.head = newNode，建立连接 2；（不能先建立连接 2，否则 this.head 不再指向 Node1）</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212004.png" alt="image-20200707212003041"></p>
<ul>
<li><strong>情况 2：position &gt; 0</strong>：</li>
</ul>
<p>首先定义两个变量 previous 和 curent 分别指向需要插入位置 pos = X 的前一个节点和后一个节点；</p>
<p>然后，通过：newNode.next = current，改变指向 3；</p>
<p>最后，通过：previous.next = newNode，改变指向 4；</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212022.png" alt="image-20200707212021082"></p>
<ul>
<li><strong>情况 2 的特殊情形：position = length</strong>：</li>
</ul>
<p>情况 2 也包含了 pos = length 的情况，该情况下 current 和 newNode.next 都指向 null；建立连接 3 和连接 4 的方式与情况 2 相同。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212047.png" alt="image-20200707212045666"></p>
<p>添加到其他位置:</p>
<ul>
<li>如果是添加到其他位置, 就需要先找到这个节点位置了.</li>
<li>我们通过 while 循环, 一点点向下找. 并且在这个过程中保存上一个节点和下一个节点.</li>
<li>找到正确的位置后, 将新节点的 next 指向下一个节点, 将上一个节点的 next 指向新的节点.</li>
</ul>
<p><strong>测试代码：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//测试代码</span></span><br><span class="line"><span class="comment">//1.创建LinkList</span></span><br><span class="line"><span class="keyword">let</span> list = <span class="keyword">new</span> <span class="title class_">LinkList</span>()</span><br><span class="line"></span><br><span class="line"><span class="comment">//2.插入数据</span></span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;aaa&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;bbb&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;ccc&#x27;</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment">//3.测试insert方法</span></span><br><span class="line">list.<span class="title function_">insert</span>(<span class="number">0</span>, <span class="string">&#x27;在链表最前面插入节点&#x27;</span>)</span><br><span class="line">list.<span class="title function_">insert</span>(<span class="number">2</span>, <span class="string">&#x27;在链表中第二个节点后插入节点&#x27;</span>)</span><br><span class="line">list.<span class="title function_">insert</span>(<span class="number">5</span>, <span class="string">&#x27;在链表最后插入节点&#x27;</span>)</span><br><span class="line"><span class="title function_">alert</span>(list)</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list)</span><br></pre></td></tr></table></figure>

<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212113.png" alt="image-20200707212112493"></p>
<h3 id="get-position"><a href="#get-position" class="headerlink" title="get(position)"></a>get(position)</h3><p><strong>代码实现：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//实现get方法</span></span><br><span class="line"><span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">get</span> = <span class="function">(<span class="params">position</span>) =&gt;</span> &#123;</span><br><span class="line">  <span class="comment">//1.越界判断</span></span><br><span class="line">  <span class="comment">// 当position = length时，取到的是null所以0 =&lt; position &lt; length</span></span><br><span class="line">  <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt;= <span class="variable language_">this</span>.<span class="property">length</span>) &#123;</span><br><span class="line">    <span class="keyword">return</span> <span class="literal">null</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="comment">//2.获取指定的positon位置的后一个节点的data</span></span><br><span class="line">  <span class="comment">//同样使用一个变量间接操作节点</span></span><br><span class="line">  <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">  <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">  <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">    current = current.<span class="property">next</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">return</span> current.<span class="property">data</span></span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p><strong>过程详解：</strong></p>
<p>get 方法的实现过程：以获取 position = 2 为例，如下图所示：</p>
<ul>
<li>首先使 current 指向第一个节点，此时 index=0；</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212134.png" alt="image-20200707212133575"></p>
<ul>
<li>通过 while 循环使 current 循环指向下一个节点，注意循环终止的条件 index++ &lt; position，即当 index=position 时停止循环，此时循环了 1 次，current 指向第二个节点(Node2)，最后通过 current.data 返回 Node2 节点的数据；</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212151.png" alt="image-20200707212150175"></p>
<p><strong>测试代码：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//测试代码</span></span><br><span class="line"><span class="comment">//1.创建LinkList</span></span><br><span class="line"><span class="keyword">let</span> list = <span class="keyword">new</span> <span class="title class_">LinkList</span>()</span><br><span class="line"></span><br><span class="line"><span class="comment">//2.插入数据</span></span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;aaa&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;bbb&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;ccc&#x27;</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment">//3.测试get方法</span></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">get</span>(<span class="number">0</span>))</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">get</span>(<span class="number">1</span>))</span><br></pre></td></tr></table></figure>

<p><strong>测试结果：</strong></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212205.png" alt="image-20200707212204745"></p>
<h3 id="indexOf-element"><a href="#indexOf-element" class="headerlink" title="indexOf(element)"></a>indexOf(element)</h3><p><strong>代码实现：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//实现indexOf方法</span></span><br><span class="line"><span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">indexOf</span> = <span class="function">(<span class="params">data</span>) =&gt;</span> &#123;</span><br><span class="line">  <span class="comment">//1.定义变量</span></span><br><span class="line">  <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">  <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line"></span><br><span class="line">  <span class="comment">//2.开始查找:只要current不指向null就一直循环</span></span><br><span class="line">  <span class="keyword">while</span> (current) &#123;</span><br><span class="line">    <span class="keyword">if</span> (current.<span class="property">data</span> == data) &#123;</span><br><span class="line">      <span class="keyword">return</span> index</span><br><span class="line">    &#125;</span><br><span class="line">    current = current.<span class="property">next</span></span><br><span class="line">    index += <span class="number">1</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">//3.遍历完链表没有找到，返回-1</span></span><br><span class="line">  <span class="keyword">return</span> -<span class="number">1</span></span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p>代码解析:</p>
<ul>
<li>代码 1 的位置还是定义需要的变量.</li>
<li>代码 2 的位置, 通过 while 循环获取节点</li>
<li>通过节点获取元素和 element 进行对比, 如果和传入 element 相同, 表示找到, 直接返回 index 即可.</li>
<li>如果没有找到, index++, 并且指向下一个节点.</li>
<li>到最后都没有找到, 说明链表中没有对应的元素, 那么返回-1 即可.</li>
</ul>
<p><strong>过程详解：</strong></p>
<p>indexOf 方法的实现过程：</p>
<ul>
<li>使用变量 current 记录当前指向的节点，使用变量 index 记录当前节点的索引值（注意 index = node 数-1）</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212225.png" alt="image-20200707212224834"></p>
<p><strong>测试代码：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//测试代码</span></span><br><span class="line"><span class="comment">//1.创建LinkList</span></span><br><span class="line"><span class="keyword">let</span> list = <span class="keyword">new</span> <span class="title class_">LinkList</span>()</span><br><span class="line"></span><br><span class="line"><span class="comment">//2.插入数据</span></span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;aaa&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;bbb&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;ccc&#x27;</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment">//3.测试indexOf方法</span></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">indexOf</span>(<span class="string">&#x27;aaa&#x27;</span>))</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">indexOf</span>(<span class="string">&#x27;ccc&#x27;</span>))</span><br></pre></td></tr></table></figure>

<p><strong>测试结果：</strong></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212237.png" alt="image-20200707212236303"></p>
<h3 id="update-position-element"><a href="#update-position-element" class="headerlink" title="update(position,element)"></a>update(position,element)</h3><p><strong>代码实现：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//实现update方法</span></span><br><span class="line"><span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">update</span> = <span class="function">(<span class="params">position, newData</span>) =&gt;</span> &#123;</span><br><span class="line">  <span class="comment">//1.越界判断</span></span><br><span class="line">  <span class="comment">//因为被修改的节点不能为null，所以position不能等于length</span></span><br><span class="line">  <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt;= <span class="variable language_">this</span>.<span class="property">length</span>) &#123;</span><br><span class="line">    <span class="keyword">return</span> <span class="literal">false</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="comment">//2.查找正确的节点</span></span><br><span class="line">  <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">  <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">  <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">    current = current.<span class="property">next</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="comment">//3.将position位置的后一个节点的data修改成newData</span></span><br><span class="line">  current.<span class="property">data</span> = newData</span><br><span class="line">  <span class="comment">//返回true表示修改成功</span></span><br><span class="line">  <span class="keyword">return</span> <span class="literal">true</span></span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p><strong>测试代码：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//测试代码</span></span><br><span class="line"><span class="comment">//1.创建LinkList</span></span><br><span class="line"><span class="keyword">let</span> list = <span class="keyword">new</span> <span class="title class_">LinkList</span>()</span><br><span class="line"></span><br><span class="line"><span class="comment">//2.插入数据</span></span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;aaa&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;bbb&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;ccc&#x27;</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment">//3.测试update方法</span></span><br><span class="line">list.<span class="title function_">update</span>(<span class="number">0</span>, <span class="string">&#x27;修改第一个节点&#x27;</span>)</span><br><span class="line">list.<span class="title function_">update</span>(<span class="number">1</span>, <span class="string">&#x27;修改第二个节点&#x27;</span>)</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list)</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">update</span>(<span class="number">3</span>, <span class="string">&#x27;能修改么&#x27;</span>))</span><br></pre></td></tr></table></figure>

<p><strong>测试结果：</strong></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212248.png" alt="image-20200707212247902"></p>
<h3 id="removeAt-position"><a href="#removeAt-position" class="headerlink" title="removeAt(position)"></a>removeAt(position)</h3><p><strong>代码实现：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//实现removeAt方法</span></span><br><span class="line"><span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">removeAt</span> = <span class="function">(<span class="params">position</span>) =&gt;</span> &#123;</span><br><span class="line">  <span class="comment">//1.越界判断</span></span><br><span class="line">  <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt;= <span class="variable language_">this</span>.<span class="property">length</span>) &#123;</span><br><span class="line">    <span class="comment">//position不能为length</span></span><br><span class="line">    <span class="keyword">return</span> <span class="literal">null</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="comment">//2.删除元素</span></span><br><span class="line">  <span class="comment">//情况1：position = 0时(删除第一个节点)</span></span><br><span class="line">  <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">  <span class="keyword">if</span> (position == <span class="number">0</span>) &#123;</span><br><span class="line">    <span class="comment">//情况2：position &gt; 0时</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">head</span> = <span class="variable language_">this</span>.<span class="property">head</span>.<span class="property">next</span></span><br><span class="line">  &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">    <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">    <span class="keyword">let</span> previous = <span class="literal">null</span></span><br><span class="line">    <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">      previous = current</span><br><span class="line">      current = current.<span class="property">next</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">//循环结束后，current指向position后一个节点，previous指向current前一个节点</span></span><br><span class="line">    <span class="comment">//再使前一个节点的next指向current的next即可</span></span><br><span class="line">    previous.<span class="property">next</span> = current.<span class="property">next</span></span><br><span class="line">  &#125;</span><br><span class="line">  <span class="comment">//3，length-1</span></span><br><span class="line">  <span class="variable language_">this</span>.<span class="property">length</span> -= <span class="number">1</span></span><br><span class="line"></span><br><span class="line">  <span class="comment">//返回被删除节点的data，为此current定义在最上面</span></span><br><span class="line">  <span class="keyword">return</span> current.<span class="property">data</span></span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p><strong>过程详解：</strong></p>
<p>removeAt 方法的实现过程：删除节点时存在多种情况：</p>
<ul>
<li><strong>情况 1：position = 0</strong>，即移除第一个节点（Node1）。</li>
</ul>
<p>通过：this.head = this.head.next，改变指向 1 即可；</p>
<p>虽然 Node1 的 next 仍指向 Node2，但是没有引用指向 Node1，则 Node1 会被垃圾回收器自动回收，所以不用处理 Node1 指向 Node2 的引用 next。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212301.png" alt="image-20200707212300727"></p>
<ul>
<li><strong>情况 2：positon &gt; 0</strong>，比如 pos = 2 即移除第三个节点（Node3）。</li>
</ul>
<p><strong>注意：</strong>position = length 时 position 后一个节点为 null 不能删除，因此 position != length；</p>
<p>首先，定义两个变量 previous 和 curent 分别指向需要删除位置 pos = x 的前一个节点和后一个节点；</p>
<p>然后，通过：previous.next = current.next，改变指向 1 即可；</p>
<p>随后，没有引用指向 Node3，Node3 就会被自动回收，至此成功删除 Node3 。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212316.png" alt="image-20200707212314952"></p>
<p><strong>测试代码：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//测试代码</span></span><br><span class="line"><span class="comment">//1.创建LinkList</span></span><br><span class="line"><span class="keyword">let</span> list = <span class="keyword">new</span> <span class="title class_">LinkList</span>()</span><br><span class="line"></span><br><span class="line"><span class="comment">//2.插入数据</span></span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;aaa&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;bbb&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;ccc&#x27;</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment">//3.测试removeAt方法</span></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">removeAt</span>(<span class="number">0</span>))</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">removeAt</span>(<span class="number">0</span>))</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list)</span><br></pre></td></tr></table></figure>

<p><strong>测试结果：</strong></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212328.png" alt="image-20200707212327149"></p>
<h3 id="其他方法"><a href="#其他方法" class="headerlink" title="其他方法"></a>其他方法</h3><p>其他方法包括：<strong>remove(element)、isEmpty()、size()</strong></p>
<p><strong>代码实现：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">/*-------------其他方法的实现--------------*/</span></span><br><span class="line"><span class="comment">//一.实现remove方法</span></span><br><span class="line"><span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">remove</span> = <span class="function">(<span class="params">data</span>) =&gt;</span> &#123;</span><br><span class="line">  <span class="comment">//1.获取data在列表中的位置</span></span><br><span class="line">  <span class="keyword">let</span> position = <span class="variable language_">this</span>.<span class="title function_">indexOf</span>(data)</span><br><span class="line">  <span class="comment">//2.根据位置信息，删除结点</span></span><br><span class="line">  <span class="keyword">return</span> <span class="variable language_">this</span>.<span class="title function_">removeAt</span>(position)</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="comment">//二.实现isEmpty方法</span></span><br><span class="line"><span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">isEmpty</span> = <span class="function">() =&gt;</span> &#123;</span><br><span class="line">  <span class="keyword">return</span> <span class="variable language_">this</span>.<span class="property">length</span> == <span class="number">0</span></span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="comment">//三.实现size方法</span></span><br><span class="line"><span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">size</span> = <span class="function">() =&gt;</span> &#123;</span><br><span class="line">  <span class="keyword">return</span> <span class="variable language_">this</span>.<span class="property">length</span></span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p><strong>测试代码：</strong></p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">//测试代码</span></span><br><span class="line"><span class="comment">//1.创建LinkList</span></span><br><span class="line"><span class="keyword">let</span> list = <span class="keyword">new</span> <span class="title class_">LinkList</span>()</span><br><span class="line"></span><br><span class="line"><span class="comment">//2.插入数据</span></span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;aaa&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;bbb&#x27;</span>)</span><br><span class="line">list.<span class="title function_">append</span>(<span class="string">&#x27;ccc&#x27;</span>)</span><br><span class="line"></span><br><span class="line"><span class="comment">/*---------------其他方法测试----------------*/</span></span><br><span class="line"><span class="comment">//remove方法</span></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">remove</span>(<span class="string">&#x27;aaa&#x27;</span>))</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list)</span><br><span class="line"><span class="comment">//isEmpty方法</span></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">isEmpty</span>())</span><br><span class="line"><span class="comment">//size方法</span></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(list.<span class="title function_">size</span>())</span><br></pre></td></tr></table></figure>

<p><strong>测试结果：</strong></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200707212340.png" alt="image-20200707212339289"></p>
<h3 id="完整实现"><a href="#完整实现" class="headerlink" title="完整实现"></a>完整实现</h3><h4 id="使用-ES5-实现"><a href="#使用-ES5-实现" class="headerlink" title="使用 ES5 实现"></a>使用 ES5 实现</h4><figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br><span class="line">124</span><br><span class="line">125</span><br><span class="line">126</span><br><span class="line">127</span><br><span class="line">128</span><br><span class="line">129</span><br><span class="line">130</span><br><span class="line">131</span><br><span class="line">132</span><br><span class="line">133</span><br><span class="line">134</span><br><span class="line">135</span><br><span class="line">136</span><br><span class="line">137</span><br><span class="line">138</span><br><span class="line">139</span><br><span class="line">140</span><br><span class="line">141</span><br><span class="line">142</span><br><span class="line">143</span><br><span class="line">144</span><br><span class="line">145</span><br><span class="line">146</span><br><span class="line">147</span><br><span class="line">148</span><br><span class="line">149</span><br><span class="line">150</span><br><span class="line">151</span><br><span class="line">152</span><br><span class="line">153</span><br><span class="line">154</span><br><span class="line">155</span><br><span class="line">156</span><br><span class="line">157</span><br><span class="line">158</span><br><span class="line">159</span><br><span class="line">160</span><br><span class="line">161</span><br><span class="line">162</span><br><span class="line">163</span><br><span class="line">164</span><br><span class="line">165</span><br><span class="line">166</span><br><span class="line">167</span><br><span class="line">168</span><br><span class="line">169</span><br><span class="line">170</span><br><span class="line">171</span><br><span class="line">172</span><br><span class="line">173</span><br><span class="line">174</span><br><span class="line">175</span><br><span class="line">176</span><br><span class="line">177</span><br><span class="line">178</span><br><span class="line">179</span><br><span class="line">180</span><br><span class="line">181</span><br><span class="line">182</span><br><span class="line">183</span><br><span class="line">184</span><br><span class="line">185</span><br><span class="line">186</span><br><span class="line">187</span><br><span class="line">188</span><br><span class="line">189</span><br><span class="line">190</span><br><span class="line">191</span><br><span class="line">192</span><br><span class="line">193</span><br><span class="line">194</span><br><span class="line">195</span><br><span class="line">196</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 封装链表类</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">LinkList</span>(<span class="params"></span>) &#123;</span><br><span class="line">  <span class="comment">// 封装一个内部类：节点类</span></span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">Node</span>(<span class="params">data</span>) &#123;</span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">data</span> = data</span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">next</span> = <span class="literal">null</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 属性</span></span><br><span class="line">  <span class="comment">// 属性head指向链表的第一个节点</span></span><br><span class="line">  <span class="variable language_">this</span>.<span class="property">head</span> = <span class="literal">null</span></span><br><span class="line">  <span class="variable language_">this</span>.<span class="property">length</span> = <span class="number">0</span></span><br><span class="line"></span><br><span class="line">  <span class="comment">// 一.实现append方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">append</span> = <span class="function">(<span class="params">data</span>) =&gt;</span> &#123;</span><br><span class="line">    <span class="comment">//1.创建新节点</span></span><br><span class="line">    <span class="keyword">let</span> newNode = <span class="keyword">new</span> <span class="title class_">Node</span>(data)</span><br><span class="line"></span><br><span class="line">    <span class="comment">//2.添加新节点</span></span><br><span class="line">    <span class="comment">//情况1：只有一个节点时候</span></span><br><span class="line">    <span class="keyword">if</span> (<span class="variable language_">this</span>.<span class="property">length</span> == <span class="number">0</span>) &#123;</span><br><span class="line">      <span class="variable language_">this</span>.<span class="property">head</span> = newNode</span><br><span class="line">      <span class="comment">//情况2：节点数大于1，在链表的最后添加新节点</span></span><br><span class="line">    &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">      <span class="comment">//让变量current指向第一个节点</span></span><br><span class="line">      <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">      <span class="comment">//当current.next(下一个节点不为空)不为空时，一直循环，直到current指向最后一个节点</span></span><br><span class="line">      <span class="keyword">while</span> (current.<span class="property">next</span>) &#123;</span><br><span class="line">        current = current.<span class="property">next</span></span><br><span class="line">      &#125;</span><br><span class="line">      <span class="comment">// 最后节点的next指向新的节点</span></span><br><span class="line">      current.<span class="property">next</span> = newNode</span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">//3.添加完新结点之后length+1</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">length</span> += <span class="number">1</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 二.实现toString方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">toString</span> = <span class="function">() =&gt;</span> &#123;</span><br><span class="line">    <span class="comment">// 1.定义变量</span></span><br><span class="line">    <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="keyword">let</span> listString = <span class="string">&#x27;&#x27;</span></span><br><span class="line"></span><br><span class="line">    <span class="comment">// 2.循环获取一个个的节点</span></span><br><span class="line">    <span class="keyword">while</span> (current) &#123;</span><br><span class="line">      listString += current.<span class="property">data</span> + <span class="string">&#x27; &#x27;</span></span><br><span class="line">      current = current.<span class="property">next</span> <span class="comment">//千万不要忘了拼接完一个节点数据之后，让current指向下一个节点</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="keyword">return</span> listString</span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// 三.实现insert方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">insert</span> = <span class="function">(<span class="params">position, data</span>) =&gt;</span> &#123;</span><br><span class="line">    <span class="comment">//理解positon的含义：position=0表示新界点插入后要成为第1个节点，position=2表示新界点插入后要成为第3个节点</span></span><br><span class="line">    <span class="comment">//1.对position进行越界判断:要求传入的position不能是负数且不能超过LinkList的length</span></span><br><span class="line">    <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt; <span class="variable language_">this</span>.<span class="property">length</span>) &#123;</span><br><span class="line">      <span class="keyword">return</span> <span class="literal">false</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">//2.根据data创建newNode</span></span><br><span class="line">    <span class="keyword">let</span> newNode = <span class="keyword">new</span> <span class="title class_">Node</span>(data)</span><br><span class="line"></span><br><span class="line">    <span class="comment">//3.插入新节点</span></span><br><span class="line">    <span class="comment">//情况1：插入位置position=0</span></span><br><span class="line">    <span class="keyword">if</span> (position == <span class="number">0</span>) &#123;</span><br><span class="line">      <span class="comment">// 让新节点指向第一个节点</span></span><br><span class="line">      newNode.<span class="property">next</span> = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">      <span class="comment">// 让head指向新节点</span></span><br><span class="line">      <span class="variable language_">this</span>.<span class="property">head</span> = newNode</span><br><span class="line">      <span class="comment">//情况2：插入位置position&gt;0(该情况包含position=length)</span></span><br><span class="line">    &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">      <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">      <span class="keyword">let</span> previous = <span class="literal">null</span></span><br><span class="line">      <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">      <span class="comment">//步骤1：通过while循环使变量current指向position位置的后一个节点(注意while循环的写法)</span></span><br><span class="line">      <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">        <span class="comment">//步骤2：在current指向下一个节点之前，让previous指向current当前指向的节点</span></span><br><span class="line">        previous = current</span><br><span class="line">        current = current.<span class="property">next</span></span><br><span class="line">      &#125;</span><br><span class="line">      <span class="comment">// 步骤3：通过变量current(此时current已经指向position位置的后一个节点)，使newNode指向position位置的后一个节点</span></span><br><span class="line">      newNode.<span class="property">next</span> = current</span><br><span class="line">      <span class="comment">//步骤4：通过变量previous，使position位置的前一个节点指向newNode</span></span><br><span class="line">      previous.<span class="property">next</span> = newNode</span><br><span class="line"></span><br><span class="line">      <span class="comment">//我们无法直接操作链表中的节点，但是可以通过变量指向这些节点，以此间接地操作节点；</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">//4.新节点插入后要length+1</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">length</span> += <span class="number">1</span></span><br><span class="line"></span><br><span class="line">    <span class="keyword">return</span> <span class="literal">true</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">//四.实现get方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">get</span> = <span class="function">(<span class="params">position</span>) =&gt;</span> &#123;</span><br><span class="line">    <span class="comment">//1.越界判断</span></span><br><span class="line">    <span class="comment">// 当position = length时，取到的是null所以0 =&lt; position &lt; length</span></span><br><span class="line">    <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt;= <span class="variable language_">this</span>.<span class="property">length</span>) &#123;</span><br><span class="line">      <span class="keyword">return</span> <span class="literal">null</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">//2.获取指定的positon位置的后一个节点的data</span></span><br><span class="line">    <span class="comment">//同样使用一个变量间接操作节点</span></span><br><span class="line">    <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">    <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">      current = current.<span class="property">next</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="keyword">return</span> current.<span class="property">data</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">//五.实现indexOf方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">indexOf</span> = <span class="function">(<span class="params">data</span>) =&gt;</span> &#123;</span><br><span class="line">    <span class="comment">//1.定义变量</span></span><br><span class="line">    <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line"></span><br><span class="line">    <span class="comment">//2.开始查找:只要current不指向null就一直循环</span></span><br><span class="line">    <span class="keyword">while</span> (current) &#123;</span><br><span class="line">      <span class="keyword">if</span> (current.<span class="property">data</span> == data) &#123;</span><br><span class="line">        <span class="keyword">return</span> index</span><br><span class="line">      &#125;</span><br><span class="line">      current = current.<span class="property">next</span></span><br><span class="line">      index += <span class="number">1</span></span><br><span class="line">    &#125;</span><br><span class="line"></span><br><span class="line">    <span class="comment">//3.遍历完链表没有找到，返回-1</span></span><br><span class="line">    <span class="keyword">return</span> -<span class="number">1</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">//六.实现update方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">update</span> = <span class="function">(<span class="params">position, newData</span>) =&gt;</span> &#123;</span><br><span class="line">    <span class="comment">//1.越界判断</span></span><br><span class="line">    <span class="comment">//因为被修改的节点不能为null，所以position不能等于length</span></span><br><span class="line">    <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt;= <span class="variable language_">this</span>.<span class="property">length</span>) &#123;</span><br><span class="line">      <span class="keyword">return</span> <span class="literal">false</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">//2.查找正确的节点</span></span><br><span class="line">    <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">    <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">      current = current.<span class="property">next</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">//3.将position位置的后一个节点的data修改成newData</span></span><br><span class="line">    current.<span class="property">data</span> = newData</span><br><span class="line">    <span class="comment">//返回true表示修改成功</span></span><br><span class="line">    <span class="keyword">return</span> <span class="literal">true</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">//七.实现removeAt方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">removeAt</span> = <span class="function">(<span class="params">position</span>) =&gt;</span> &#123;</span><br><span class="line">    <span class="comment">//1.越界判断</span></span><br><span class="line">    <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt;= <span class="variable language_">this</span>.<span class="property">length</span>) &#123;</span><br><span class="line">      <span class="keyword">return</span> <span class="literal">null</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">//2.删除元素</span></span><br><span class="line">    <span class="comment">//情况1：position = 0时(删除第一个节点)</span></span><br><span class="line">    <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="keyword">if</span> (position == <span class="number">0</span>) &#123;</span><br><span class="line">      <span class="comment">//情况2：position &gt; 0时</span></span><br><span class="line">      <span class="variable language_">this</span>.<span class="property">head</span> = <span class="variable language_">this</span>.<span class="property">head</span>.<span class="property">next</span></span><br><span class="line">    &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">      <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">      <span class="keyword">let</span> previous = <span class="literal">null</span></span><br><span class="line">      <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">        previous = current</span><br><span class="line">        current = current.<span class="property">next</span></span><br><span class="line">      &#125;</span><br><span class="line">      <span class="comment">//循环结束后，current指向position后一个节点，previous指向current前一个节点</span></span><br><span class="line">      <span class="comment">//再使前一个节点的next指向current的next即可</span></span><br><span class="line">      previous.<span class="property">next</span> = current.<span class="property">next</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="comment">//3，length-1</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">length</span> -= <span class="number">1</span></span><br><span class="line"></span><br><span class="line">    <span class="comment">//返回被删除节点的data，为此current定义在最上面</span></span><br><span class="line">    <span class="keyword">return</span> current.<span class="property">data</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">/*-------------其他方法的实现--------------*/</span></span><br><span class="line">  <span class="comment">//八.实现remove方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">remove</span> = <span class="function">(<span class="params">data</span>) =&gt;</span> &#123;</span><br><span class="line">    <span class="comment">//1.获取data在列表中的位置</span></span><br><span class="line">    <span class="keyword">let</span> position = <span class="variable language_">this</span>.<span class="title function_">indexOf</span>(data)</span><br><span class="line">    <span class="comment">//2.根据位置信息，删除结点</span></span><br><span class="line">    <span class="keyword">return</span> <span class="variable language_">this</span>.<span class="title function_">removeAt</span>(position)</span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">//九.实现isEmpty方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">isEmpty</span> = <span class="function">() =&gt;</span> &#123;</span><br><span class="line">    <span class="keyword">return</span> <span class="variable language_">this</span>.<span class="property">length</span> == <span class="number">0</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">//十.实现size方法</span></span><br><span class="line">  <span class="title class_">LinkList</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">size</span> = <span class="function">() =&gt;</span> &#123;</span><br><span class="line">    <span class="keyword">return</span> <span class="variable language_">this</span>.<span class="property">length</span></span><br><span class="line">  &#125;</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<h4 id="使用-ES6-实现"><a href="#使用-ES6-实现" class="headerlink" title="使用 ES6 实现"></a>使用 ES6 实现</h4><figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br><span class="line">87</span><br><span class="line">88</span><br><span class="line">89</span><br><span class="line">90</span><br><span class="line">91</span><br><span class="line">92</span><br><span class="line">93</span><br><span class="line">94</span><br><span class="line">95</span><br><span class="line">96</span><br><span class="line">97</span><br><span class="line">98</span><br><span class="line">99</span><br><span class="line">100</span><br><span class="line">101</span><br><span class="line">102</span><br><span class="line">103</span><br><span class="line">104</span><br><span class="line">105</span><br><span class="line">106</span><br><span class="line">107</span><br><span class="line">108</span><br><span class="line">109</span><br><span class="line">110</span><br><span class="line">111</span><br><span class="line">112</span><br><span class="line">113</span><br><span class="line">114</span><br><span class="line">115</span><br><span class="line">116</span><br><span class="line">117</span><br><span class="line">118</span><br><span class="line">119</span><br><span class="line">120</span><br><span class="line">121</span><br><span class="line">122</span><br><span class="line">123</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 单向链表</span></span><br><span class="line"><span class="keyword">export</span> <span class="keyword">class</span> <span class="title class_">Node</span> &#123;</span><br><span class="line">  <span class="title function_">constructor</span>(<span class="params">element</span>) &#123;</span><br><span class="line">    <span class="comment">// 保存元素</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">element</span> = element</span><br><span class="line">    <span class="comment">// 指向下一个节点</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">next</span> = <span class="literal">null</span></span><br><span class="line">  &#125;</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="keyword">export</span> <span class="keyword">class</span> <span class="title class_">LinkedList</span> &#123;</span><br><span class="line">  <span class="title function_">constructor</span>(<span class="params"></span>) &#123;</span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">head</span> = <span class="literal">null</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">length</span> = <span class="number">0</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="title function_">append</span>(<span class="params">element</span>) &#123;</span><br><span class="line">    <span class="keyword">const</span> newNode = <span class="keyword">new</span> <span class="title class_">Node</span>(element)</span><br><span class="line">    <span class="keyword">if</span> (!<span class="variable language_">this</span>.<span class="property">head</span>) &#123;</span><br><span class="line">      <span class="variable language_">this</span>.<span class="property">head</span> = newNode</span><br><span class="line">    &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">      <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">      <span class="keyword">while</span> (current.<span class="property">next</span>) &#123;</span><br><span class="line">        current = current.<span class="property">next</span></span><br><span class="line">      &#125;</span><br><span class="line">      current.<span class="property">next</span> = newNode</span><br><span class="line">    &#125;</span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">length</span>++</span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="title function_">insert</span>(<span class="params">position, element</span>) &#123;</span><br><span class="line">    <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt; <span class="variable language_">this</span>.<span class="property">length</span> - <span class="number">1</span>) <span class="keyword">return</span> <span class="literal">false</span></span><br><span class="line">    <span class="keyword">const</span> newNode = <span class="keyword">new</span> <span class="title class_">Node</span>(element)</span><br><span class="line">    <span class="keyword">if</span> (position === <span class="number">0</span>) &#123;</span><br><span class="line">      newNode.<span class="property">next</span> = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">      <span class="variable language_">this</span>.<span class="property">head</span> = newNode</span><br><span class="line">    &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">      <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">      <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">      <span class="keyword">let</span> previous = <span class="literal">null</span></span><br><span class="line">      <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">        previous = current</span><br><span class="line">        current = current.<span class="property">next</span></span><br><span class="line">      &#125;</span><br><span class="line">      previous.<span class="property">next</span> = newNode</span><br><span class="line">      newNode.<span class="property">next</span> = current</span><br><span class="line">    &#125;</span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">length</span>++</span><br><span class="line">    <span class="keyword">return</span> <span class="literal">true</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="title function_">get</span>(<span class="params">position</span>) &#123;</span><br><span class="line">    <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt; <span class="variable language_">this</span>.<span class="property">length</span> - <span class="number">1</span>) <span class="keyword">return</span> <span class="literal">null</span></span><br><span class="line">    <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">    <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">      current = current.<span class="property">next</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="keyword">return</span> current.<span class="property">element</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="title function_">indexOf</span>(<span class="params">element</span>) &#123;</span><br><span class="line">    <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">    <span class="keyword">while</span> (current) &#123;</span><br><span class="line">      <span class="keyword">if</span> (current.<span class="property">element</span> === element) &#123;</span><br><span class="line">        <span class="keyword">return</span> index</span><br><span class="line">      &#125;</span><br><span class="line">      index++</span><br><span class="line">      current = current.<span class="property">next</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="keyword">return</span> -<span class="number">1</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="title function_">removeAt</span>(<span class="params">position</span>) &#123;</span><br><span class="line">    <span class="keyword">if</span> (position &lt; <span class="number">0</span> || position &gt; <span class="variable language_">this</span>.<span class="property">length</span> - <span class="number">1</span>) <span class="keyword">return</span> <span class="literal">null</span></span><br><span class="line">    <span class="keyword">let</span> current = <span class="variable language_">this</span>.<span class="property">head</span></span><br><span class="line">    <span class="keyword">if</span> (position === <span class="number">0</span>) &#123;</span><br><span class="line">      <span class="variable language_">this</span>.<span class="property">head</span> = current.<span class="property">next</span></span><br><span class="line">    &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">      <span class="keyword">let</span> index = <span class="number">0</span></span><br><span class="line">      <span class="keyword">let</span> previous = <span class="literal">null</span></span><br><span class="line">      <span class="keyword">while</span> (index++ &lt; position) &#123;</span><br><span class="line">        previous = current</span><br><span class="line">        current = current.<span class="property">next</span></span><br><span class="line">      &#125;</span><br><span class="line">      previous.<span class="property">next</span> = current.<span class="property">next</span></span><br><span class="line">    &#125;</span><br><span class="line">    <span class="variable language_">this</span>.<span class="property">length</span>--</span><br><span class="line">    <span class="keyword">return</span> current.<span class="property">element</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="comment">// update (position, element) &#123;</span></span><br><span class="line">  <span class="comment">//   if (position &lt; 0 || position &gt;= this.length) return false</span></span><br><span class="line">  <span class="comment">//   let current = this.head</span></span><br><span class="line">  <span class="comment">//   let index = 0</span></span><br><span class="line">  <span class="comment">//   while (index++ &lt; position) &#123;</span></span><br><span class="line">  <span class="comment">//     current = current.next</span></span><br><span class="line">  <span class="comment">//   &#125;</span></span><br><span class="line">  <span class="comment">//   current.element = element</span></span><br><span class="line">  <span class="comment">//   return true</span></span><br><span class="line">  <span class="comment">// &#125;</span></span><br><span class="line"></span><br><span class="line">  <span class="title function_">update</span>(<span class="params">position, element</span>) &#123;</span><br><span class="line">    <span class="keyword">const</span> result = <span class="variable language_">this</span>.<span class="title function_">removeAt</span>(position)</span><br><span class="line">    <span class="variable language_">this</span>.<span class="title function_">insert</span>(position, element)</span><br><span class="line">    <span class="keyword">return</span> result</span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="title function_">remove</span>(<span class="params"></span>) &#123;</span><br><span class="line">    <span class="keyword">const</span> index = <span class="variable language_">this</span>.<span class="title function_">indexOf</span>(element)</span><br><span class="line">    <span class="keyword">if</span> (index === -<span class="number">1</span>) <span class="keyword">return</span></span><br><span class="line">    <span class="variable language_">this</span>.<span class="title function_">removeAt</span>(index)</span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="title function_">isEmpty</span>(<span class="params"></span>) &#123;</span><br><span class="line">    <span class="keyword">return</span> <span class="variable language_">this</span>.<span class="property">length</span> === <span class="number">0</span></span><br><span class="line">  &#125;</span><br><span class="line"></span><br><span class="line">  <span class="title function_">size</span>(<span class="params"></span>) &#123;</span><br><span class="line">    <span class="keyword">return</span> <span class="variable language_">this</span>.<span class="property">length</span></span><br><span class="line">  &#125;</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/292a04a3.html">http://blog.mhy.loc.cc/archives/292a04a3.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/JavaScript/">JavaScript</a><a class="post-meta__tags" href="/tags/%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84/">数据结构</a><a class="post-meta__tags" href="/tags/%E7%AE%97%E6%B3%95/">算法</a><a class="post-meta__tags" href="/tags/%E5%8D%95%E5%90%91%E9%93%BE%E8%A1%A8/">单向链表</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/353666f0.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200719133158.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">给你的博客添加一个收藏页</div></div></a></div><div class="next-post pull-right"><a href="/archives/14862ea.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">JavaScript数据结构和算法：队列</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/14862ea.html" title="JavaScript数据结构和算法：队列"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-03</div><div class="title">JavaScript数据结构和算法：队列</div></div></a></div><div><a href="/archives/d343d10e.html" title="JavaScript数据结构和算法：栈结构"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-06-28</div><div class="title">JavaScript数据结构和算法：栈结构</div></div></a></div><div><a href="/archives/838e5b66.html" title="JavaScript数据结构和算法：数组"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-06-25</div><div class="title">JavaScript数据结构和算法：数组</div></div></a></div><div><a href="/archives/2f89d13b.html" title="练习题"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-04-12</div><div class="title">练习题</div></div></a></div><div><a href="/archives/b2cd753e.html" title="ES6标准入门(十)：class类中constructor和name问题"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185259.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-11-03</div><div class="title">ES6标准入门(十)：class类中constructor和name问题</div></div></a></div><div><a href="/archives/16f8f18e.html" title="ES6标准入门(九)：Proxy"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185259.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-10-28</div><div class="title">ES6标准入门(九)：Proxy</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%8D%95%E5%90%91%E9%93%BE%E8%A1%A8%E7%AE%80%E4%BB%8B"><span class="toc-text">单向链表简介</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%95%B0%E7%BB%84%E5%AD%98%E5%9C%A8%E7%9A%84%E7%BC%BA%E7%82%B9"><span class="toc-text">数组存在的缺点</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%93%BE%E8%A1%A8%E7%9A%84%E4%BC%98%E5%8A%BF"><span class="toc-text">链表的优势</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%93%BE%E8%A1%A8%E4%B8%AD%E7%9A%84%E5%B8%B8%E8%A7%81%E6%93%8D%E4%BD%9C"><span class="toc-text">链表中的常见操作</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%B0%81%E8%A3%85%E5%8D%95%E5%90%91%E9%93%BE%E8%A1%A8%E7%B1%BB"><span class="toc-text">封装单向链表类</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%88%9B%E5%BB%BA%E5%8D%95%E5%90%91%E9%93%BE%E8%A1%A8%E7%B1%BB"><span class="toc-text">创建单向链表类</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#append-element"><span class="toc-text">append(element)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#toString-NaN"><span class="toc-text">toString()</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#insert-position-element"><span class="toc-text">insert(position,element)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#get-position"><span class="toc-text">get(position)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#indexOf-element"><span class="toc-text">indexOf(element)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#update-position-element"><span class="toc-text">update(position,element)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#removeAt-position"><span class="toc-text">removeAt(position)</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%85%B6%E4%BB%96%E6%96%B9%E6%B3%95"><span class="toc-text">其他方法</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AE%8C%E6%95%B4%E5%AE%9E%E7%8E%B0"><span class="toc-text">完整实现</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%BD%BF%E7%94%A8-ES5-%E5%AE%9E%E7%8E%B0"><span class="toc-text">使用 ES5 实现</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%BD%BF%E7%94%A8-ES6-%E5%AE%9E%E7%8E%B0"><span class="toc-text">使用 ES6 实现</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>