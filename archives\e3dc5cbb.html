<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Hexo框架(一)：使用Hexo快速搭建个人博客 | 你真是一个美好的人类</title><meta name="keywords" content="GitBash安装,博客搭建,Hexo,Node.js安装"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="（1）搭建博客的前期准备工作；（2）使用Hexo快速搭建博客；">
<meta property="og:type" content="article">
<meta property="og:title" content="Hexo框架(一)：使用Hexo快速搭建个人博客">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/e3dc5cbb.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="（1）搭建博客的前期准备工作；（2）使用Hexo快速搭建博客；">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png">
<meta property="article:published_time" content="2020-02-16T07:25:13.000Z">
<meta property="article:modified_time" content="2020-02-16T07:25:13.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="GitBash安装">
<meta property="article:tag" content="博客搭建">
<meta property="article:tag" content="Hexo">
<meta property="article:tag" content="Node.js安装">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/e3dc5cbb"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Hexo框架(一)：使用Hexo快速搭建个人博客',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-02-16 07:25:13'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Hexo框架(一)：使用Hexo快速搭建个人博客</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-02-16T07:25:13.000Z" title="发表于 2020-02-16 07:25:13">2020-02-16</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-02-16T07:25:13.000Z" title="更新于 2020-02-16 07:25:13">2020-02-16</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><div class="note danger flat"><h2 id="前言："><a href="#前言：" class="headerlink" title="前言："></a>前言：</h2><p>本篇设置基于 NEXT 主题 7.7.2 版本！！</p>
</div>

<p>平时自己也会做很多笔记，把一些学习和开发中的经验心得和踩过的一些坑及解决办法写下来，但是笔记都太多零散，不方便整理成册，而且因为电脑原因还丢失了很多笔记，所以就想写一个博客。</p>
<p>但是之前所使用的一些博客或者记录网站，都不太符合自己的心意，要么广告太多，要么太过繁琐。所以我觉得搭建一个属于自己的博客。</p>
<p>Hexo 是一个简单、快速、强大的基于 Github Pages 的博客发布工具，支持 Markdown 格式，有众多优秀插件和主题。本篇使用 Hexo + GitHub Pages 在 windows 系统快速搭建一个免费个人博客。</p>
<h2 id="前期准备工作"><a href="#前期准备工作" class="headerlink" title="前期准备工作"></a>前期准备工作</h2><h3 id="安装-Git-Bash"><a href="#安装-Git-Bash" class="headerlink" title="安装 Git Bash"></a>安装 Git Bash</h3><p>安装教程你可以看这里：<a href="/archives/616a84f2.html">Git 安装教程</a></p>
<h3 id="安装-Node-js"><a href="#安装-Node-js" class="headerlink" title="安装 Node.js"></a>安装 Node.js</h3><p>安装教程你可以看这里：<a href="/archives/4f1f2f20.html">Node.js 安装教程</a></p>
<h3 id="配置-Github-仓库"><a href="#配置-Github-仓库" class="headerlink" title="配置 Github 仓库"></a>配置 Github 仓库</h3><p>关于 GitHub 注册相关这里就不赘述了。</p>
<h4 id="新建仓库"><a href="#新建仓库" class="headerlink" title="新建仓库"></a>新建仓库</h4><ul>
<li>新建一个名为<code>你的用户名.github.io</code>的仓库，如果你的 github 用户名是<code>test</code>，那么你就新建<code>test.github.io</code>的仓库（必须是你的用户名，其它名称无效），将来你的网站访问地址就是 <code>http://test.github.io</code> 了.</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205017.png" alt="新建仓库"></p>
<ul>
<li><p>仓库名字必须是：<code>username.github.io</code>，其中<code>username</code>是你的用户名,然后点击 Create repository；</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205025.png" alt="创建仓库"></p>
</li>
<li><p>创建成功：</p>
</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205033.png" alt="创建完成"></p>
<h4 id="配置-SSH-key"><a href="#配置-SSH-key" class="headerlink" title="配置 SSH key"></a>配置 SSH key</h4><ul>
<li>回到 GitBash 中，配置 GitHub 账户信息（YourName 和 YourEail 都替换成你自己的）：</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205042.png" alt="image-20200318210215347"></p>
<ul>
<li><p>创建 SSH。</p>
</li>
<li><p>在 GitBash 中输入命令(替换成你 GitHub 绑定的邮箱)，回车三次。</p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">ssh<span class="literal">-keygen</span> <span class="literal">-t</span> rsa <span class="literal">-C</span> <span class="string">&quot;<EMAIL>&quot;</span></span><br></pre></td></tr></table></figure>

</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205053.png" alt="配置"></p>
<ul>
<li>出现以下界面：</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205058.png" alt="成功"></p>
<ul>
<li>找到红框中的路径，用记事本打开<code>id_rsa.pub</code>文件，复制里面的内容。</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205107.png" alt="路径"></p>
<ul>
<li>返回 GitHub，打开这个设置：</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205114.png" alt="设置"></p>
<ul>
<li>选择 SSH and GPG keys，然后点击 New SSHkey</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205123.png" alt="新建SSH key"></p>
<ul>
<li>把你复制的记事本的内容填到这里，然后点击 Add SSH key：</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205133.png" alt="image-20200318211835432"></p>
<ul>
<li><p>检查是否成功：</p>
<pre><code>在GitBash中输入命令：

<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"></span><br><span class="line">ssh <span class="literal">-T</span> **************</span><br></pre></td></tr></table></figure>

如果提示:

<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">Are you sure you want to <span class="keyword">continue</span> connecting (yes/no)?</span><br></pre></td></tr></table></figure>

输入：yes。

如果看到这样。说明SSH配置成功！
</code></pre>
</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205145.png" alt="配置成功"></p>
<h3 id="代码编辑器"><a href="#代码编辑器" class="headerlink" title="代码编辑器"></a>代码编辑器</h3><p>我选择使用 VSCode，关于代码编辑器相关的东西，这里不再赘述。</p>
<h2 id="使用-Hexo-搭建博客"><a href="#使用-Hexo-搭建博客" class="headerlink" title="使用 Hexo 搭建博客"></a>使用 Hexo 搭建博客</h2><h3 id="安装-Hexo"><a href="#安装-Hexo" class="headerlink" title="安装 Hexo"></a>安装 Hexo</h3><ul>
<li>1.创建一个文件夹，我们可以命名为 Blog，将这个文件夹在 VSCode 中打开，并打开 VSCode 终端。</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205153.png" alt="vscode"></p>
<ul>
<li>2.在终端中输入<code>npm install -g hexo</code> 进行全局安装。</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205201.png" alt="输入"></p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205209.png" alt="image-20200318213600276"></p>
<ul>
<li>3.初始化项目：在终端中输入<code>hexo init</code>，初始化完成后项目结构如图：</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320205220.png" alt="image-20200318214545681"></p>
<ul>
<li>如果文件夹内没有 node_modules 文件夹，我们需要安装一下依赖，输入命令：<code>npm install</code> 进行安装。</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320210653.png" alt="image-20200318214841319"></p>
<ul>
<li>安装完成后，我们可以运行<code>hexo g</code></li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320210703.png" alt="image-20200318214932858"></p>
<ul>
<li>然后运行<code>hexo s</code>，即可本地预览博客。</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320210713.png" alt="image-20200318215039717"></p>


<ul>
<li>浏览器输入 <a target="_blank" rel="noopener external nofollow noreferrer" href="http://localhost:4000/">http://localhost:4000/</a> ，查看效果。出现这个界面，表示成功！</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320210725.png" alt="image-20200318215136343"></p>
<h3 id="上传到-GitHub"><a href="#上传到-GitHub" class="headerlink" title="上传到 GitHub"></a>上传到 GitHub</h3><ul>
<li>首先打开 Blog 根目录下的<code>_config.yml</code>文件，翻到最后进行如下配置(替换你自己的 GitHub 名字)：</li>
</ul>
<figure class="highlight yml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">deploy:</span></span><br><span class="line">  <span class="attr">type:</span> <span class="string">git</span></span><br><span class="line">  <span class="attr">repo:</span> <span class="string">https://github.com/YourgithubName/YourgithubName.github.io.git</span></span><br><span class="line">  <span class="attr">branch:</span> <span class="string">master</span></span><br></pre></td></tr></table></figure>

<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320210736.png" alt="image-20200318215528546"></p>
<ul>
<li>在终端执行命令安装一下插件：</li>
</ul>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install hexo<span class="literal">-deployer-git</span> <span class="literal">--save</span></span><br></pre></td></tr></table></figure>

<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320210746.png" alt="image-20200318215809920"></p>
<p>（我使用了淘宝镜像，所以是 cnpm。与 npm 没有什么区别。）</p>
<ul>
<li>然后我们依次执行一下这 3 个命令<ul>
<li><code>hexo clean</code></li>
<li><code>hexo g</code></li>
<li><code>hexo d</code></li>
</ul>
</li>
<li>如果在过程中弹出提示要求你输入 GitHub 的账户和密码，输入即可，最后弹出：</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320210754.png" alt="image-20200318220305772"></p>
<ul>
<li>打开浏览器，输入你的地址：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://yourname.github.io/%EF%BC%8C%E5%8D%B3%E5%8F%AF%E7%9C%8B%E5%88%B0%E6%95%88%E6%9E%9C%E3%80%82%E8%87%B3%E6%AD%A4%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA%E5%AE%8C%E6%AF%95%E3%80%82">https://YourName.github.io/，即可看到效果。至此博客搭建完毕。</a></li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320210803.png" alt="image-20200318220503520"></p>
<h2 id="结语"><a href="#结语" class="headerlink" title="结语"></a>结语</h2><p>使用 Hexo + GitHub Pages 搭建一个博客的基础工作到这里就完成了，后面将详细介绍如何使用及个性化设置。</p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/e3dc5cbb.html">http://blog.mhy.loc.cc/archives/e3dc5cbb.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/GitBash%E5%AE%89%E8%A3%85/">GitBash安装</a><a class="post-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a><a class="post-meta__tags" href="/tags/Hexo/">Hexo</a><a class="post-meta__tags" href="/tags/Node-js%E5%AE%89%E8%A3%85/">Node.js安装</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/e8460ade.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Hexo框架(五)：将SEO描述作为简介</div></div></a></div><div class="next-post pull-right"><a href="/archives/540169c9.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Hexo框架(四)：Next主题文章置顶和公告效果</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2022-02-10</div><div class="title">使用GitHub Actions 实现自动化部署和部署到服务器</div></div></a></div><div><a href="/archives/353666f0.html" title="给你的博客添加一个收藏页"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200719133158.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-19</div><div class="title">给你的博客添加一个收藏页</div></div></a></div><div><a href="/archives/f36eea83.html" title="Hexo博客添加emoji表情支持"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">Hexo博客添加emoji表情支持</div></div></a></div><div><a href="/archives/2798a2b1.html" title="给文章标题添加一个emoji表情"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">给文章标题添加一个emoji表情</div></div></a></div><div><a href="/archives/f06684a1.html" title="NexT版本更新V8.0记录"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-19</div><div class="title">NexT版本更新V8.0记录</div></div></a></div><div><a href="/archives/b1e33f9d.html" title="sitemeta渐变背景实现"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-03</div><div class="title">sitemeta渐变背景实现</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A"><span class="toc-text">前言：</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%89%8D%E6%9C%9F%E5%87%86%E5%A4%87%E5%B7%A5%E4%BD%9C"><span class="toc-text">前期准备工作</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AE%89%E8%A3%85-Git-Bash"><span class="toc-text">安装 Git Bash</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AE%89%E8%A3%85-Node-js"><span class="toc-text">安装 Node.js</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%85%8D%E7%BD%AE-Github-%E4%BB%93%E5%BA%93"><span class="toc-text">配置 Github 仓库</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%96%B0%E5%BB%BA%E4%BB%93%E5%BA%93"><span class="toc-text">新建仓库</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%85%8D%E7%BD%AE-SSH-key"><span class="toc-text">配置 SSH key</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BB%A3%E7%A0%81%E7%BC%96%E8%BE%91%E5%99%A8"><span class="toc-text">代码编辑器</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%BD%BF%E7%94%A8-Hexo-%E6%90%AD%E5%BB%BA%E5%8D%9A%E5%AE%A2"><span class="toc-text">使用 Hexo 搭建博客</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AE%89%E8%A3%85-Hexo"><span class="toc-text">安装 Hexo</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%B8%8A%E4%BC%A0%E5%88%B0-GitHub"><span class="toc-text">上传到 GitHub</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%BB%93%E8%AF%AD"><span class="toc-text">结语</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>