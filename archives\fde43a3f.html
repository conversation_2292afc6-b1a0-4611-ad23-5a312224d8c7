<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Hexo框架(八)：双线部署及全站CDN加速 | 你真是一个美好的人类</title><meta name="keywords" content="博客搭建,Hexo"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="（1）部署到GitHub；（2）部署到coding； （3）全站CDN加速；">
<meta property="og:type" content="article">
<meta property="og:title" content="Hexo框架(八)：双线部署及全站CDN加速">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/fde43a3f.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="（1）部署到GitHub；（2）部署到coding； （3）全站CDN加速；">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png">
<meta property="article:published_time" content="2020-02-28T15:18:21.000Z">
<meta property="article:modified_time" content="2020-02-28T15:18:21.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="博客搭建">
<meta property="article:tag" content="Hexo">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/fde43a3f"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Hexo框架(八)：双线部署及全站CDN加速',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-02-28 15:18:21'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Hexo框架(八)：双线部署及全站CDN加速</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-02-28T15:18:21.000Z" title="发表于 2020-02-28 15:18:21">2020-02-28</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-02-28T15:18:21.000Z" title="更新于 2020-02-28 15:18:21">2020-02-28</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><div class="note danger flat"><h2 id="前言："><a href="#前言：" class="headerlink" title="前言："></a>前言：</h2><p>本篇设置基于 NEXT 主题 7.7.2 版本！！</p>
<p>CDN 加速我已更新：<a href="/archives/54c51cfa.html">Hexo 框架 (十八)：图片自适应 webp 及全站 CDN 加速</a></p>
</div>

<h2 id="部署到-GitHub"><a href="#部署到-GitHub" class="headerlink" title="部署到 GitHub"></a>部署到 GitHub</h2><p>这里就不再赘述如何部署到 GitHub，如果你还不会，可以参考一下 <a href="/archives/e3dc5cbb.html">Hexo 框架 (一)：使用 Hexo 快速搭建个人博客</a></p>
<h3 id="购买域名"><a href="#购买域名" class="headerlink" title="购买域名"></a>购买域名</h3><p>购买域名什么的，这里不再赘述，这里假定你已经购买了域名，并且会基本的解析操作。</p>
<h3 id="添加解析记录"><a href="#添加解析记录" class="headerlink" title="添加解析记录"></a>添加解析记录</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200404125320.png" alt="添加解析记录"></p>
<h3 id="Github-Pages-对自定义域上-Https"><a href="#Github-Pages-对自定义域上-Https" class="headerlink" title="Github Pages 对自定义域上 Https"></a>Github Pages 对自定义域上 Https</h3><p>我们在 Github Pages 项目中 Settings 选项卡 Github Pages 选项：在 Custom domain 添加你的自定义域名。</p>
<p>刷新页面 如果能勾选 Enforce HTTPS 即完成。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200404125034.png" alt="自定义域名"></p>
<h2 id="部署到-coding"><a href="#部署到-coding" class="headerlink" title="部署到 coding"></a>部署到 coding</h2><p>GitHub 服务器毕竟是在国外，国内访问的速度比较慢。而 Coding 是<strong>国内</strong>的一个面向开发者的云端开发平台，这意味着能够大大提升国内的访问速度。</p>
<p>本文简单记录了 Hexo 博客部署到 Coding 的过程。</p>
<h3 id="创建仓库"><a href="#创建仓库" class="headerlink" title="创建仓库"></a>创建仓库</h3><ul>
<li><p>首先官网注册账号。现在好像不能注册个人版了。你可以点击这里进行注册：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://e.coding.net/signup?_ga=2.161434680.426044788.1585971162-1077804823.1584698701">Coding</a></p>
</li>
<li><p>注册完账号之后，请前个人账户的设置页面选择 SSH 公钥将本地生成的 公钥 添加进去。公钥储存位置一般在 <code>C:\Users\<USER>\.ssh</code> 目录下的 <code>id_rsa.pub</code> 文件里，用记事本打开复制其内容即可。操作与 GitHub 类似，你可以参考 <a href="/archives/e3dc5cbb.html">Hexo 框架 (一)：使用 Hexo 快速搭建个人博客</a></p>
</li>
<li><p>新建项目</p>
</li>
<li><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200404123409.png" alt="新建项目"></p>
</li>
<li><p>然后创建仓库</p>
</li>
<li><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200404123453.png"></p>
</li>
</ul>
<p>一个仓库用来存储静态页面，一个用来保存我们的配置文件，做个备份。</p>
<h3 id="部署到-Coding-仓库"><a href="#部署到-Coding-仓库" class="headerlink" title="部署到 Coding 仓库"></a>部署到 Coding 仓库</h3><p>现在我们将 public 目录推送到 Coding 上博客仓库的 master 分支。</p>
<p>我们只需要改下站点的配置文件，添加如下代码：</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="bullet">-</span> <span class="attr">type:</span> <span class="string">git</span></span><br><span class="line">  <span class="attr">repository:</span> <span class="string">https://e.coding.net/XXXXX/XXXXXX.git</span></span><br><span class="line">  <span class="attr">branch:</span> <span class="string">master</span></span><br></pre></td></tr></table></figure>

<p>然后 <code>hexo deploy</code> 即可部署成功。</p>
<h3 id="开启-Coding-Pages-服务"><a href="#开启-Coding-Pages-服务" class="headerlink" title="开启 Coding Pages 服务"></a>开启 Coding Pages 服务</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200404123705.png" alt="静态网站"></p>
<p>博客部署完成后，我们在<code>构建与部署&gt; 静态网站</code>中一键开启 Coding Pages 服务。</p>
<p>在浏览器中直接输入他给的域名，查看是否部署成功。</p>
<h3 id="添加自定义域名"><a href="#添加自定义域名" class="headerlink" title="添加自定义域名"></a>添加自定义域名</h3><p>首先得有一个自定义域名，然后我们把它绑定到 Coding 上。</p>
<p>我是用的万网域名，直接打开 <a target="_blank" rel="noopener external nofollow noreferrer" href="https://account.aliyun.com/">阿里云控制台</a>。</p>
<p>添加解析记录：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/**************.png" alt="添加解析"></p>
<p>注意：因为我们要实现国内外访问不同的服务，所以需要分别设置解析，无论是腾讯云还是阿里云的解析服务，都能够很好的区分国内外节点，我们需要将国内的 <code>CNAME</code> 设置到 <code>pages.coding.me</code>，将国外的 <code>CNAME</code> 设置到 <code>pages.github.io</code>。</p>
<h3 id="Coding-绑定自定义域名"><a href="#Coding-绑定自定义域名" class="headerlink" title="Coding 绑定自定义域名"></a>Coding 绑定自定义域名</h3><p>打开 Coding 的 Pages 服务，进入设置页面，为网站绑定自己的域名。这里他也有说怎么绑定。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200404124318.png" alt="image-20200404124317316"></p>
<p>注意：一定要选首选的域名，并且<strong>非首选域名要勾选跳转至首选域名</strong>，不然有些第三方服务数据会统计不到一起。</p>
<h3 id="开启-HTTPS"><a href="#开启-HTTPS" class="headerlink" title="开启 HTTPS"></a>开启 HTTPS</h3><p>如果你之前已经部署到了 GitHub Pages 并开启了 HTTPS，那么直接在设置页面绑定你自己的域名，SSL/TLS 安全证书就会显示申请错误。</p>
<p>申请错误原因是：在验证域名所有权时会定位到 Github Pages 的主机上导致 SSL 证书申请失败。</p>
<p><strong>正确的做法是：先去域名 DNS 把 GitHub 的解析暂停掉，然后再重新申请 SSL 证书，大约十秒左右就能申请成功，然后开启强制 HTTPS 访问</strong>。</p>
<h3 id="配置-config-yml"><a href="#配置-config-yml" class="headerlink" title="配置_config.yml"></a>配置_config.yml</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">deploy:</span></span><br><span class="line">  <span class="attr">type:</span> <span class="string">git</span></span><br><span class="line">  <span class="attr">repo:</span></span><br><span class="line">    <span class="attr">coding:</span> <span class="string">https://e.coding.net/XXXX/blog.git</span></span><br><span class="line">    <span class="attr">github:</span> <span class="string">https://github.com/XXXXX/XXXXX.github.io.git</span></span><br><span class="line">  <span class="attr">branch:</span> <span class="string">master</span></span><br></pre></td></tr></table></figure>

<p>到此双线部署完成，你每次<code>hexo d</code> 就会自动上传到两个仓库，并自动部署。</p>
<h2 id="全站-CDN-加速"><a href="#全站-CDN-加速" class="headerlink" title="全站 CDN 加速"></a>全站 CDN 加速</h2><p>由于 GitHub Pages 的服务器在国外，国内访问速度可能会非常慢。目前有以下几种解决方案：</p>
<ul>
<li>博客双线部署在 GitHub Pages 和 Coding Pages 上，其中国内流量解析到 Coding Pages，国外流量解析到 GitHub Pages<ul>
<li>优势：无需购买 VPS、速度比较快、不用备案</li>
<li>劣势：Coding Pages 的服务并不是那么稳定，近期出现多次不能正常部署或访问 Pages 的问题</li>
</ul>
</li>
<li>博客部署到国内的 VPS<ul>
<li>优势：速度快</li>
<li>劣势：需要购买 VPS、需要备案</li>
</ul>
</li>
<li>博客部署在 GitHub Pages 上并采用 CDN 加速<ul>
<li>优势：速度快、无需备案、稳定</li>
<li>劣势：CDN 比较贵</li>
</ul>
</li>
</ul>
<p><strong>CDN 介绍</strong></p>
<blockquote>
<p>内容分发网络（Content delivery network 或 Content distribution network，缩写：CDN）是指一种透过互联网互相连接的计算机网络系统，利用最靠近每位用户的服务器，更快、更可靠地将音乐、图片、影片、应用程序及其他文件发送给用户，来提供高性能、可扩展性及低成本的网络内容传递给用户。</p>
</blockquote>
<p>简单来说，CDN 就是部署在世界各地的缓存服务器，它们会提前缓存网站上的资源，然后当用户想要访问相关资源时，直接从 CDN 服务器上取就可以了。这样不仅可以增加访问速度减少访问延迟，还可以减缓网站服务器上的压力。</p>
<p><strong>为什么选择 Cloudflare？</strong></p>
<p>国内的 CDN 服务提供商有不少，但是基本都需要备案，因此我转向了 Cloudflare 这个国外的 CDN 服务提供商。</p>
<p>Cloudflare 是全球最大的 DNS 服务提供商之一。除此之外他们还提供 CDN、SSL 证书、DDos 保护等服务，并且 Cloudflare 与百度有合作，在国内也部署有大量的节点，还能顺便解决百度爬无法抓取 GitHub Pages 的问题。</p>
<h3 id="快速开始"><a href="#快速开始" class="headerlink" title="快速开始"></a>快速开始</h3><ul>
<li><p>首先要去 <a target="_blank" rel="noopener external nofollow noreferrer" href="https://www.cloudflare.com/zh-cn/network/china/">Cloudflare</a> 注册一个帐号。<strong>注意：我们需要的是国际版的 CloudFlare，而不是 CloudFlare 与百度合作的百度云加速</strong>。</p>
</li>
<li><p>注册好后点击 <code>Add site</code> 添加你的网站个性化域名。</p>
</li>
<li><p>添加好后选择免费的那个计划（Plan），有钱也可以选择收费的，提供的服务更多。</p>
</li>
<li><p>然后点击 <code>Scan DNS Records</code>，等待扫描完成。即使没有扫到 DNS 记录也没有关系，我们可以在后面自己添加：</p>
</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200404130218.png" alt="添加记录"></p>
<p>然后 Cloudflare 会要你把你的 DNS 服务器替换成他提供的，我们只需要去域名商那里设置一下即可。<strong>CloudFlare 既是一个 CDN 服务商，同样也是一个 DNS 服务商</strong>。DNS 修改完成后可以点击 <code>Recheck Nameservers</code> 来复查 NS 记录是否正确设置。</p>
<h3 id="Cloudflare-配置详解"><a href="#Cloudflare-配置详解" class="headerlink" title="Cloudflare 配置详解"></a>Cloudflare 配置详解</h3><h4 id="Overview-菜单设置"><a href="#Overview-菜单设置" class="headerlink" title="Overview 菜单设置"></a>Overview 菜单设置</h4><p>主要关注下面两个快捷设置：</p>
<ul>
<li><code>under attack mode</code>：当你的网站被攻击的时候打开它，这样可以阻止攻击。</li>
<li><code>development mode</code>：由于 cloudflare 采用了缓存技术，当你更新了网站的内容，不一定能马上在前台看到更新的内容。这个时候你只要打开 development mode 就可以马上看到网站更新的内容。当然，正常情况下还是关掉比较好。</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200404131850.png" alt="OverView菜单"></p>
<h4 id="Analytics-菜单设置"><a href="#Analytics-菜单设置" class="headerlink" title="Analytics 菜单设置"></a>Analytics 菜单设置</h4><p>这里主要是看网站使用 cloudflare 之后的一些分析数据，Analytics 下面包含几个子菜单：Traffic、security、Performance、DNS。</p>
<ul>
<li>Traffic 子菜单：主要是网站在过去某一个时间段的请求、带宽、访客情况。</li>
<li>Security 子菜单：主要是某一段时间内受到的威胁数据，包括威胁次数、来自哪些国家、威胁的类型等等。</li>
<li>Performance 子菜单：付费功能。</li>
<li>DNS 子菜单：主要是 DNS 查询的数据。</li>
</ul>
<h4 id="DNS-菜单设置"><a href="#DNS-菜单设置" class="headerlink" title="DNS 菜单设置"></a>DNS 菜单设置</h4><p>在 DNS 菜单里面，你可以随意添加或删除所有 DNS 记录，也可以选择使用或不使用 cloudflare 的 CDN 服务。</p>
<p>注意：只有 A 记录和 CNAME 记录才可以设置使用 CDN 服务，我们只需要点亮 <code>Proxy status</code> 处的云彩小图标即可切换成使用 CDN 的状态。</p>
<h4 id="SSL-TLS-菜单设置"><a href="#SSL-TLS-菜单设置" class="headerlink" title="SSL/TLS 菜单设置"></a>SSL/TLS 菜单设置</h4><p>这里面主要是网站加密的设置。</p>
<ul>
<li><code>SSL</code> 设置：如果你本来的网站有 SSL 证书，这里选择 full 即可，即浏览器到 Cloudflare 和 Cloudflare 到服务器都是加密的。</li>
<li><strong><code>Always Use HTTPS</code> 设置：这个打开，一直使用 https 链接</strong>。</li>
<li><code>Authenticated Origin Pulls</code> &amp; <code>Opportunistic Encryption</code> &amp; <code>Onion Routing</code> 设置：这些设置都打开</li>
<li><code>Minimum TLS Version</code> 设置：选择 1.1 即可，如果选择太高可能导致网站打不开。</li>
</ul>
<h4 id="Firewall-菜单设置"><a href="#Firewall-菜单设置" class="headerlink" title="Firewall 菜单设置"></a>Firewall 菜单设置</h4><p>Firewall 菜单里面可以对网站安全进一步设置，限制某些 IP 或地区的用户访问等。</p>
<ul>
<li><p>Events 子菜单：这里面可以查看防火墙的一些小事件，比如屏蔽的一些访问。</p>
</li>
<li><p>Managed Rules 子菜单：付费功能。</p>
</li>
<li><p>Firewall Rules 子菜单：这里可以设置限制其他人访问你的网站的规则，每个账户只能设置 5 个限制规则。</p>
</li>
<li><p>TOOLS 子菜单：这里的功能是 firewall rules 功能的补充，都是对用户访问你的网站的一些设置。</p>
<ul>
<li><code>IP Access Rules</code>：主要是针对 IP、IP 范围及国家和 ASN 设置一些规则。</li>
<li><code>User Agent Blocking</code>：对用户使用的代理设置规则，比如浏览器类型、CPU、操作系统。</li>
</ul>
</li>
<li><p>Settting 子菜单：</p>
<ul>
<li><code>Security Level</code>：这个设置 low 即可，如果设置太高，就会过于敏感，导致很多用户受影响。</li>
<li><code>Challenge Passage</code>：这是验证的有效期，可以设置久一点，比如 <code>1 hour</code>。</li>
<li><code>Browser Integrity Check</code> 和 <code>Privacy Pass Support</code>：全部打开。</li>
</ul>
</li>
</ul>
<h4 id="Speed-菜单设置"><a href="#Speed-菜单设置" class="headerlink" title="Speed 菜单设置"></a>Speed 菜单设置</h4><ul>
<li><code>Auto Minify</code>：网页最小化处理，把 Javascript, css html 前面都打勾即可。</li>
<li><code>Brotli</code>：这是一种比 gzip 更好的网页压缩方式，打开即可。</li>
<li><code>Rocket Loader</code>：这个需要测试决定是否需要打开，没有固定的答案，你可以打开之后，然后在不同的浏览器上面查看网页的效果，如果出现网页排版等问题，那就将它关闭。</li>
</ul>
<h4 id="Caching-菜单设置"><a href="#Caching-菜单设置" class="headerlink" title="Caching 菜单设置"></a>Caching 菜单设置</h4><ul>
<li><p><code>Purge Cache</code>：这个设置是清除缓存，其中：</p>
</li>
<li><p><code>Custom Purge</code>：清除指定页面的缓存，我们可以点击 <code>custom purge</code>, 然后在框里面输入要清除的链接，再点击 <code>purge</code> 即可。</p>
<ul>
<li><code>Purge Everything</code>：清除整个网站的缓存。</li>
</ul>
</li>
<li><p><code>Caching Level</code>：缓存水平设置，决定缓存哪些内容，这里选择 standard 即可。</p>
</li>
<li><p><code>Browser Cache TTL</code>：即浏览器缓存过期时间，决定浏览器多久向网站获取一次新缓存。</p>
</li>
</ul>
<h4 id="Page-Rules-菜单设置"><a href="#Page-Rules-菜单设置" class="headerlink" title="Page Rules 菜单设置"></a>Page Rules 菜单设置</h4><p>免费版本可以设置 3 个页面的规则。</p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/fde43a3f.html">http://blog.mhy.loc.cc/archives/fde43a3f.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a><a class="post-meta__tags" href="/tags/Hexo/">Hexo</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/d18fd292.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Hexo框架(九)：博客展示GitHub Chart</div></div></a></div><div class="next-post pull-right"><a href="/archives/868f84ed.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Hexo框架(七)：博客性能优化提升加载速度</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2022-02-10</div><div class="title">使用GitHub Actions 实现自动化部署和部署到服务器</div></div></a></div><div><a href="/archives/353666f0.html" title="给你的博客添加一个收藏页"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200719133158.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-19</div><div class="title">给你的博客添加一个收藏页</div></div></a></div><div><a href="/archives/f36eea83.html" title="Hexo博客添加emoji表情支持"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">Hexo博客添加emoji表情支持</div></div></a></div><div><a href="/archives/2798a2b1.html" title="给文章标题添加一个emoji表情"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">给文章标题添加一个emoji表情</div></div></a></div><div><a href="/archives/f06684a1.html" title="NexT版本更新V8.0记录"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-19</div><div class="title">NexT版本更新V8.0记录</div></div></a></div><div><a href="/archives/b1e33f9d.html" title="sitemeta渐变背景实现"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-03</div><div class="title">sitemeta渐变背景实现</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A"><span class="toc-text">前言：</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E9%83%A8%E7%BD%B2%E5%88%B0-GitHub"><span class="toc-text">部署到 GitHub</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%B4%AD%E4%B9%B0%E5%9F%9F%E5%90%8D"><span class="toc-text">购买域名</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%B7%BB%E5%8A%A0%E8%A7%A3%E6%9E%90%E8%AE%B0%E5%BD%95"><span class="toc-text">添加解析记录</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Github-Pages-%E5%AF%B9%E8%87%AA%E5%AE%9A%E4%B9%89%E5%9F%9F%E4%B8%8A-Https"><span class="toc-text">Github Pages 对自定义域上 Https</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E9%83%A8%E7%BD%B2%E5%88%B0-coding"><span class="toc-text">部署到 coding</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%88%9B%E5%BB%BA%E4%BB%93%E5%BA%93"><span class="toc-text">创建仓库</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%83%A8%E7%BD%B2%E5%88%B0-Coding-%E4%BB%93%E5%BA%93"><span class="toc-text">部署到 Coding 仓库</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%BC%80%E5%90%AF-Coding-Pages-%E6%9C%8D%E5%8A%A1"><span class="toc-text">开启 Coding Pages 服务</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%B7%BB%E5%8A%A0%E8%87%AA%E5%AE%9A%E4%B9%89%E5%9F%9F%E5%90%8D"><span class="toc-text">添加自定义域名</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Coding-%E7%BB%91%E5%AE%9A%E8%87%AA%E5%AE%9A%E4%B9%89%E5%9F%9F%E5%90%8D"><span class="toc-text">Coding 绑定自定义域名</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%BC%80%E5%90%AF-HTTPS"><span class="toc-text">开启 HTTPS</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%85%8D%E7%BD%AE-config-yml"><span class="toc-text">配置_config.yml</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%85%A8%E7%AB%99-CDN-%E5%8A%A0%E9%80%9F"><span class="toc-text">全站 CDN 加速</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%BF%AB%E9%80%9F%E5%BC%80%E5%A7%8B"><span class="toc-text">快速开始</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Cloudflare-%E9%85%8D%E7%BD%AE%E8%AF%A6%E8%A7%A3"><span class="toc-text">Cloudflare 配置详解</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#Overview-%E8%8F%9C%E5%8D%95%E8%AE%BE%E7%BD%AE"><span class="toc-text">Overview 菜单设置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#Analytics-%E8%8F%9C%E5%8D%95%E8%AE%BE%E7%BD%AE"><span class="toc-text">Analytics 菜单设置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#DNS-%E8%8F%9C%E5%8D%95%E8%AE%BE%E7%BD%AE"><span class="toc-text">DNS 菜单设置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#SSL-TLS-%E8%8F%9C%E5%8D%95%E8%AE%BE%E7%BD%AE"><span class="toc-text">SSL&#x2F;TLS 菜单设置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#Firewall-%E8%8F%9C%E5%8D%95%E8%AE%BE%E7%BD%AE"><span class="toc-text">Firewall 菜单设置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#Speed-%E8%8F%9C%E5%8D%95%E8%AE%BE%E7%BD%AE"><span class="toc-text">Speed 菜单设置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#Caching-%E8%8F%9C%E5%8D%95%E8%AE%BE%E7%BD%AE"><span class="toc-text">Caching 菜单设置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#Page-Rules-%E8%8F%9C%E5%8D%95%E8%AE%BE%E7%BD%AE"><span class="toc-text">Page Rules 菜单设置</span></a></li></ol></li></ol></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>