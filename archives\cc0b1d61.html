<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Hexo框架(十六)：Valine评论系统配置邮件提醒功能 | 你真是一个美好的人类</title><meta name="keywords" content="博客搭建,Hexo"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="（1）注册valine； (2)配置邮件提醒功能Valine Admin">
<meta property="og:type" content="article">
<meta property="og:title" content="Hexo框架(十六)：Valine评论系统配置邮件提醒功能">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/cc0b1d61.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="（1）注册valine； (2)配置邮件提醒功能Valine Admin">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png">
<meta property="article:published_time" content="2020-04-15T21:20:20.000Z">
<meta property="article:modified_time" content="2020-04-15T21:20:20.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="博客搭建">
<meta property="article:tag" content="Hexo">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/cc0b1d61"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Hexo框架(十六)：Valine评论系统配置邮件提醒功能',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-04-15 21:20:20'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Hexo框架(十六)：Valine评论系统配置邮件提醒功能</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-04-15T21:20:20.000Z" title="发表于 2020-04-15 21:20:20">2020-04-15</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-04-15T21:20:20.000Z" title="更新于 2020-04-15 21:20:20">2020-04-15</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><p>next 主题集成了很多评论系统，Disqus、友言、畅言、Valine（基于 Leancloud）、Gitment、Gitalk 等。不过 Disqus 是韩国的，虽说国内是能有的不过有时候还是会莫名其妙像被墙起来了一样，友言和畅言已经停止维护，Next 在 7.2 版本也把 Gitment 移除支持了，不过 Gitalk 和 Gitment 比较类似，都是基于 GitHub 的 Issue 实现的。Gitalk 评论虽然最符合程序员的喜好，毕竟基于 GitHub 的 Issue，可以自由改动、打 Tag 等，但是缺点就是必须登录 GitHub 账号，有时候有些小问题就显得麻烦了所以我最后选择了 valine,免登陆，而且免费！</p>
<p>不过我很早就配置了这个评论功能，配置起来也很简单，但是这里还是简单的说一下吧。</p>
<div class="note flat"><p>我现在的 next 主题版本已经更新到了 7.8.0</p>
</div>

<h2 id="注册-Valine"><a href="#注册-Valine" class="headerlink" title="注册 Valine"></a>注册 Valine</h2><p>注册 valine 及配置，你可以参考：<a href="/archives/264a3045.html">Next 主题配置与美化</a> 第三部分中的第 10 条。总体来说十分简单。</p>
<p>值得注意的是，如果你和我一样采用的是<strong>数据文件</strong>的方式，只需要把该文中的主题配置文件换成 next.yml 就好。你可以参考：<a href="/archives/5b20fbd0.html">博客主题持续更新的问题</a></p>
<p>当然我也更加推荐使用<strong>数据文件</strong>的方式来进行配置。</p>
<h2 id="Valine-的一些样式修改"><a href="#Valine-的一些样式修改" class="headerlink" title="Valine 的一些样式修改"></a>Valine 的一些样式修改</h2><p>这里贴一下，我修改过的一些样式，让评论区看起来更加整洁。我所有的自定义样式都放置于<code>hexo/source/_data/style.styl</code>文件中</p>
<figure class="highlight stylus"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 隐藏 valine 的 powered by</span></span><br><span class="line"><span class="selector-class">.power</span><span class="selector-class">.txt-right</span> &#123;</span><br><span class="line">	<span class="attribute">display</span>: none;</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="comment">// valine 评论框对齐文章</span></span><br><span class="line">div<span class="selector-id">#comments</span><span class="selector-class">.comments</span>.v&#123;</span><br><span class="line">  <span class="attribute">margin-left</span>: <span class="number">0px</span> ;</span><br><span class="line">  <span class="attribute">margin-right</span>: <span class="number">0px</span> ;</span><br><span class="line">  <span class="attribute">border</span>: <span class="number">0px</span>;</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="comment">// Valine 隐藏系统信息</span></span><br><span class="line">.vsys&#123;</span><br><span class="line">  <span class="attribute">display</span>:none <span class="meta">!important</span>;</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p>记得要在 next.yml 或是主题配置文件中取消掉<code>custom_file_path</code>的注释：</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">custom_file_path:</span></span><br><span class="line">  <span class="attr">style:</span> <span class="string">source/_data/styles.styl</span></span><br></pre></td></tr></table></figure>

<div class="note danger flat"><p>注意：隐藏 valine 的 powered by 样式已经发生了变化（2020.4.12），现在应该修正为：</p>
</div>

<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line">// 隐藏 valine 的 powered by</span><br><span class="line">.vcopy&#123;</span><br><span class="line">	display: none !important;</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<h2 id="配置邮件提醒功能"><a href="#配置邮件提醒功能" class="headerlink" title="配置邮件提醒功能"></a>配置邮件提醒功能</h2><h3 id="基础设置"><a href="#基础设置" class="headerlink" title="基础设置"></a>基础设置</h3><ul>
<li>在 设置》安全中心》服务开关中，关闭除了数据存储外的所有功能，并在下方 web 安全域名中添加好你的域名：</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200411204717.png" alt="服务开关"></p>
<ul>
<li>在<code>主题配置文件中</code> ，我们需要关闭掉自带的邮件提醒服务：</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">valine:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">appid:</span></span><br><span class="line">  <span class="attr">appkey:</span></span><br><span class="line">  <span class="comment">#这里↓↓↓↓↓↓↓</span></span><br><span class="line">  <span class="attr">notify:</span> <span class="literal">false</span></span><br><span class="line">  <span class="attr">verify:</span> <span class="literal">false</span></span><br><span class="line">  <span class="comment">#这里↑↑↑↑↑↑↑</span></span><br><span class="line">  <span class="attr">placeholder:</span> <span class="string">在这里写下你的评论吧！</span></span><br><span class="line">  <span class="attr">avatar:</span> <span class="string">robohash</span></span><br><span class="line">  <span class="attr">guest_info:</span> <span class="string">nick,mail</span></span><br><span class="line">  <span class="attr">pageSize:</span> <span class="number">10</span></span><br><span class="line">  <span class="attr">language:</span> <span class="string">zh-cn</span></span><br><span class="line">  <span class="attr">visitor:</span> <span class="literal">false</span></span><br><span class="line">  <span class="attr">comment_count:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">recordIP:</span> <span class="literal">false</span></span><br></pre></td></tr></table></figure>

<p>顺便提一句，因为主题自带的 CDN 很慢，你可以在主题配置文件中使用第三方 CDN：</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">vendors:</span></span><br><span class="line">		<span class="attr">valine:</span> <span class="string">//cdn.jsdelivr.net/npm/valine@1/dist/Valine.min.js</span></span><br></pre></td></tr></table></figure>

<h3 id="配置-Valine-Admin"><a href="#配置-Valine-Admin" class="headerlink" title="配置 Valine Admin"></a>配置 Valine Admin</h3><blockquote>
<p>Valine Admin 是 Valine 评论系统的扩展和增强，主要实现评论邮件通知、评论管理、垃圾评论过滤等功能。支持完全自定义的邮件通知模板。基于 Akismet API 实现准确的垃圾评论过滤。此外，使用云函数等技术解决了免费版云引擎休眠问题，支持云引擎自动唤醒，漏发邮件自动补发。兼容云淡风轻及 Deserts 维护的多版本 Valine。</p>
</blockquote>
<h4 id="云引擎”一键”部署"><a href="#云引擎”一键”部署" class="headerlink" title="云引擎”一键”部署"></a>云引擎”一键”部署</h4><ol>
<li>在<a target="_blank" rel="noopener external nofollow noreferrer" href="https://leancloud.cn/dashboard/#/apps">Leancloud</a>云引擎设置界面，填写代码库并保存：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/DesertsP/Valine-Admin.git">https://github.com/DesertsP/Valine-Admin.git</a></li>
</ol>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200411205523.png" alt="设置仓库"></p>
<ol>
<li>在设置页面，设置环境变量以及 Web 二级域名。</li>
</ol>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200411205606.png" alt="环境变量"></p>
<table>
<thead>
<tr>
<th align="left">变量</th>
<th align="left">示例</th>
<th align="left">说明</th>
</tr>
</thead>
<tbody><tr>
<td align="left">SITE_NAME</td>
<td align="left">Deserts</td>
<td align="left">[必填]博客名称</td>
</tr>
<tr>
<td align="left">SITE_URL</td>
<td align="left"><a href="http://blog.mhy.loc.cc/">http://blog.mhy.loc.cc</a></td>
<td align="left">[必填]首页地址</td>
</tr>
<tr>
<td align="left"><strong>SMTP_SERVICE</strong></td>
<td align="left">QQ</td>
<td align="left">[新版支持]邮件服务提供商，支持 QQ、163、126、Gmail 以及 <a target="_blank" rel="noopener external nofollow noreferrer" href="https://nodemailer.com/smtp/well-known/#supported-services">更多</a></td>
</tr>
<tr>
<td align="left">SMTP_USER</td>
<td align="left"><a href="mailto:<EMAIL>" rel="external nofollow noreferrer"><EMAIL></a></td>
<td align="left">[必填]SMTP 登录用户</td>
</tr>
<tr>
<td align="left">SMTP_PASS</td>
<td align="left">ccxxxxxxxxch</td>
<td align="left">[必填]SMTP 登录密码（QQ 邮箱需要获取独立密码）</td>
</tr>
<tr>
<td align="left">SENDER_NAME</td>
<td align="left">Deserts</td>
<td align="left">[必填]发件人</td>
</tr>
<tr>
<td align="left">SENDER_EMAIL</td>
<td align="left"><a href="mailto:<EMAIL>" rel="external nofollow noreferrer"><EMAIL></a></td>
<td align="left">[必填]发件邮箱</td>
</tr>
<tr>
<td align="left">ADMIN_URL</td>
<td align="left"><a target="_blank" rel="noopener external nofollow noreferrer" href="https://xxx.leanapp.cn/">https://xxx.leanapp.cn/</a></td>
<td align="left">[建议]Web 主机二级域名，用于自动唤醒</td>
</tr>
<tr>
<td align="left">BLOGGER_EMAIL</td>
<td align="left"><a href="mailto:<EMAIL>" rel="external nofollow noreferrer"><EMAIL></a></td>
<td align="left">[可选]博主通知收件地址，默认使用 SENDER_EMAIL</td>
</tr>
<tr>
<td align="left">AKISMET_KEY</td>
<td align="left">xxxxxxxxxxxx</td>
<td align="left">[可选]Akismet Key 用于垃圾评论检测，设为 MANUAL_REVIEW 开启人工审核，留空不使用反垃圾</td>
</tr>
</tbody></table>
<p><strong>以上必填参数请务必正确设置。</strong></p>
<p>二级域名用于评论后台管理，如<a target="_blank" rel="noopener external nofollow noreferrer" href="https://xxxx.leanapp.cn/">https://XXX.leanapp.cn</a> 。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200411205719.png" alt="二级域名"></p>
<ol>
<li>切换到部署标签页，分支使用 master，点击部署即可</li>
</ol>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200411205821.png" alt="部署环境"></p>
<p>第一次部署需要花点时间。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200411205846.png" alt="部署过程"></p>
<ol>
<li><p>评论管理。访问设置的二级域名</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">https://二级域名.leanapp.cn/sign-up</span><br></pre></td></tr></table></figure>

<p>，注册管理员登录信息，如：</p>
<p><a target="_blank" rel="noopener external nofollow noreferrer" href="https://deserts.leanapp.cn/sign-up">https://deserts.leanapp.cn/sign-up</a></p>
<blockquote>
<p>注：使用原版 Valine 如果遇到注册页面不显示直接跳转至登录页的情况，请手动删除_User 表中的全部数据。</p>
</blockquote>
</li>
</ol>
<p>此后，可以通过<code>https://二级域名.leanapp.cn/</code>管理评论。</p>
<ol>
<li>定时任务设置</li>
</ol>
<p>目前实现了两种云函数定时任务：</p>
<ul>
<li>(1)自动唤醒，定时访问 Web APP 二级域名防止云引擎休眠；.</li>
<li>(2)每天定时检查 24 小时内漏发的邮件通知。</li>
</ul>
<p>进入云引擎-定时任务中，创建定时器，创建两个定时任务。</p>
<p>选择<code>self-wake</code>云函数，Cron 表达式为<code>0 0/30 7-23 * * ?</code>，表示每天早 6 点到晚 23 点每隔 30 分钟访问云引擎，<code>ADMIN_URL</code>环境变量务必设置正确：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cloud.panjunwen.com/2018/09/ping-mu-kuai-zhao-2018-09-18-xia-wu-2-57-43.png" alt="唤醒云引擎"></p>
<p>选择<code>resend-mails</code>云函数，Cron 表达式为<code>0 0 8 * * ?</code>，表示每天早 8 点检查过去 24 小时内漏发的通知邮件并补发：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cloud.panjunwen.com/2018/09/ping-mu-kuai-zhao-2018-09-18-xia-wu-2-57-53.png" alt="通知检查"></p>
<p><strong>添加定时器后记得点击启动方可生效。</strong></p>
<p><strong>至此，Valine Admin 已经可以正常工作，更多以下是可选的进阶配置。</strong></p>
<h4 id="邮件通知模板"><a href="#邮件通知模板" class="headerlink" title="邮件通知模板"></a>邮件通知模板</h4><p>邮件通知模板在云引擎环境变量中设定，可自定义通知邮件标题及内容模板。</p>
<table>
<thead>
<tr>
<th align="left">环境变量</th>
<th align="left">示例</th>
<th align="left">说明</th>
</tr>
</thead>
<tbody><tr>
<td align="left">MAIL_SUBJECT</td>
<td align="left">${PARENT_NICK}，您在${SITE_NAME}上的评论收到了回复</td>
<td align="left">[可选]@通知邮件主题（标题）模板</td>
</tr>
<tr>
<td align="left">MAIL_TEMPLATE</td>
<td align="left">见下文</td>
<td align="left">[可选]@通知邮件内容模板</td>
</tr>
<tr>
<td align="left">MAIL_SUBJECT_ADMIN</td>
<td align="left">${SITE_NAME}上有新评论了</td>
<td align="left">[可选]博主邮件通知主题模板</td>
</tr>
<tr>
<td align="left">MAIL_TEMPLATE_ADMIN</td>
<td align="left">见下文</td>
<td align="left">[可选]博主邮件通知内容模板</td>
</tr>
</tbody></table>
<p>邮件通知包含两种，分别是被@通知和博主通知，这两种模板都可以完全自定义。默认使用经典的蓝色风格模板（样式来源未知）。</p>
<p>默认被@通知邮件内容模板如下：</p>
<figure class="highlight html"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">div</span></span></span><br><span class="line"><span class="tag">  <span class="attr">style</span>=<span class="string">&quot;border-top:2px solid #12ADDB;box-shadow:0 1px 3px #AAAAAA;line-height:180%;padding:0 15px 12px;margin:50px auto;font-size:12px;&quot;</span></span></span><br><span class="line"><span class="tag">&gt;</span></span><br><span class="line">  <span class="tag">&lt;<span class="name">h2</span></span></span><br><span class="line"><span class="tag">    <span class="attr">style</span>=<span class="string">&quot;border-bottom:1px solid #DDD;font-size:14px;font-weight:normal;padding:13px 0 10px 8px;&quot;</span></span></span><br><span class="line"><span class="tag">  &gt;</span></span><br><span class="line">    您在<span class="tag">&lt;<span class="name">a</span></span></span><br><span class="line"><span class="tag">      <span class="attr">style</span>=<span class="string">&quot;text-decoration:none;color: #12ADDB;&quot;</span></span></span><br><span class="line"><span class="tag">      <span class="attr">href</span>=<span class="string">&quot;$&#123;SITE_URL&#125;&quot;</span></span></span><br><span class="line"><span class="tag">      <span class="attr">target</span>=<span class="string">&quot;_blank&quot;</span></span></span><br><span class="line"><span class="tag">    &gt;</span></span><br><span class="line">      $&#123;SITE_NAME&#125;&lt;/a</span><br><span class="line">    &gt;上的评论有了新的回复</span><br><span class="line">  <span class="tag">&lt;/<span class="name">h2</span>&gt;</span></span><br><span class="line">  $&#123;PARENT_NICK&#125; 同学，您曾发表评论：</span><br><span class="line">  <span class="tag">&lt;<span class="name">div</span> <span class="attr">style</span>=<span class="string">&quot;padding:0 12px 0 12px;margin-top:18px&quot;</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">div</span></span></span><br><span class="line"><span class="tag">      <span class="attr">style</span>=<span class="string">&quot;background-color: #f5f5f5;padding: 10px 15px;margin:18px 0;word-wrap:break-word;&quot;</span></span></span><br><span class="line"><span class="tag">    &gt;</span></span><br><span class="line">      $&#123;PARENT_COMMENT&#125;</span><br><span class="line">    <span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">p</span>&gt;</span><span class="tag">&lt;<span class="name">strong</span>&gt;</span>$&#123;NICK&#125;<span class="tag">&lt;/<span class="name">strong</span>&gt;</span>回复说：<span class="tag">&lt;/<span class="name">p</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">div</span></span></span><br><span class="line"><span class="tag">      <span class="attr">style</span>=<span class="string">&quot;background-color: #f5f5f5;padding: 10px 15px;margin:18px 0;word-wrap:break-word;&quot;</span></span></span><br><span class="line"><span class="tag">    &gt;</span></span><br><span class="line">      $&#123;COMMENT&#125;</span><br><span class="line">    <span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">p</span>&gt;</span></span><br><span class="line">      您可以点击<span class="tag">&lt;<span class="name">a</span></span></span><br><span class="line"><span class="tag">        <span class="attr">style</span>=<span class="string">&quot;text-decoration:none; color:#12addb&quot;</span></span></span><br><span class="line"><span class="tag">        <span class="attr">href</span>=<span class="string">&quot;$&#123;POST_URL&#125;&quot;</span></span></span><br><span class="line"><span class="tag">        <span class="attr">target</span>=<span class="string">&quot;_blank&quot;</span></span></span><br><span class="line"><span class="tag">        &gt;</span>查看回复的完整內容&lt;/a</span><br><span class="line">      &gt;，欢迎再次光临<span class="tag">&lt;<span class="name">a</span></span></span><br><span class="line"><span class="tag">        <span class="attr">style</span>=<span class="string">&quot;text-decoration:none; color:#12addb&quot;</span></span></span><br><span class="line"><span class="tag">        <span class="attr">href</span>=<span class="string">&quot;$&#123;SITE_URL&#125;&quot;</span></span></span><br><span class="line"><span class="tag">        <span class="attr">target</span>=<span class="string">&quot;_blank&quot;</span></span></span><br><span class="line"><span class="tag">        &gt;</span>$&#123;SITE_NAME&#125;&lt;/a</span><br><span class="line">      &gt;。<span class="tag">&lt;<span class="name">br</span> /&gt;</span></span><br><span class="line">    <span class="tag">&lt;/<span class="name">p</span>&gt;</span></span><br><span class="line">  <span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br></pre></td></tr></table></figure>

<p>效果如下图：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cloud.panjunwen.com/2018/09/wei-ming-ming.png" alt="mail-blue-template"></p>
<p>@通知模板中的可用变量如下（注，这是邮件模板变量，是指嵌入到 HTML 邮件模板中的变量，请勿与云引擎环境变量混淆）：</p>
<table>
<thead>
<tr>
<th align="left">模板变量</th>
<th align="left">说明</th>
</tr>
</thead>
<tbody><tr>
<td align="left">SITE_NAME</td>
<td align="left">博客名称</td>
</tr>
<tr>
<td align="left">SITE_URL</td>
<td align="left">博客首页地址</td>
</tr>
<tr>
<td align="left">POST_URL</td>
<td align="left">文章地址（完整路径）</td>
</tr>
<tr>
<td align="left">PARENT_NICK</td>
<td align="left">收件人昵称（被@者，父级评论人）</td>
</tr>
<tr>
<td align="left">PARENT_COMMENT</td>
<td align="left">父级评论内容</td>
</tr>
<tr>
<td align="left">NICK</td>
<td align="left">新评论者昵称</td>
</tr>
<tr>
<td align="left">COMMENT</td>
<td align="left">新评论内容</td>
</tr>
</tbody></table>
<p>默认博主通知邮件内容模板如下：</p>
<figure class="highlight html"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">div</span> <span class="attr">style</span>=<span class="string">&quot;border-top:2px solid #12ADDB;box-shadow:0 1px 3px #AAAAAA;line-height:180%;padding:0 15px 12px;margin:50px auto;font-size:12px;&quot;</span>&gt;</span><span class="tag">&lt;<span class="name">h2</span> <span class="attr">style</span>=<span class="string">&quot;border-bottom:1px solid #DDD;font-size:14px;font-weight:normal;padding:13px 0 10px 8px;&quot;</span>&gt;</span>您在<span class="tag">&lt;<span class="name">a</span> <span class="attr">style</span>=<span class="string">&quot;text-decoration:none;color: #12ADDB;&quot;</span> <span class="attr">href</span>=<span class="string">&quot;$&#123;SITE_URL&#125;&quot;</span> <span class="attr">target</span>=<span class="string">&quot;_blank&quot;</span>&gt;</span>$&#123;SITE_NAME&#125;<span class="tag">&lt;/<span class="name">a</span>&gt;</span>上的文章有了新的评论<span class="tag">&lt;/<span class="name">h2</span>&gt;</span><span class="tag">&lt;<span class="name">p</span>&gt;</span><span class="tag">&lt;<span class="name">strong</span>&gt;</span>$&#123;NICK&#125;<span class="tag">&lt;/<span class="name">strong</span>&gt;</span>回复说：<span class="tag">&lt;/<span class="name">p</span>&gt;</span><span class="tag">&lt;<span class="name">div</span> <span class="attr">style</span>=<span class="string">&quot;background-color: #f5f5f5;padding: 10px 15px;margin:18px 0;word-wrap:break-word;&quot;</span>&gt;</span> $&#123;COMMENT&#125;<span class="tag">&lt;/<span class="name">div</span>&gt;</span><span class="tag">&lt;<span class="name">p</span>&gt;</span>您可以点击<span class="tag">&lt;<span class="name">a</span> <span class="attr">style</span>=<span class="string">&quot;text-decoration:none; color:#12addb&quot;</span> <span class="attr">href</span>=<span class="string">&quot;$&#123;POST_URL&#125;&quot;</span> <span class="attr">target</span>=<span class="string">&quot;_blank&quot;</span>&gt;</span>查看回复的完整內容<span class="tag">&lt;/<span class="name">a</span>&gt;</span><span class="tag">&lt;<span class="name">br</span>&gt;</span><span class="tag">&lt;/<span class="name">p</span>&gt;</span><span class="tag">&lt;/<span class="name">div</span>&gt;</span><span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br></pre></td></tr></table></figure>

<p>博主通知邮件模板中的可用变量与@通知中的基本一致，**<code>PARENT_NICK</code> 和 <code>PARENT_COMMENT</code> 变量不再可用。**</p>
<p>这里还提供一个彩虹风格的@通知邮件模板代码：</p>
<figure class="highlight html"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">div</span></span></span><br><span class="line"><span class="tag">  <span class="attr">style</span>=<span class="string">&quot;border-radius: 10px 10px 10px 10px;font-size:13px;    color: #555555;width: 666px;font-family:&#x27;Century Gothic&#x27;,&#x27;Trebuchet MS&#x27;,&#x27;Hiragino Sans GB&#x27;,微软雅黑,&#x27;Microsoft Yahei&#x27;,Tahoma,Helvetica,Arial,&#x27;SimSun&#x27;,sans-serif;margin:50px auto;border:1px solid #eee;max-width:100%;background: #ffffff repeating-linear-gradient(-45deg,#fff,#fff 1.125rem,transparent 1.125rem,transparent 2.25rem);box-shadow: 0 1px 5px rgba(0, 0, 0, 0.15);&quot;</span></span></span><br><span class="line"><span class="tag">&gt;</span></span><br><span class="line">  <span class="tag">&lt;<span class="name">div</span></span></span><br><span class="line"><span class="tag">    <span class="attr">style</span>=<span class="string">&quot;width:100%;background:#49BDAD;color:#ffffff;border-radius: 10px 10px 0 0;background-image: -moz-linear-gradient(0deg, rgb(67, 198, 184), rgb(255, 209, 244));background-image: -webkit-linear-gradient(0deg, rgb(67, 198, 184), rgb(255, 209, 244));height: 66px;&quot;</span></span></span><br><span class="line"><span class="tag">  &gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">p</span></span></span><br><span class="line"><span class="tag">      <span class="attr">style</span>=<span class="string">&quot;font-size:15px;word-break:break-all;padding: 23px 32px;margin:0;background-color: hsla(0,0%,100%,.4);border-radius: 10px 10px 0 0;&quot;</span></span></span><br><span class="line"><span class="tag">    &gt;</span></span><br><span class="line">      您在<span class="tag">&lt;<span class="name">a</span> <span class="attr">style</span>=<span class="string">&quot;text-decoration:none;color: #ffffff;&quot;</span> <span class="attr">href</span>=<span class="string">&quot;$&#123;SITE_URL&#125;&quot;</span>&gt;</span></span><br><span class="line">        $&#123;SITE_NAME&#125;&lt;/a</span><br><span class="line">      &gt;上的留言有新回复啦！</span><br><span class="line">    <span class="tag">&lt;/<span class="name">p</span>&gt;</span></span><br><span class="line">  <span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br><span class="line">  <span class="tag">&lt;<span class="name">div</span> <span class="attr">style</span>=<span class="string">&quot;margin:40px auto;width:90%&quot;</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">p</span>&gt;</span>$&#123;PARENT_NICK&#125; 同学，您曾在文章上发表评论：<span class="tag">&lt;/<span class="name">p</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">div</span></span></span><br><span class="line"><span class="tag">      <span class="attr">style</span>=<span class="string">&quot;background: #fafafa repeating-linear-gradient(-45deg,#fff,#fff 1.125rem,transparent 1.125rem,transparent 2.25rem);box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);margin:20px 0px;padding:15px;border-radius:5px;font-size:14px;color:#555555;&quot;</span></span></span><br><span class="line"><span class="tag">    &gt;</span></span><br><span class="line">      $&#123;PARENT_COMMENT&#125;</span><br><span class="line">    <span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">p</span>&gt;</span>$&#123;NICK&#125; 给您的回复如下：<span class="tag">&lt;/<span class="name">p</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">div</span></span></span><br><span class="line"><span class="tag">      <span class="attr">style</span>=<span class="string">&quot;background: #fafafa repeating-linear-gradient(-45deg,#fff,#fff 1.125rem,transparent 1.125rem,transparent 2.25rem);box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);margin:20px 0px;padding:15px;border-radius:5px;font-size:14px;color:#555555;&quot;</span></span></span><br><span class="line"><span class="tag">    &gt;</span></span><br><span class="line">      $&#123;COMMENT&#125;</span><br><span class="line">    <span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">p</span>&gt;</span></span><br><span class="line">      您可以点击<span class="tag">&lt;<span class="name">a</span></span></span><br><span class="line"><span class="tag">        <span class="attr">style</span>=<span class="string">&quot;text-decoration:none; color:#12addb&quot;</span></span></span><br><span class="line"><span class="tag">        <span class="attr">href</span>=<span class="string">&quot;$&#123;POST_URL&#125;#comments&quot;</span></span></span><br><span class="line"><span class="tag">        &gt;</span>查看回复的完整內容&lt;/a</span><br><span class="line">      &gt;，欢迎再次光临<span class="tag">&lt;<span class="name">a</span></span></span><br><span class="line"><span class="tag">        <span class="attr">style</span>=<span class="string">&quot;text-decoration:none; color:#12addb&quot;</span></span></span><br><span class="line"><span class="tag">        <span class="attr">href</span>=<span class="string">&quot;$&#123;SITE_URL&#125;&quot;</span></span></span><br><span class="line"><span class="tag">      &gt;</span></span><br><span class="line">        $&#123;SITE_NAME&#125;&lt;/a</span><br><span class="line">      &gt;。</span><br><span class="line">    <span class="tag">&lt;/<span class="name">p</span>&gt;</span></span><br><span class="line">    <span class="tag">&lt;<span class="name">style</span> <span class="attr">type</span>=<span class="string">&quot;text/css&quot;</span>&gt;</span><span class="language-css"></span></span><br><span class="line"><span class="language-css">      <span class="selector-tag">a</span><span class="selector-pseudo">:link</span> &#123;</span></span><br><span class="line"><span class="language-css">        <span class="attribute">text-decoration</span>: none;</span></span><br><span class="line"><span class="language-css">      &#125;</span></span><br><span class="line"><span class="language-css">      <span class="selector-tag">a</span><span class="selector-pseudo">:visited</span> &#123;</span></span><br><span class="line"><span class="language-css">        <span class="attribute">text-decoration</span>: none;</span></span><br><span class="line"><span class="language-css">      &#125;</span></span><br><span class="line"><span class="language-css">      <span class="selector-tag">a</span><span class="selector-pseudo">:hover</span> &#123;</span></span><br><span class="line"><span class="language-css">        <span class="attribute">text-decoration</span>: none;</span></span><br><span class="line"><span class="language-css">      &#125;</span></span><br><span class="line"><span class="language-css">      <span class="selector-tag">a</span><span class="selector-pseudo">:active</span> &#123;</span></span><br><span class="line"><span class="language-css">        <span class="attribute">text-decoration</span>: none;</span></span><br><span class="line"><span class="language-css">      &#125;</span></span><br><span class="line"><span class="language-css">    </span><span class="tag">&lt;/<span class="name">style</span>&gt;</span></span><br><span class="line">  <span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">div</span>&gt;</span></span><br></pre></td></tr></table></figure>

<p>效果如图：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cloud.panjunwen.com/2018/09/ping-mu-kuai-zhao-2018-09-15-xia-wu-5-17-21.png" alt="彩虹模板"></p>
<h4 id="垃圾评论检测"><a href="#垃圾评论检测" class="headerlink" title="垃圾评论检测"></a>垃圾评论检测</h4><blockquote>
<p>Akismet (Automattic Kismet)是应用广泛的一个垃圾留言过滤系统，其作者是大名鼎鼎的 WordPress 创始人 Matt Mullenweg，Akismet 也是 WordPress 默认安装的插件，其使用非常广泛，设计目标便是帮助博客网站来过滤留言 Spam。有了 Akismet 之后，基本上不用担心垃圾留言的烦恼了。<br>启用 Akismet 后，当博客再收到留言会自动将其提交到 Akismet 并与 Akismet 上的黑名单进行比对，如果名列该黑名单中，则该条留言会被标记为垃圾评论且不会发布。</p>
</blockquote>
<p>如果还没有 Akismet Key，你可以去 <a target="_blank" rel="noopener external nofollow noreferrer" href="https://akismet.com/development/">AKISMET FOR DEVELOPERS 免费申请一个</a>；<br><strong>当 AKISMET_KEY 设为 MANUAL_REVIEW 时，开启人工审核模式；</strong><br>如果你不需要反垃圾评论，Akismet Key 环境变量可以忽略。</p>
<p><strong>为了实现较为精准的垃圾评论识别，采集的判据除了评论内容、邮件地址和网站地址外，还包括评论者的 IP 地址、浏览器信息等，但仅在云引擎后台使用这些数据，确保隐私和安全。</strong></p>
<p><strong>如果使用了本站最新的 Valine 和 Valine Admin，并设置了 Akismet Key，可以有效地拦截垃圾评论。被标为垃圾的评论可以在管理页面取消标注。</strong></p>
<table>
<thead>
<tr>
<th align="left">环境变量</th>
<th align="left">示例</th>
<th align="left">说明</th>
</tr>
</thead>
<tbody><tr>
<td align="left">AKISMET_KEY</td>
<td align="left">xxxxxxxxxxxx</td>
<td align="left">[可选]Akismet Key 用于垃圾评论检测</td>
</tr>
</tbody></table>
<h4 id="防止云引擎休眠"><a href="#防止云引擎休眠" class="headerlink" title="防止云引擎休眠"></a>防止云引擎休眠</h4><p>关于自动休眠的官方说法：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://leancloud.cn/docs/leanengine_plan.html#hash633315134">点击查看</a></p>
<p>目前最新版的 Valine Admin 已经可以实现自唤醒，即在 LeanCloud 云引擎中定时请求 Web 应用地址防止休眠。对于夜间休眠期漏发的邮件通知，自动在次日早上补发。<strong>务必确保配置中设置了 ADMIN_URL 环境变量，并在第 5 步添加了两个云函数定时任务。</strong></p>
<h4 id="Troubleshooting"><a href="#Troubleshooting" class="headerlink" title="Troubleshooting"></a>Troubleshooting</h4><ul>
<li><p>部署失败，请在评论中附图，或去 Github 发起 Issue</p>
</li>
<li><p>邮件发送失败，确保环境变量都没问题后，重启云引擎</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cloud.panjunwen.com/2018/09/ping-mu-kuai-zhao-2018-09-15-xia-wu-5-22-56.png" alt="重启云引擎"></p>
</li>
<li><p>博主通知模板中不要出现<code>PARENT*</code>相关参数（请勿混用模板）</p>
</li>
<li><p>点击邮件中的链接跳转至相应评论，这一细节实现需要一点额外的代码：</p>
</li>
</ul>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line">&lt;script&gt;</span><br><span class="line">    <span class="keyword">if</span>(<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">hash</span>)&#123;</span><br><span class="line">        <span class="keyword">var</span> checkExist = <span class="built_in">setInterval</span>(<span class="keyword">function</span>(<span class="params"></span>) &#123;</span><br><span class="line">           <span class="keyword">if</span> ($(<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">hash</span>).<span class="property">length</span>) &#123;</span><br><span class="line">              $(<span class="string">&#x27;html, body&#x27;</span>).<span class="title function_">animate</span>(&#123;<span class="attr">scrollTop</span>: $(<span class="variable language_">window</span>.<span class="property">location</span>.<span class="property">hash</span>).<span class="title function_">offset</span>().<span class="property">top</span>-<span class="number">90</span>&#125;, <span class="number">1000</span>);</span><br><span class="line">              <span class="built_in">clearInterval</span>(checkExist);</span><br><span class="line">           &#125;</span><br><span class="line">        &#125;, <span class="number">100</span>);</span><br><span class="line">    &#125;</span><br><span class="line">&lt;/script&gt;</span><br></pre></td></tr></table></figure>

<ul>
<li>自定义邮件服务器地址和端口信息，删除 SMTP_SERVICE 环境变量，新增以下变量：</li>
</ul>
<table>
<thead>
<tr>
<th align="left">变量</th>
<th align="left">示例</th>
<th align="left">说明</th>
</tr>
</thead>
<tbody><tr>
<td align="left">SMTP_HOST</td>
<td align="left">smtp.qq.com</td>
<td align="left">[可选]SMTP_SERVICE 留空时，自定义 SMTP 服务器地址</td>
</tr>
<tr>
<td align="left">SMTP_PORT</td>
<td align="left">465</td>
<td align="left">[可选]SMTP_SERVICE 留空时，自定义 SMTP 端口</td>
</tr>
<tr>
<td align="left">SMTP_SECURE</td>
<td align="left">true</td>
<td align="left">[可选]SMTP_SERVICE 留空时填写</td>
</tr>
</tbody></table>
<ul>
<li>这是我的环境变量配置。</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200411210226.png" alt="环境变量配置"></p>
<h2 id="可能遇到的一些问题"><a href="#可能遇到的一些问题" class="headerlink" title="可能遇到的一些问题"></a>可能遇到的一些问题</h2><h3 id="部署失败"><a href="#部署失败" class="headerlink" title="部署失败"></a>部署失败</h3><p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200411210908.png" alt="部署失败"></p>
<p>这种情况是 node 的问题，你需要把项目 fork 到自己的仓库，并且修改<code>package.json</code></p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line">&quot;engines&quot;: &#123;</span><br><span class="line">  &quot;node&quot;: &quot;6.x&quot;</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p>修改为：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line">&quot;engines&quot;: &#123;</span><br><span class="line">  &quot;node&quot;: &quot;12.x&quot;</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p>然后把代码库改为你 fork 下来的仓库即可。</p>
<div class="note success flat"><p>目前作者好像已经修复了这个问题。(2020.4.26)</p>
</div>

<h3 id="邮件发送失败"><a href="#邮件发送失败" class="headerlink" title="邮件发送失败"></a>邮件发送失败</h3><p>可能是被判定为垃圾邮件被拒收了。我最开始使用的 126 邮箱，刚刚开始还可以，后面发送邮件全部被拒，查了一下，是因为被判定为垃圾邮件了，我就换成了 outlook 邮箱。具体的失败信息，可以查看 云引擎》应用日志 。在日志中可以查看失败的具体原因。</p>
<h2 id="参考资料"><a href="#参考资料" class="headerlink" title="参考资料"></a>参考资料</h2><p><a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/DesertsP/Valine-Admin">Valine-Admin</a></p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/cc0b1d61.html">http://blog.mhy.loc.cc/archives/cc0b1d61.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a><a class="post-meta__tags" href="/tags/Hexo/">Hexo</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/e358bc47.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Hexo框架(十七)：更换博客背景图片及图片压缩</div></div></a></div><div class="next-post pull-right"><a href="/archives/2f89d13b.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">练习题</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2022-02-10</div><div class="title">使用GitHub Actions 实现自动化部署和部署到服务器</div></div></a></div><div><a href="/archives/353666f0.html" title="给你的博客添加一个收藏页"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200719133158.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-19</div><div class="title">给你的博客添加一个收藏页</div></div></a></div><div><a href="/archives/f36eea83.html" title="Hexo博客添加emoji表情支持"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">Hexo博客添加emoji表情支持</div></div></a></div><div><a href="/archives/2798a2b1.html" title="给文章标题添加一个emoji表情"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">给文章标题添加一个emoji表情</div></div></a></div><div><a href="/archives/f06684a1.html" title="NexT版本更新V8.0记录"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-19</div><div class="title">NexT版本更新V8.0记录</div></div></a></div><div><a href="/archives/b1e33f9d.html" title="sitemeta渐变背景实现"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-03</div><div class="title">sitemeta渐变背景实现</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%B3%A8%E5%86%8C-Valine"><span class="toc-text">注册 Valine</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#Valine-%E7%9A%84%E4%B8%80%E4%BA%9B%E6%A0%B7%E5%BC%8F%E4%BF%AE%E6%94%B9"><span class="toc-text">Valine 的一些样式修改</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E9%85%8D%E7%BD%AE%E9%82%AE%E4%BB%B6%E6%8F%90%E9%86%92%E5%8A%9F%E8%83%BD"><span class="toc-text">配置邮件提醒功能</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%9F%BA%E7%A1%80%E8%AE%BE%E7%BD%AE"><span class="toc-text">基础设置</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%85%8D%E7%BD%AE-Valine-Admin"><span class="toc-text">配置 Valine Admin</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%BA%91%E5%BC%95%E6%93%8E%E2%80%9D%E4%B8%80%E9%94%AE%E2%80%9D%E9%83%A8%E7%BD%B2"><span class="toc-text">云引擎”一键”部署</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%82%AE%E4%BB%B6%E9%80%9A%E7%9F%A5%E6%A8%A1%E6%9D%BF"><span class="toc-text">邮件通知模板</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%9E%83%E5%9C%BE%E8%AF%84%E8%AE%BA%E6%A3%80%E6%B5%8B"><span class="toc-text">垃圾评论检测</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E9%98%B2%E6%AD%A2%E4%BA%91%E5%BC%95%E6%93%8E%E4%BC%91%E7%9C%A0"><span class="toc-text">防止云引擎休眠</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#Troubleshooting"><span class="toc-text">Troubleshooting</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%8F%AF%E8%83%BD%E9%81%87%E5%88%B0%E7%9A%84%E4%B8%80%E4%BA%9B%E9%97%AE%E9%A2%98"><span class="toc-text">可能遇到的一些问题</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%83%A8%E7%BD%B2%E5%A4%B1%E8%B4%A5"><span class="toc-text">部署失败</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%82%AE%E4%BB%B6%E5%8F%91%E9%80%81%E5%A4%B1%E8%B4%A5"><span class="toc-text">邮件发送失败</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%8F%82%E8%80%83%E8%B5%84%E6%96%99"><span class="toc-text">参考资料</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>