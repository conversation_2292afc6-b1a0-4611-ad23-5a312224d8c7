mixin articleSort(posts)
  .article-sort
    - let year
    - posts.forEach(article => {
      - const tempYear = date(article.date, 'YYYY')
      - const noCoverClass = article.cover === false || !theme.cover.archives_enable ? 'no-article-cover' : ''
      - const title = article.title || _p('no_title')
      if tempYear !== year
        - year = tempYear
        .article-sort-item.year= year
      .article-sort-item(class=noCoverClass)
        if article.cover && theme.cover.archives_enable
          a.article-sort-item-img(href=url_for(article.path) title=title)
            if article.cover_type === 'img'
              img(src=url_for(article.cover) alt=title onerror=`this.onerror=null;this.src='${url_for(theme.error_img.post_page)}'`)
            else
              div(style=`background: ${article.cover}`)
        .article-sort-item-info
          .article-sort-item-time
            i.far.fa-calendar-alt
            time.post-meta-date-created(datetime=date_xml(article.date) title=_p('post.created') + ' ' + full_date(article.date))= date(article.date, config.date_format)
          a.article-sort-item-title(href=url_for(article.path) title=title)= title
    - })