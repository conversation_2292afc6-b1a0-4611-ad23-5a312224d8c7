<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>练习题 | 你真是一个美好的人类</title><meta name="keywords" content="JavaScript,每天进步一点点,试题"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="主要是JavaScript的一些基础知识练习题。">
<meta property="og:type" content="article">
<meta property="og:title" content="练习题">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/2f89d13b.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="主要是JavaScript的一些基础知识练习题。">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg">
<meta property="article:published_time" content="2020-04-12T20:26:12.000Z">
<meta property="article:modified_time" content="2020-04-26T20:26:12.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="JavaScript">
<meta property="article:tag" content="每天进步一点点">
<meta property="article:tag" content="试题">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/2f89d13b"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: '练习题',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-04-26 20:26:12'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">练习题</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-04-12T20:26:12.000Z" title="发表于 2020-04-12 20:26:12">2020-04-12</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-04-26T20:26:12.000Z" title="更新于 2020-04-26 20:26:12">2020-04-26</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/">前端</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/JavaScript/">JavaScript</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%89%8D%E7%AB%AF/JavaScript/%E4%B9%A0%E9%A2%98%E9%9B%86/">习题集</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><h4 id="2020-4-12"><a href="#2020-4-12" class="headerlink" title="2020.4.12"></a>2020.4.12</h4><p>当我们这样做的时候会发生什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">function</span> <span class="title function_">bark</span>(<span class="params"></span>) &#123;</span><br><span class="line">  <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">&quot;woof!&quot;</span><span class="string">&quot;);</span></span><br><span class="line"><span class="string">&#125;</span></span><br><span class="line"><span class="string">bark.animal = &quot;</span>dog<span class="string">&quot;</span></span><br></pre></td></tr></table></figure>

<ul>
<li><input checked="" disabled="" type="checkbox"> A：Nothing,this is totally fine!</li>
<li><input disabled="" type="checkbox"> B：SyntaxError. You cannot add properties to a function this way.</li>
<li><input disabled="" type="checkbox"> C：Undefind</li>
<li><input disabled="" type="checkbox"> D：ReferenceError</li>
</ul>
<div class="note success flat"><p>解析：A。这在 JavaScript 中是可能的，因为函数也是对象！（原始类型之外的所有东西都是对象）。函数是一种特殊类型的对象，自己编写的代码并不是实际的函数。该函数是具有属性的对象，此属性是可调用的。</p>
</div>

<hr>
<h4 id="2020-4-13"><a href="#2020-4-13" class="headerlink" title="2020.4.13"></a>2020.4.13</h4><p>下面的代码会输出什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">function</span> <span class="title function_">Person</span>(<span class="params">firstName, lastName</span>) &#123;</span><br><span class="line">  <span class="variable language_">this</span>.<span class="property">firstName</span> = firstName</span><br><span class="line">  <span class="variable language_">this</span>.<span class="property">lastName</span> = lastName</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="keyword">const</span> lydia = <span class="keyword">new</span> <span class="title class_">Person</span>(<span class="string">&#x27;Lydia&#x27;</span>, <span class="string">&#x27;Hallie&#x27;</span>)</span><br><span class="line"><span class="keyword">const</span> sarah = <span class="title class_">Person</span>(<span class="string">&#x27;Sarah&#x27;</span>, <span class="string">&#x27;Smith&#x27;</span>)</span><br><span class="line"></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(lydia)</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(sarah)</span><br></pre></td></tr></table></figure>

<ul>
<li><input checked="" disabled="" type="checkbox"> A：Person{fistName: “Lydia”, lastName:”Hallie”} and undefined</li>
<li><input disabled="" type="checkbox"> B：Person{fistName: “Lydia”, lastName:”Hallie”} and Person{fistName: “Sarah”, lastName:”Smith”}</li>
<li><input disabled="" type="checkbox"> C：Person{fistName: “Lydia”, lastName:”Hallie”} and {}</li>
<li><input disabled="" type="checkbox"> D：Person{fistName: “Lydia”, lastName:”Hallie”} and ReferenceError</li>
</ul>
<div class="note success flat"><p>解析：A。对于 sarah，我们没有使用 new 关键字。使用 new 时，它指的是我们创建的新空对象，但是，如果你不添加 new，它指的是全局对象！我们制定了 this.firstName = “Sarah”和 this.lastName = “Samith”。我们实际做的是定于 global.firstName = “Sarah”和 global.lastName = “Samith”。sarah 本身返回值是 undefined。</p>
</div>

<hr>
<h4 id="2020-4-14"><a href="#2020-4-14" class="headerlink" title="2020.4.14"></a>2020.4.14</h4><p>下面代码的输出是什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">let</span> number = <span class="number">0</span></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(number++)</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(++number)</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(number)</span><br></pre></td></tr></table></figure>

<ul>
<li><input disabled="" type="checkbox"> A：1 1 2</li>
<li><input disabled="" type="checkbox"> B：1 2 2</li>
<li><input checked="" disabled="" type="checkbox"> C：0 2 2</li>
<li><input disabled="" type="checkbox"> D：0 1 2</li>
</ul>
<div class="note success flat"><p>解析：C。</p>
<ul>
<li><strong>后缀</strong> 一元运算符++：<ul>
<li>1、返回值（返回 0）；</li>
<li>2、增加值（现在数值是 1）。</li>
</ul>
</li>
<li><strong>前缀</strong> 一元运算符++：<ul>
<li>1、增加值（现在数值是 2）</li>
<li>2、返回值（返回 2）</li>
</ul>
</li>
<li>所以返回 0 2 2</li>
</ul>
</div>

<hr>
<h4 id="2020-4-15"><a href="#2020-4-15" class="headerlink" title="2020.4.15"></a>2020.4.15</h4><p>单击按钮时 <code>event.tatget</code> 是什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">&lt;div onclick=<span class="string">&quot;console.log(&#x27;first dib&#x27;)&quot;</span>&gt;</span><br><span class="line">  <span class="language-xml"><span class="tag">&lt;<span class="name">div</span> <span class="attr">onclick</span>=<span class="string">&quot;console.log(&#x27;second div)&quot;</span>&gt;</span></span></span><br><span class="line"><span class="language-xml">    <span class="tag">&lt;<span class="name">button</span> <span class="attr">onclick</span>=<span class="string">&quot;console.log(&#x27;button&#x27;)&quot;</span>&gt;</span>click<span class="tag">&lt;/<span class="name">button</span>&gt;</span></span></span><br><span class="line"><span class="language-xml">  <span class="tag">&lt;/<span class="name">div</span>&gt;</span></span></span><br><span class="line">&lt;/div&gt;</span><br></pre></td></tr></table></figure>

<ul>
<li><input disabled="" type="checkbox"> A：div 外部</li>
<li><input disabled="" type="checkbox"> B：div 内部</li>
<li><input checked="" disabled="" type="checkbox"> C：button</li>
<li><input disabled="" type="checkbox"> D：所有嵌套元素的数组</li>
</ul>
<div class="note success flat"><p>解析：C。导致事件的最深嵌套元素是事件的目标。你可以通过<code>event.stopPropagation</code> 停止冒泡。</p>
</div>

<hr>
<h4 id="2020-4-16"><a href="#2020-4-16" class="headerlink" title="2020.4.16"></a>2020.4.16</h4><p>下面代码的输出是什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">function</span> <span class="title function_">checkAge</span>(<span class="params">date</span>) &#123;</span><br><span class="line">  <span class="keyword">if</span> (data === &#123; <span class="attr">age</span>: <span class="number">18</span> &#125;) &#123;</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">&#x27;You are an abult&#x27;</span>)</span><br><span class="line">  &#125; <span class="keyword">else</span> <span class="keyword">if</span> (data == &#123; <span class="attr">age</span>: <span class="number">18</span> &#125;) &#123;</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">&#x27;You are still an abult&#x27;</span>)</span><br><span class="line">  &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">    <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">&#x27;Hmm..You donot have an age I guess&#x27;</span>)</span><br><span class="line">  &#125;</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="title function_">checkAge</span>(&#123; <span class="attr">age</span>: <span class="number">18</span> &#125;)</span><br></pre></td></tr></table></figure>

<ul>
<li><input disabled="" type="checkbox"> A：You are an abult</li>
<li><input disabled="" type="checkbox"> B：You are still an abult</li>
<li><input checked="" disabled="" type="checkbox"> C：Hmm..You donot have an age I guess</li>
</ul>
<div class="note success flat"><p>解析：C。在比较相等性，原始类型通过它们的值进行比较，而对象通过它们的引用进行比较。JavaScript 检查对象是否具有对内存中相同位置的引用。我们作为参数传递的对象和我们用于检查相等性的对象在内存中位于不同的位置，所以它们的引用是不同的。</p>
<p>这就是为什么 {age:18} === {age:18} 和 {age:18} == {age:18} 返回 false 的原因。</p>
</div>

<hr>
<h4 id="2020-4-17"><a href="#2020-4-17" class="headerlink" title="2020.4.17"></a>2020.4.17</h4><p>下面代码输出什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">function</span> <span class="title function_">sum</span>(<span class="params">a, b</span>) &#123;</span><br><span class="line">  <span class="keyword">return</span> a + b</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="title function_">sum</span>(<span class="number">1</span>, <span class="string">&#x27;2&#x27;</span>)</span><br></pre></td></tr></table></figure>

<ul>
<li><input disabled="" type="checkbox"> A：NaN</li>
<li><input disabled="" type="checkbox"> B：TypeError</li>
<li><input checked="" disabled="" type="checkbox"> C：”12”</li>
<li><input disabled="" type="checkbox"> D：3</li>
</ul>
<div class="note success flat"><p>解析：C。JavaScript 是一种动态类型语言：我们没有指定某些变量的类型。在您不知道的情况下，值可以自动转换为另一类型，称为隐式类型转换。 强制从一种类型转换为另一种类型。</p>
<p>在此示例中，JavaScript 将数字 1 转换为字符串，以使函数有意义并返回值。在让数字类型<code>1</code>和字符串类型<code>“2”</code>相加时，该数字被视为字符串。我们可以像连接<code> “hello”</code> +<code>“world”</code> 这样的字符串，所以这里发生的是<code>“1”</code>+<code>2”</code>，返回<code>“12” </code></p>
</div>

<hr>
<h4 id="2020-4-18"><a href="#2020-4-18" class="headerlink" title="2020.4.18"></a>2020.4.18</h4><p>所有对象都有原型？</p>
<ul>
<li><input disabled="" type="checkbox"> A：对</li>
<li><input checked="" disabled="" type="checkbox"> B：错误</li>
</ul>
<div class="note success flat"><p>解析：B。除基础对象外，所有对象都有原型。基础对象可以访问某些方法和属性，例如：<code>toString</code> 。这就是你可以使用内置 JavaScript 方法的原因！所有这些方法都可以在原型上找到。虽然 JavaScript 无法直接在你的对象上找到它，但它会沿着原型链向下寻找并在那里找到它，这使你可以访问它。</p>
<p>注：基础对象指原型链终点的对象。基础对象的原型是 <code>null</code></p>
</div>

<hr>
<h4 id="2020-4-19"><a href="#2020-4-19" class="headerlink" title="2020.4.19"></a>2020.4.19</h4><p>下面代码输出的是什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">const</span> obj = &#123; <span class="attr">a</span>: <span class="string">&#x27;one&#x27;</span>, <span class="attr">b</span>: <span class="string">&#x27;two&#x27;</span>, <span class="attr">c</span>: <span class="string">&#x27;three&#x27;</span> &#125;</span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(obj)</span><br></pre></td></tr></table></figure>

<ul>
<li><input disabled="" type="checkbox"> A：{a:”noe”, b:”two”}</li>
<li><input disabled="" type="checkbox"> B：{ b:”two”, a:”three”}</li>
<li><input checked="" disabled="" type="checkbox"> C：{a:”three”, b:”two”}</li>
<li><input disabled="" type="checkbox"> D：SyntaxError</li>
</ul>
<div class="note success flat"><p>解析：C。如果对象有两个具有相同名称的键，则将替换前面的键。它仍然处于原来的位置，但具有最后指定的值。</p>
</div>

<hr>
<h4 id="2020-4-20"><a href="#2020-4-20" class="headerlink" title="2020.4.20"></a>2020.4.20</h4><p>简述箭头函数和普通函数的区别？能不能作为构造函数？</p>
<ul>
<li><p>语法更加简洁、清晰</p>
</li>
<li><p>箭头函数不会创建自己的 this</p>
</li>
<li><p>箭头函数继承而来的 this 指向永远不变</p>
</li>
<li><p><code>.call()</code> / <code>.apply()</code> / <code>.bind()</code> 无法改变箭头函数中 this 的指向</p>
</li>
<li><p>箭头函数不能作为构造函数使用</p>
</li>
<li><p>箭头函数没有自己的 arguments</p>
</li>
<li><p>箭头函数没有原型 prototype</p>
</li>
<li><p>箭头函数不能用作 Generator 函数，不能使用 yeild 关键字</p>
</li>
</ul>
<hr>
<h4 id="2020-4-21"><a href="#2020-4-21" class="headerlink" title="2020.4.21"></a>2020.4.21</h4><p>下面代码的输出是什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">function</span> <span class="title function_">getAge</span>(<span class="params"></span>) &#123;</span><br><span class="line">  <span class="string">&#x27;use strict&#x27;</span></span><br><span class="line">  age = <span class="number">21</span></span><br><span class="line">  <span class="variable language_">console</span>.<span class="title function_">log</span>(age)</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="title function_">getAge</span>()</span><br></pre></td></tr></table></figure>

<ul>
<li><input disabled="" type="checkbox"> A：21</li>
<li><input disabled="" type="checkbox"> B：undefined</li>
<li><input checked="" disabled="" type="checkbox"> C：ReferenceError</li>
<li><input disabled="" type="checkbox"> D：TypeError</li>
</ul>
<div class="note success flat"><p>解析：C。使用 <code>use strict </code> 可以确保不会意外的声明全局变量。我们从未声明变量 <code>age</code> ,因此我们使用 <code>use strict</code> ，它会引发一个 ReferenceError。如果我们不使用 <code>use strict </code> ，它就会起作用，因为属性 <code>age</code> 会被添加到全局对象中。</p>
</div>

<hr>
<h4 id="2020-4-22"><a href="#2020-4-22" class="headerlink" title="2020.4.22"></a>2020.4.22</h4><p>下面的代码输出是什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">const</span> sum = <span class="built_in">eval</span>(<span class="string">&#x27;10*10+5&#x27;</span>)</span><br></pre></td></tr></table></figure>

<ul>
<li><input checked="" disabled="" type="checkbox"> A：105</li>
<li><input disabled="" type="checkbox"> B：”105”</li>
<li><input disabled="" type="checkbox"> C：TypeError</li>
<li><input disabled="" type="checkbox"> D：”10-10+5”</li>
</ul>
<div class="note success flat"><p>解析：A.。<code>eval</code> 会为字符串传递的代码求值。如果它是一个表达式，现在这种情况下一样，它会计算表达式。表达式为 <code>10*10+5</code> 计算得到 <code>105</code> 。</p>
</div>

<hr>
<h4 id="2020-4-23"><a href="#2020-4-23" class="headerlink" title="2020.4.23"></a>2020.4.23</h4><p>单击下面的 <code>html</code> 片段打印的内容是什么?</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line">&lt;div onclick=<span class="string">&quot;console.log(&#x27;div&#x27;)&gt;</span></span><br><span class="line"><span class="string">  &lt;p onclick=&quot;</span><span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">&#x27;p&#x27;</span>)<span class="string">&quot;&gt;</span></span><br><span class="line"><span class="string">    Click Here!</span></span><br><span class="line"><span class="string">  &lt;/p&gt;</span></span><br><span class="line"><span class="string">&lt;/div&gt;</span></span><br></pre></td></tr></table></figure>

<ul>
<li><input checked="" disabled="" type="checkbox"> A：p div</li>
<li><input disabled="" type="checkbox"> B：div p</li>
<li><input disabled="" type="checkbox"> C：p</li>
<li><input disabled="" type="checkbox"> D：div</li>
</ul>
<div class="note success flat"><p>解析：A。如果我们单击 <code>p</code> 我们会看到两个日志， <code>p</code> 和 <code>div</code> 。在事件传播期间，有三个阶段：捕获，目标和冒泡。默认情况下，事件处理程序在冒泡阶段执行（除非您将 <code>useCapture</code> 设置为 <code>true</code> ）。它从最深的嵌套元素向外延伸。</p>
</div>

<hr>
<h4 id="2020-4-24"><a href="#2020-4-24" class="headerlink" title="2020.4.24"></a>2020.4.24</h4><p>下面代码的输出是什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="title class_">String</span>.<span class="property"><span class="keyword">prototype</span></span>.<span class="property">giveLydiaPizza</span> = <span class="function">() =&gt;</span> &#123;</span><br><span class="line">  <span class="keyword">return</span> <span class="string">&quot;Just give Lydia pizza already!“</span></span><br><span class="line"><span class="string">&#125;;</span></span><br><span class="line"><span class="string">const name = &quot;</span><span class="title class_">Lydia</span><span class="string">&quot;;</span></span><br><span class="line"><span class="string">name.giveLydiaPizza()</span></span><br></pre></td></tr></table></figure>

<ul>
<li><input checked="" disabled="" type="checkbox"> A：”Just give Lydia pizza already!“</li>
<li><input disabled="" type="checkbox"> B：TypeError:not a function</li>
<li><input disabled="" type="checkbox"> C：SyntaxError</li>
<li><input disabled="" type="checkbox"> D：undefined</li>
</ul>
<div class="note success flat"><p>解析：A。<code>String</code> 是一个内置的构造函数，我们可以为它添加属性。我刚给它的原型添加了一个方法。原始类型的字符串自动转换为字符串对象，由字符串原型函数生成。因此，所有字符串（字符串对象）都可以访问该方法。</p>
<p>注：当使用基本类型的字符串调用 <code>giveLydiaPizza</code> 时，实际上发生了下面的过程：</p>
<ul>
<li>创建一个 <code>String</code> 的包装类型实例</li>
<li>在实例上调用 <code>substring</code> 方法</li>
<li>销毁实例</li>
</ul>
</div>

<hr>
<h4 id="2020-4-25"><a href="#2020-4-25" class="headerlink" title="2020.4.25"></a>2020.4.25</h4><p>下面代码的输出是什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="keyword">typeof</span> <span class="keyword">typeof</span> <span class="number">1</span>)</span><br></pre></td></tr></table></figure>

<ul>
<li><input disabled="" type="checkbox"> A：”number”</li>
<li><input checked="" disabled="" type="checkbox"> B：”string”</li>
<li><input disabled="" type="checkbox"> C：”object”</li>
<li><input disabled="" type="checkbox"> D：”undefined”</li>
</ul>
<div class="note success flat"><p>解析：B。<code>typeof 1</code> 返回 <code>“number”</code> ，<code>typeof &quot;number&quot;</code> 返回 <code>&quot;string&quot;</code></p>
</div>

<hr>
<h4 id="2020-4-26"><a href="#2020-4-26" class="headerlink" title="2020.4.26"></a>2020.4.26</h4><p>下面代码的输出是什么？</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">const</span> numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]</span><br><span class="line">numbers[<span class="number">10</span>] = <span class="number">11</span></span><br><span class="line"><span class="variable language_">console</span>.<span class="title function_">log</span>(numbers)</span><br></pre></td></tr></table></figure>

<ul>
<li><input disabled="" type="checkbox"> A：[1, 2, 3, 7 x null, 11]</li>
<li><input disabled="" type="checkbox"> B：[1, 2, 3, 11]</li>
<li><input checked="" disabled="" type="checkbox"> C：[1, 2, 3, 7 x empty, 11]</li>
<li><input disabled="" type="checkbox"> D：SyntaxError</li>
</ul>
<div class="note success flat"><p>解析：C。当你为数组中的元素设置一个超过数组长度的值是，JavaScript 会创建一个名为 “空插槽” 的东西。这些位置的值实际上是 <code>undefined</code> ,但你会看到类似的东西：<code>[1, 2, 3, 7xempty, 11]</code> 这取决于你运行它的位置（每个浏览器有可能不同）。</p>
</div>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/2f89d13b.html">http://blog.mhy.loc.cc/archives/2f89d13b.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/JavaScript/">JavaScript</a><a class="post-meta__tags" href="/tags/%E6%AF%8F%E5%A4%A9%E8%BF%9B%E6%AD%A5%E4%B8%80%E7%82%B9%E7%82%B9/">每天进步一点点</a><a class="post-meta__tags" href="/tags/%E8%AF%95%E9%A2%98/">试题</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/cc0b1d61.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Hexo框架(十六)：Valine评论系统配置邮件提醒功能</div></div></a></div><div class="next-post pull-right"><a href="/archives/ce7a0d96.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Hexo框架(十五)：代码块折叠效果</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/292a04a3.html" title="JavaScript数据结构和算法：单向链表"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-12</div><div class="title">JavaScript数据结构和算法：单向链表</div></div></a></div><div><a href="/archives/14862ea.html" title="JavaScript数据结构和算法：队列"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-03</div><div class="title">JavaScript数据结构和算法：队列</div></div></a></div><div><a href="/archives/d343d10e.html" title="JavaScript数据结构和算法：栈结构"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-06-28</div><div class="title">JavaScript数据结构和算法：栈结构</div></div></a></div><div><a href="/archives/838e5b66.html" title="JavaScript数据结构和算法：数组"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-06-25</div><div class="title">JavaScript数据结构和算法：数组</div></div></a></div><div><a href="/archives/b2cd753e.html" title="ES6标准入门(十)：class类中constructor和name问题"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185259.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-11-03</div><div class="title">ES6标准入门(十)：class类中constructor和name问题</div></div></a></div><div><a href="/archives/16f8f18e.html" title="ES6标准入门(九)：Proxy"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185259.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-10-28</div><div class="title">ES6标准入门(九)：Proxy</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-12"><span class="toc-text">2020.4.12</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-13"><span class="toc-text">2020.4.13</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-14"><span class="toc-text">2020.4.14</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-15"><span class="toc-text">2020.4.15</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-16"><span class="toc-text">2020.4.16</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-17"><span class="toc-text">2020.4.17</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-18"><span class="toc-text">2020.4.18</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-19"><span class="toc-text">2020.4.19</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-20"><span class="toc-text">2020.4.20</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-21"><span class="toc-text">2020.4.21</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-22"><span class="toc-text">2020.4.22</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-23"><span class="toc-text">2020.4.23</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-24"><span class="toc-text">2020.4.24</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-25"><span class="toc-text">2020.4.25</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#2020-4-26"><span class="toc-text">2020.4.26</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>