{".mine": [{"nickname": "评论管理", "site": "https://valine.juanertu.com", "content": "valine-admin后台评论管理系统"}, {"nickname": "又拍云控制台", "site": "https://console.upyun.com/dashboard/", "content": "CDN管理，刷新CDN缓存"}, {"nickname": "LeanCloud控制台", "site": "https://leancloud.cn/dashboard/applist.html#/apps", "content": "应用管理，数据控制"}, {"nickname": "百度自动推送", "site": "http://oloo8858xbop.leanapp.cn/", "content": "查看博客的每天自动推送情况"}, {"nickname": "百度统计", "site": "https://tongji.baidu.com/web/10000188477/overview", "content": "查看网站的百度统计信息"}, {"nickname": "开课吧", "site": "https://appaadih2af2521.pc.xiaoe-tech.com/bought", "content": "学习网站"}, {"nickname": "网易云课堂", "site": "https://study.163.com/my", "content": "学习网站"}, {"nickname": "NexT", "site": "https://theme-next.org/docs/", "content": "NexT主题官方文档"}, {"nickname": "butterfly", "site": "https://demo.jerryc.me/posts/21cfbf15/", "content": "butterfly主题官方文档"}], ".docs": [{"nickname": "JavaScript", "site": "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference", "content": "JavaScript API 文档"}, {"nickname": "HTML", "site": "https://developer.mozilla.org/en-US/docs/Web/HTML/Element", "content": "HTML API 文档"}, {"nickname": "CSS", "site": "https://developer.mozilla.org/en-US/docs/Web/CSS/Reference", "content": "CSS API 文档"}, {"nickname": "Python", "site": "https://docs.python.org/", "content": "Python API 文档"}, {"nickname": "PHP", "site": "https://www.php.net/manual/zh/index.php", "content": "PHP API 文档"}, {"nickname": "SQL", "site": "https://docs.microsoft.com/zh-cn/sql/", "content": "SQL API 文档"}, {"nickname": "Node.js", "site": "http://nodejs.cn/", "content": "Node.js API 文档"}, {"nickname": "MongoDB", "site": "https://docs.mongodb.com/manual/", "content": "MongoDB API 文档"}, {"nickname": "Vue.js", "site": "https://cn.vuejs.org/v2/guide/", "content": "一套用于构建用户界面的渐进式框架。"}, {"nickname": "Vuex", "site": "https://vuex.vuejs.org/zh/", "content": "一个专为Vue.js应用程序开发的状态管理模式"}, {"nickname": "Vue Router", "site": "https://router.vuejs.org/zh/", "content": "Vue.js官方路由管理器"}, {"nickname": "Puppeteer中文文档", "site": "https://zhaoqize.github.io/puppeteer-api-zh_CN/", "content": "一个Node库，它提供了一个高级 API 来通过 DevTools 协议控制 Chromium 或 Chrome。Puppeteer 默认以 headless 模式运行，但是可以通过修改配置文件运行“有头”模式。"}, {"nickname": "axios中文文档", "site": "http://www.axios-js.com/", "content": "易用、简洁且高效的http库"}, {"nickname": "Express中文文档", "site": "https://www.expressjs.com.cn/", "content": "基于Node.js平台，快速、开放、极简的Web开发框架"}, {"nickname": "<PERSON><PERSON>", "site": "https://www.layui.com/", "content": "经典模块化前端UI框架"}], ".code": [{"nickname": "<PERSON><PERSON><PERSON>", "site": "https://github.com/", "content": "全球最大的面向开源及私有软件项目的托管平台"}, {"nickname": "Gitlab", "site": "https://gitlab.com/", "content": "支持无限的公有项目和私有项目的代码托管平台"}, {"nickname": "Coding", "site": "https://coding.net/", "content": "国内首个一站式云端软件服务平台"}, {"nickname": "<PERSON><PERSON><PERSON>", "site": "https://gitee.com/", "content": "国内最大的开源社区 OSChina 的代码托管平台"}, {"nickname": "阿里云代码托管", "site": "https://code.aliyun.com/", "content": "阿里云旗下代码托管平台"}, {"nickname": "百度效率云", "site": "http://xiaolvyun.baidu.com/", "content": "百度云旗下的 Git 代码托管平台"}, {"nickname": "SourceForge", "site": "https://sourceforge.net/", "content": "又称 SF.net，是开源软件开发者进行开发管理的集中式场所"}, {"nickname": "Bitbucket", "site": "https://bitbucket.org/", "content": "同时支持 Git 和 Mercurial 这两个版本控制软件，免费的私有仓库，支持5人以内的合作开发"}, {"nickname": "npm", "site": "https://www.npmjs.com/", "content": "JavaScript 世界的包管理工具,并且是 Node.js 平台的默认包管理工具。通过 npm 可以安装、共享、分发代码,管理项目依赖关系"}], ".skill": [{"nickname": "LeetCode", "site": "https://leetcode-cn.com/", "content": "全球极客挚爱的技术成长平台"}, {"nickname": "Topcoder", "site": "https://www.topcoder.com/", "content": "全世界规模最大的程序竞赛网站，也会有一些算法竞赛，适合一些高端的或者搞ACM的，也会举办一些比赛"}, {"nickname": "Codeforces", "site": "http://codeforces.com/", "content": "罗斯最大的算法比赛网站"}, {"nickname": "Hihocoder", "site": "http://www.hihocoder.com/", "content": "技术团队来自原北京大学POJ (PKU Online Judge)开发团队，收集了全球范围内很多地区、高校举办的比赛试题， 提供365天*24小时的在线提交、评判程序的服务"}, {"nickname": "LintCode", "site": "https://www.lintcode.com/", "content": "被称作中文版的leetcode，也是可以做为编程能力提升的一个中转站"}, {"nickname": "SPOJ", "site": "https://www.spoj.com/", "content": "波兰的算法刷题网站"}, {"nickname": "NEUQ OJ", "site": "http://acmclub.cn/", "content": "一个在线的判题平台"}, {"nickname": "洛谷", "site": "https://www.luogu.com.cn/", "content": "创办于2013年，致力于为参加noip、noi、acm的选手提供清爽、快捷的编程体验"}, {"nickname": "牛客网", "site": "https://www.nowcoder.com/contestRoom", "content": "中国最大的IT题库"}, {"nickname": "计蒜客", "site": "https://nanti.jisuanke.com/oi", "content": "计蒜客OI题库致力于为参加noi、noip、信息学竞赛的选手提供优秀的Online Judge系统"}, {"nickname": "C语言网", "site": "https://www.dotcpp.com/oj/contest.html", "content": "在这里可以参加包括ACM、NOI在内的各种C/C++/java程序比赛，也可以DIY举办各类程序比赛活动！"}], ".school": [{"nickname": "菜鸟教程", "site": "https://www.runoob.com/", "content": "提供了编程的基础技术教程, 介绍了HTML、CSS、Javascript、Python，Java，Ruby，C，PHP , MySQL等各种编程语言的基础知识"}, {"nickname": "W3school", "site": "https://www.w3school.com.cn/", "content": "领先的 Web 技术教程"}, {"nickname": "前端网", "site": "http://www.qianduandaxue.com/", "content": "最好的自学web前端网站"}], ".community": [{"nickname": "Stack Overflow", "site": "https://stackoverflow.com/", "content": "全球最受程序员欢迎的开发社区"}, {"nickname": "CSDN", "site": "https://www.csdn.net/", "content": "全球最大中文IT社区，为IT专业技术人员提供最全面的信息传播和服务平台"}, {"nickname": "博客园", "site": "https://www.cnblogs.com", "content": "代码改变世界"}, {"nickname": "V2EX", "site": "https://www.v2ex.com/", "content": "V2EX = way to explore"}, {"nickname": "掘金", "site": "https://juejin.im/", "content": "一个帮助开发者成长的社区"}, {"nickname": "SegmentFault", "site": "https://segmentfault.com/", "content": "改变并提升人们获取知识的方式和效率，帮助更多的开发者获得成长与成功"}, {"nickname": "开源中国", "site": "https://www.oschina.net/", "content": "国内最大的开源技术社区"}, {"nickname": "ITeye", "site": "https://www.iteye.com/", "content": "ITeye软件开发交流社区 - Java编程 Spring框架 Ajax技术 agile敏捷软件开发 ruby on rails实践"}, {"nickname": "51CTO", "site": "https://www.51cto.com/", "content": "技术成就梦想"}, {"nickname": "ITPUB", "site": "http://www.itpub.net/", "content": "全球最大的学习分享平台"}, {"nickname": "知乎", "site": "https://www.zhihu.com/", "content": "国内最受欢迎的知识性问答社区"}, {"nickname": "简书", "site": "https://www.jianshu.com/", "content": "创作你的创作"}, {"nickname": "云+社区", "site": "https://cloud.tencent.com/developer", "content": "来自腾讯的开发者技术分享社区"}, {"nickname": "云栖社区", "site": "https://yq.aliyun.com/", "content": "阿里云面向开发者的开放型技术平台"}, {"nickname": "极术社区", "site": "https://aijishu.com/", "content": "全球软硬件资源信息，只要你有问题，我们就能解答。"}], ".serve": [{"nickname": "阿里云", "site": "https://www.aliyun.com/", "content": "为了无法计算的价值"}, {"nickname": "腾讯云", "site": "https://cloud.tencent.com/", "content": "产业智变 云启未来"}, {"nickname": "百度云", "site": "https://cloud.baidu.com/", "content": "计算无限可能"}, {"nickname": "七牛云", "site": "https://www.qiniu.com/", "content": "国内领先的企业级云服务商"}, {"nickname": "又拍云", "site": "https://www.upyun.com/?utm_source=lianmeng&utm_medium=referral", "content": "专注 CDN、云存储、小程序开发方案、 短视频开发方案、DDoS高防等产品的国内知名企业级云服务商"}, {"nickname": "Cloudflare", "site": "https://www.cloudflare.com/", "content": "网络性能和安全公司"}, {"nickname": "jsDelivr", "site": "https://www.jsdelivr.com/", "content": "A free, fast, and reliable Open Source CDN for npm and GitHub"}], ".sitetool": [{"nickname": "百度站长平台", "site": "https://ziyuan.baidu.com/", "content": "百度搜索资源平台 - 让网站更具价值"}, {"nickname": "Google 站长平台", "site": "http://www.google.cn/webmasters/", "content": "支持、学习、互动交流和 Search Console – Google"}, {"nickname": "Bing 网站管理员工具", "site": "https://www.bing.com/toolbox/webmaster/", "content": "Bing 网站管理员工具"}, {"nickname": "站长工具 - 站长之家", "site": "http://tool.chinaz.com/", "content": "SEO工具,权重查询,收录查询,PR查询,ICP备案查询,whois查询,友情链接查询,反向链接查询,网站测试,<PERSON>查询,Alexa查询"}, {"nickname": "搜狗站长平台", "site": "http://zhanzhang.sogou.com/", "content": "全面掌握在搜狗搜索中的数据表现"}, {"nickname": "360 站长平台", "site": "http://zhanzhang.so.com/", "content": "给网站带来更多流量和展现"}, {"nickname": "超级 SEO 外链工具", "site": "https://tool.lusongsong.com/seo/", "content": "网站自动化宣传机器/免费的超级外链工具可批量增加外链"}, {"nickname": "域名备案管理系统", "site": "http://www.beian.miit.gov.cn/", "content": "工业和信息化部ICP/IP地址/域名信息备案管理系统"}, {"nickname": "公安备案网", "site": "http://beian.gov.cn/", "content": "全国互联网安全管理服务平台"}], ".design": [{"nickname": "Iconfont", "site": "https://www.iconfont.cn/", "content": "阿里巴巴矢量图标库，提供矢量图标下载、在线存储、格式转换等功能"}, {"nickname": "Font Awesome", "site": "https://fa5.dashgame.com/#/%E5%9B%BE%E6%A0%87", "content": "一个基于CSS 和 LESS 的字体和图标工具包"}, {"nickname": "Flaticon", "site": "https://www.flaticon.com/", "content": "海量扁平化免费的图标库"}, {"nickname": "icons8", "site": "https://icons8.com/", "content": "独特系统平台风格和web图标库，下载免费图标，音乐"}, {"nickname": "Unsplash", "site": "https://unsplash.com/", "content": "质量超高的免费图片素材库，无需注册，直接下载"}, {"nickname": "Mixkit", "site": "https://mixkit.co/", "content": "免费、高质量、可商用的视频素材分享网站"}, {"nickname": "WebGradients", "site": "https://webgradients.com/", "content": "渐变色搭配方案"}, {"nickname": "CoolHue2.0", "site": "https://webkul.github.io/coolhue/", "content": "渐变色搭配方案"}, {"nickname": "Gradient", "site": "https://gradient.shapefactory.co/?a=540A43&b=629EA1&d=25", "content": "渐变色搭配方案"}, {"nickname": "UiGradients", "site": "https://uigradients.com/#JShine", "content": "渐变色搭配方案"}], ".tools": [{"nickname": "智图", "site": "https://zhitu.isux.us/", "content": "腾讯ISUX前端团队开发的一个专门用于图片压缩和图片格式转换的平台"}, {"nickname": "程序员的工具箱", "site": "https://tool.lu/", "content": "站长工具、代码格式化、压缩、加密、解密、下载链接转换等"}, {"nickname": "OSCHINA.NET社区", "site": "https://tool.oschina.net/", "content": "常用文档、常用对照表、代码处理、Html/Js/Css工具、加密/转码工具等"}, {"nickname": "记磊工具箱", "site": "https://tools.ly522.com/", "content": "Dns检测、CSS格式化、超级Ping、端口扫描等"}, {"nickname": "孟坤工具箱", "site": "http://tool.mkblog.cn/", "content": "css一键美化、文本差异比较、代码高亮等"}, {"nickname": "Text to ASCII", "site": "http://patorjk.com/software/taag/", "content": "Text to ASCII Art Generator，字符串转成 ASCII 码图案"}, {"nickname": "临时邮箱", "site": "https://linshiyouxiang.net/", "content": "匿名注册不常用的网站/论坛，保护隐私免骚扰"}, {"nickname": "TinyPNG", "site": "https://tinypng.com/", "content": "PNG/JPG图片在线压缩利器"}, {"nickname": "图片格式在线转换", "site": "https://ezgif.com/png-to-webp", "content": "webp图片格式在线转换"}, {"nickname": "SVGOMG", "site": "https://jakearchibald.github.io/svgomg/", "content": "SVG在线压缩平台"}, {"nickname": "Loading", "site": "https://loading.io/", "content": "制作GIF、SVG、CSS加载动画图标"}, {"nickname": "Preloaders", "site": "https://www.logaster.cn/", "content": "Loading 懒加载动画在线制作"}, {"nickname": "移除图片背景", "site": "https://www.remove.bg/", "content": "一键智能抠图"}, {"nickname": "KMS服务", "site": "https://kms.avg.cx/", "content": "KMS激活Windows一键脚本"}]}