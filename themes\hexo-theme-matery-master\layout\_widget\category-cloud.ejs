<%
var colorArr = ['#F9EBEA', '#F5EEF8', '#D5F5E3', '#E8F8F5', '#FEF9E7',
    '#F8F9F9', '#82E0AA', '#D7BDE2', '#A3E4D7', '#85C1E9', '#F8C471', '#F9E79F', '#FFF'];
var colorCount = colorArr.length;
var hashCode = function (str) {
    if (!str && str.length === 0) {
        return 0;
    }

    var hash = 0;
    for (var i = 0, len = str.length; i < len; i++) {
        hash = ((hash << 5) - hash) + str.charCodeAt(i);
        hash |= 0;
    }
    return hash;
};
var i = 0;
var isCategory = is_category();
%>

<div id="category-cloud" class="container chip-container">
    <div class="card">
        <div class="card-content">
            <div class="tag-title center-align">
                <i class="fas fa-bookmark"></i>&nbsp;&nbsp;<%= __('postCategoryTitle') %>
            </div>
            <div class="tag-chips">
                <% if (site.categories && site.categories.length > 0) { %>
                <% site.categories.map(function(category) { %>
                <%
                    i++;
                    var color = i >= colorCount
                            ? colorArr[Math.abs(hashCode(category.name) % colorCount)]
                            : colorArr[i - 1];
                %>
                <a href="<%- url_for(category.path) %>" title="<%- category.name %>: <%- category.length %>">
                    <span class="chip center-align waves-effect waves-light
                            <% if (isCategory && category.name == page.category) { %> chip-active <% } else { %> chip-default <% } %>"
                            style="background-color: <%- color %>;"><%- category.name %>
                        <span class="tag-length"><%- category.length %></span>
                    </span>
                </a>
                <% }); %>
                <% } else { %>
                <%= __('categoryEmptyTip') %>
                <% } %>
            </div>
        </div>
    </div>
</div>
