<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Node爬虫实战 | 你真是一个美好的人类</title><meta name="keywords" content="Node,Node爬虫,实战代码"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="Node爬虫实战代码的记录，简单总结了心得。">
<meta property="og:type" content="article">
<meta property="og:title" content="Node爬虫实战">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/b07ae32c.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="Node爬虫实战代码的记录，简单总结了心得。">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png">
<meta property="article:published_time" content="2019-12-26T11:02:11.000Z">
<meta property="article:modified_time" content="2019-12-26T11:02:11.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="Node">
<meta property="article:tag" content="Node爬虫">
<meta property="article:tag" content="实战代码">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/b07ae32c"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Node爬虫实战',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2019-12-26 11:02:11'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Node爬虫实战</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2019-12-26T11:02:11.000Z" title="发表于 2019-12-26 11:02:11">2019-12-26</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2019-12-26T11:02:11.000Z" title="更新于 2019-12-26 11:02:11">2019-12-26</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/Node/">Node</a><i class="fas fa-angle-right post-meta-separator"></i><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/Node/Node%E7%88%AC%E8%99%AB/">Node爬虫</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><p>这几天对 Node 编写小爬虫进行了学习，这里记录一下实战的代码。</p>
<h2 id="爬取网页信息"><a href="#爬取网页信息" class="headerlink" title="爬取网页信息"></a>爬取网页信息</h2><figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">let</span> axios = <span class="built_in">require</span>(<span class="string">&#x27;axios&#x27;</span>)</span><br><span class="line"><span class="keyword">let</span> &#123; fsRead, fsWrite, fsDir &#125; = <span class="built_in">require</span>(<span class="string">&#x27;./rw&#x27;</span>)</span><br><span class="line"><span class="keyword">let</span> httpUrl = <span class="string">&#x27;https://www.1905.com/vod/list/n_1_t_1/o3p1.html&#x27;</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">req</span>(<span class="params">link</span>) &#123;</span><br><span class="line">  <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">Promise</span>(<span class="keyword">function</span> (<span class="params">resolve, reject</span>) &#123;</span><br><span class="line">    axios.<span class="title function_">get</span>(link).<span class="title function_">then</span>(<span class="keyword">function</span> (<span class="params">res</span>) &#123;</span><br><span class="line">      <span class="title function_">resolve</span>(res.<span class="property">data</span>)</span><br><span class="line">    &#125;)</span><br><span class="line">  &#125;)</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 获取起始页面的所有分类地址</span></span><br><span class="line"><span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">getClassUrl</span>(<span class="params">link</span>) &#123;</span><br><span class="line">  <span class="keyword">let</span> data = <span class="keyword">await</span> <span class="title function_">req</span>(link)</span><br><span class="line">  <span class="comment">// 解析HTML内容</span></span><br><span class="line">  <span class="keyword">let</span> reg = <span class="regexp">/&lt;span class=&quot;search-index-L&quot;&gt;类型(.*?)&lt;div class=&quot;grid-12x&quot;&gt;/gi</span>s</span><br><span class="line">  <span class="keyword">let</span> result = reg.<span class="title function_">exec</span>(data)[<span class="number">1</span>]</span><br><span class="line">  <span class="comment">// console.log(result);</span></span><br><span class="line">  <span class="keyword">let</span> reg1 = <span class="regexp">/&lt;a href=&quot;javascript\:void\(0\);&quot; onclick=&quot;location\.href=&#x27;(.*?)&#x27;;return false;&quot; .*?&gt;(.*?)&lt;\/a&gt;/gi</span>s</span><br><span class="line">  <span class="keyword">let</span> arr = []</span><br><span class="line"></span><br><span class="line">  <span class="keyword">while</span> ((result2 = reg1.<span class="title function_">exec</span>(result))) &#123;</span><br><span class="line">    <span class="keyword">if</span> (result2[<span class="number">2</span>] != <span class="string">&#x27;全部&#x27;</span>) &#123;</span><br><span class="line">      <span class="keyword">let</span> obj = &#123;</span><br><span class="line">        <span class="attr">className</span>: result2[<span class="number">2</span>],</span><br><span class="line">        <span class="attr">link</span>: result2[<span class="number">1</span>],</span><br><span class="line">      &#125;</span><br><span class="line">      arr.<span class="title function_">push</span>(obj)</span><br><span class="line">      <span class="keyword">await</span> <span class="title function_">fsDir</span>(<span class="string">&#x27;./movies/&#x27;</span> + result2[<span class="number">2</span>])</span><br><span class="line">      <span class="title function_">getMovie</span>(result2[<span class="number">1</span>], result2[<span class="number">2</span>])</span><br><span class="line">    &#125;</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="comment">// console.log(arr);</span></span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 获取分类里的电影连接</span></span><br><span class="line"><span class="comment">// 根据电影连接获取电影的详细信息</span></span><br><span class="line"><span class="comment">// 通过分类获取页面中的连接</span></span><br><span class="line"><span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">getMovie</span>(<span class="params">link, movieType</span>) &#123;</span><br><span class="line">  <span class="keyword">let</span> data = <span class="keyword">await</span> <span class="title function_">req</span>(link)</span><br><span class="line">  <span class="keyword">let</span> reg = <span class="regexp">/&lt;a class=&quot;pic-pack-outer&quot; target=&quot;\_blank&quot; href=&quot;(.*?)&quot; .*?&gt;/gi</span>s</span><br><span class="line">  <span class="keyword">var</span> res9</span><br><span class="line">  <span class="keyword">var</span> arrList = []</span><br><span class="line">  <span class="keyword">while</span> ((res9 = reg.<span class="title function_">exec</span>(data))) &#123;</span><br><span class="line">    arrList.<span class="title function_">push</span>(res9[<span class="number">1</span>])</span><br><span class="line">    <span class="title function_">parsePage</span>(res9[<span class="number">1</span>], movieType)</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="comment">// console.log(&quot;分类：&quot; + movieType);</span></span><br><span class="line">  <span class="comment">// console.log(arrList);</span></span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">parsePage</span>(<span class="params">url, movieType</span>) &#123;</span><br><span class="line">  <span class="comment">// console.log(url);</span></span><br><span class="line">  <span class="keyword">let</span> data = <span class="keyword">await</span> <span class="title function_">req</span>(url)</span><br><span class="line">  <span class="keyword">let</span> reg = <span class="regexp">/&lt;h1 class=&quot;playerBox-info-name playerBox-info-cnName&quot;&gt;(.*?)&lt;\/h1&gt;.*?id=&quot;playerBoxIntroCon&quot;&gt;(.*?)&lt;a .*?导演.*?data-hrefexp=&quot;fr=vodplay\_ypzl\_dy&quot;&gt;(.*?)&lt;\/a&gt;/gi</span>s</span><br><span class="line">  <span class="keyword">let</span> res3 = reg.<span class="title function_">exec</span>(data)</span><br><span class="line">  <span class="variable language_">console</span>.<span class="title function_">log</span>(res3[<span class="number">1</span>])</span><br><span class="line">  <span class="keyword">let</span> movie = &#123;</span><br><span class="line">    <span class="attr">name</span>: res3[<span class="number">1</span>],</span><br><span class="line">    <span class="attr">brief</span>: res3[<span class="number">2</span>],</span><br><span class="line">    <span class="attr">daoyan</span>: res3[<span class="number">3</span>],</span><br><span class="line">    <span class="attr">movieUrl</span>: url,</span><br><span class="line">    movieType,</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">let</span> str = <span class="title class_">JSON</span>.<span class="title function_">stringify</span>(movie)</span><br><span class="line">  <span class="title function_">fsWrite</span>(<span class="string">&#x27;./movies/&#x27;</span> + movieType + <span class="string">&#x27;/&#x27;</span> + res3[<span class="number">1</span>] + <span class="string">&#x27;json&#x27;</span>, str)</span><br><span class="line">&#125;</span><br><span class="line"><span class="title function_">getClassUrl</span>(httpUrl)</span><br></pre></td></tr></table></figure>

<h2 id="爬取表情包"><a href="#爬取表情包" class="headerlink" title="爬取表情包"></a>爬取表情包</h2><figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">const</span> cheerio = <span class="built_in">require</span>(<span class="string">&#x27;cheerio&#x27;</span>)</span><br><span class="line"><span class="keyword">const</span> axios = <span class="built_in">require</span>(<span class="string">&#x27;axios&#x27;</span>)</span><br><span class="line"><span class="keyword">const</span> fs = <span class="built_in">require</span>(<span class="string">&#x27;fs&#x27;</span>)</span><br><span class="line"><span class="keyword">const</span> url = <span class="built_in">require</span>(<span class="string">&#x27;url&#x27;</span>)</span><br><span class="line"><span class="keyword">const</span> path = <span class="built_in">require</span>(<span class="string">&#x27;path&#x27;</span>)</span><br><span class="line"><span class="comment">// 将延迟函数封装成promise对象（防止请求速度过快下载失败）</span></span><br><span class="line"><span class="keyword">function</span> <span class="title function_">wait</span>(<span class="params">millSeconds</span>) &#123;</span><br><span class="line">  <span class="keyword">return</span> <span class="keyword">new</span> <span class="title class_">Promise</span>(<span class="keyword">function</span> (<span class="params">resolve, reject</span>) &#123;</span><br><span class="line">    <span class="built_in">setTimeout</span>(<span class="function">() =&gt;</span> &#123;</span><br><span class="line">      <span class="title function_">resolve</span>(<span class="string">&#x27;成功执行延迟函数，延迟时间：&#x27;</span> + millSeconds)</span><br><span class="line">    &#125;, millSeconds)</span><br><span class="line">  &#125;)</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 获取HTML文档的内容</span></span><br><span class="line"><span class="comment">// 获取页面总数</span></span><br><span class="line"><span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">pageNum</span>(<span class="params">link</span>) &#123;</span><br><span class="line">  <span class="keyword">let</span> res = <span class="keyword">await</span> axios.<span class="title function_">get</span>(link)</span><br><span class="line">  <span class="keyword">let</span> $ = cheerio.<span class="title function_">load</span>(res.<span class="property">data</span>)</span><br><span class="line">  <span class="keyword">let</span> btnLength = $(<span class="string">&#x27;.pagination li&#x27;</span>).<span class="property">length</span></span><br><span class="line">  <span class="keyword">let</span> allNum = $(<span class="string">&#x27;.pagination li&#x27;</span>)</span><br><span class="line">    .<span class="title function_">eq</span>(btnLength - <span class="number">2</span>)</span><br><span class="line">    .<span class="title function_">find</span>(<span class="string">&#x27;a&#x27;</span>)</span><br><span class="line">    .<span class="title function_">text</span>()</span><br><span class="line">  <span class="keyword">return</span> allNum</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 获取页面</span></span><br><span class="line"><span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">getListPage</span>(<span class="params">pageNum</span>) &#123;</span><br><span class="line">  <span class="keyword">let</span> httpUrl = <span class="string">`https://www.doutula.com/article/list/?page=<span class="subst">$&#123;pageNum&#125;</span>`</span></span><br><span class="line">  <span class="keyword">let</span> res = <span class="keyword">await</span> axios.<span class="title function_">get</span>(httpUrl)</span><br><span class="line">  <span class="keyword">let</span> $ = cheerio.<span class="title function_">load</span>(res.<span class="property">data</span>)</span><br><span class="line">  $(<span class="string">&#x27;#home .col-sm-9&gt;a&#x27;</span>).<span class="title function_">each</span>(<span class="function">(<span class="params">i, element</span>) =&gt;</span> &#123;</span><br><span class="line">    <span class="keyword">let</span> pageUrl = $(element).<span class="title function_">attr</span>(<span class="string">&#x27;href&#x27;</span>)</span><br><span class="line">    <span class="keyword">let</span> title = $(element).<span class="title function_">find</span>(<span class="string">&#x27;.random_title&#x27;</span>).<span class="title function_">text</span>()</span><br><span class="line">    <span class="keyword">let</span> reg = <span class="regexp">/(.*?)\d/gi</span>s</span><br><span class="line">    title = reg.<span class="title function_">exec</span>(title)[<span class="number">1</span>]</span><br><span class="line">    fs.<span class="title function_">mkdir</span>(<span class="string">&#x27;./img/&#x27;</span> + title, <span class="keyword">function</span> (<span class="params">err</span>) &#123;</span><br><span class="line">      <span class="keyword">if</span> (err) &#123;</span><br><span class="line">        <span class="variable language_">console</span>.<span class="title function_">log</span>(err)</span><br><span class="line">      &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">        <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">&#x27;创建：&#x27;</span> + <span class="string">&#x27;./img/&#x27;</span> + title)</span><br><span class="line">      &#125;</span><br><span class="line">    &#125;)</span><br><span class="line">    <span class="title function_">parsePage</span>(pageUrl, title)</span><br><span class="line">  &#125;)</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 进入表情包页面</span></span><br><span class="line"><span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">parsePage</span>(<span class="params">link, title</span>) &#123;</span><br><span class="line">  <span class="keyword">let</span> res = <span class="keyword">await</span> axios.<span class="title function_">get</span>(link)</span><br><span class="line">  <span class="keyword">let</span> $ = cheerio.<span class="title function_">load</span>(res.<span class="property">data</span>)</span><br><span class="line">  $(<span class="string">&#x27;.pic-content img&#x27;</span>).<span class="title function_">each</span>(<span class="function">(<span class="params">i, element</span>) =&gt;</span> &#123;</span><br><span class="line">    <span class="keyword">let</span> imgUrl = $(element).<span class="title function_">attr</span>(<span class="string">&#x27;src&#x27;</span>)</span><br><span class="line">    <span class="keyword">let</span> b = url.<span class="title function_">parse</span>(imgUrl)</span><br><span class="line">    <span class="keyword">let</span> name = path.<span class="title function_">parse</span>(b.<span class="property">pathname</span>)</span><br><span class="line">    <span class="comment">// 创建路径名字</span></span><br><span class="line">    <span class="keyword">let</span> filePath = <span class="string">`./img/<span class="subst">$&#123;title&#125;</span>/<span class="subst">$&#123;name.base&#125;</span>/`</span></span><br><span class="line">    <span class="comment">// 创建写入流</span></span><br><span class="line">    <span class="keyword">let</span> ws = fs.<span class="title function_">createWriteStream</span>(filePath.<span class="title function_">trim</span>())</span><br><span class="line">    axios.<span class="title function_">get</span>(imgUrl, &#123; <span class="attr">responseType</span>: <span class="string">&#x27;stream&#x27;</span> &#125;).<span class="title function_">then</span>(<span class="keyword">function</span> (<span class="params">res</span>) &#123;</span><br><span class="line">      res.<span class="property">data</span>.<span class="title function_">pipe</span>(ws)</span><br><span class="line">      <span class="variable language_">console</span>.<span class="title function_">log</span>(<span class="string">&#x27;正在下载表情：&#x27;</span> + filePath)</span><br><span class="line">      <span class="comment">// 监听事件，关闭写入流</span></span><br><span class="line">      res.<span class="property">data</span>.<span class="title function_">on</span>(<span class="string">&#x27;close&#x27;</span>, <span class="function">() =&gt;</span> ws.<span class="title function_">close</span>())</span><br><span class="line">    &#125;)</span><br><span class="line">  &#125;)</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 开始爬取所有页面</span></span><br><span class="line"><span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">spider</span>(<span class="params">link</span>) &#123;</span><br><span class="line">  <span class="keyword">let</span> allPageNum = <span class="keyword">await</span> <span class="title function_">pageNum</span>(link)</span><br><span class="line">  <span class="keyword">for</span> (<span class="keyword">let</span> i = <span class="number">1</span>; i &lt;= allPageNum; i++) &#123;</span><br><span class="line">    <span class="keyword">await</span> <span class="title function_">wait</span>(<span class="number">4000</span> * i) <span class="comment">// 每个页面延迟3秒</span></span><br><span class="line">    <span class="title function_">getListPage</span>(i)</span><br><span class="line">  &#125;</span><br><span class="line">&#125;</span><br><span class="line"><span class="title function_">spider</span>(<span class="string">&#x27;https://www.doutula.com/article/list/?page=1&#x27;</span>)</span><br></pre></td></tr></table></figure>

<h2 id="爬取音乐"><a href="#爬取音乐" class="headerlink" title="爬取音乐"></a>爬取音乐</h2><figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br></pre></td><td class="code"><pre><span class="line"><span class="keyword">const</span> axios = <span class="built_in">require</span>(<span class="string">&#x27;axios&#x27;</span>)</span><br><span class="line"><span class="keyword">const</span> fs = <span class="built_in">require</span>(<span class="string">&#x27;fs&#x27;</span>)</span><br><span class="line"><span class="keyword">const</span> path = <span class="built_in">require</span>(<span class="string">&#x27;path&#x27;</span>)</span><br><span class="line"><span class="comment">// 目标：下载音乐</span></span><br><span class="line"><span class="comment">// 1.获取音乐相关的信息，通过信息获取下载地址</span></span><br><span class="line"><span class="comment">// 2.通过获取音乐列表获取音乐信息</span></span><br><span class="line"><span class="comment">// 3.通过音乐的分类页获取音乐列表</span></span><br><span class="line"><span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">getPage</span>(<span class="params">num</span>) &#123;</span><br><span class="line">  <span class="keyword">let</span> httpUrl = <span class="string">&#x27;http://www.app-echo.com/api/recommend/sound-day?page=&#x27;</span> + num</span><br><span class="line">  <span class="keyword">let</span> res = <span class="keyword">await</span> axios.<span class="title function_">get</span>(httpUrl)</span><br><span class="line">  <span class="comment">// console.log(res.data.list);</span></span><br><span class="line">  <span class="keyword">let</span> list = res.<span class="property">data</span>.<span class="property">list</span></span><br><span class="line">  list.<span class="title function_">forEach</span>(<span class="keyword">function</span> (<span class="params">item, i</span>) &#123;</span><br><span class="line">    <span class="keyword">let</span> title = item.<span class="property">sound</span>.<span class="property">name</span></span><br><span class="line">    <span class="keyword">let</span> musicUrl = item.<span class="property">sound</span>.<span class="property">source</span></span><br><span class="line">    <span class="keyword">let</span> fileName = path.<span class="title function_">parse</span>(musicUrl).<span class="property">name</span></span><br><span class="line">    <span class="keyword">let</span> content = <span class="string">`<span class="subst">$&#123;title&#125;</span>,<span class="subst">$&#123;musicUrl&#125;</span>.<span class="subst">$&#123;fileName&#125;</span>\n`</span></span><br><span class="line">    fs.<span class="title function_">writeFile</span>(<span class="string">&#x27;music.txt&#x27;</span>, content, &#123; <span class="attr">flag</span>: <span class="string">&#x27;a&#x27;</span> &#125;, <span class="keyword">function</span> (<span class="params"></span>) &#123;</span><br><span class="line">      <span class="comment">// console.log(&#x27;写入完成:&#x27;+ title);</span></span><br><span class="line">    &#125;)</span><br><span class="line">    <span class="comment">// console.log(path.parse(musicUrl));</span></span><br><span class="line">    <span class="title function_">download</span>(musicUrl, fileName)</span><br><span class="line">  &#125;)</span><br><span class="line">&#125;</span><br><span class="line"><span class="keyword">async</span> <span class="keyword">function</span> <span class="title function_">download</span>(<span class="params">link, fileName</span>) &#123;</span><br><span class="line">  <span class="keyword">let</span> res = <span class="keyword">await</span> axios.<span class="title function_">get</span>(link, &#123; <span class="attr">responseType</span>: <span class="string">&#x27;stream&#x27;</span> &#125;)</span><br><span class="line">  <span class="keyword">let</span> ws = fs.<span class="title function_">createWriteStream</span>(<span class="string">&#x27;./music/&#x27;</span> + fileName + <span class="string">&#x27;.mp3&#x27;</span>)</span><br><span class="line">  <span class="variable language_">console</span>.<span class="title function_">log</span>(res.<span class="property">data</span>)</span><br><span class="line">  res.<span class="property">data</span>.<span class="title function_">pipe</span>(ws)</span><br><span class="line">  res.<span class="property">data</span>.<span class="title function_">on</span>(<span class="string">&#x27;close&#x27;</span>, <span class="keyword">function</span> (<span class="params"></span>) &#123;</span><br><span class="line">    ws.<span class="title function_">close</span>()</span><br><span class="line">  &#125;)</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 爬一页意思一下就行了</span></span><br><span class="line"><span class="title function_">getPage</span>(<span class="number">1</span>)</span><br></pre></td></tr></table></figure>

<h2 id="心得总结"><a href="#心得总结" class="headerlink" title="心得总结"></a>心得总结</h2><p>编写爬虫主要是通过 <code>axios</code> 来进行发送请求，在这个过程中，我们要分析网页结构，和网站信息，来提取我们需要的信息，进行一个爬取。在这个过程中，大部分都是异步完成的，要记得加 <code>await</code> 。</p>
<p>在没有 <code>cheerio</code> 模块的时候，我们通过正则匹配来进行抓取，有了 <code>cheerio</code> 模块我们可以像使用 <code>jquery</code> 一样方便的来获取页面中的元素。</p>
<p>当然过程中也使用了 Node 的一些核心模块，包括 url 的解析，path 路径的解析，文件的读写，还有 <code>stream</code> 的操作等等，这次爬虫的小实战，也算是对前几天的学习的一个综合运用。</p>
<p>妈妈再也不用担心斗图斗不过别人啦！</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200423134324.png" alt="爬取表情包"></p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/b07ae32c.html">http://blog.mhy.loc.cc/archives/b07ae32c.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/Node/">Node</a><a class="post-meta__tags" href="/tags/Node%E7%88%AC%E8%99%AB/">Node爬虫</a><a class="post-meta__tags" href="/tags/%E5%AE%9E%E6%88%98%E4%BB%A3%E7%A0%81/">实战代码</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/f36d08b9.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200422204027.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Puppeteer学习笔记</div></div></a></div><div class="next-post pull-right"><a href="/archives/8737131e.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200422204027.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Puppeteer的入门教程和实践</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/7e878b19.html" title="爬虫总结笔记"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-01-02</div><div class="title">爬虫总结笔记</div></div></a></div><div><a href="/archives/1567847a.html" title="Node爬取数据到数据库练习"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-23</div><div class="title">Node爬取数据到数据库练习</div></div></a></div><div><a href="/archives/1212afb3.html" title="Node爬取数据到数据库实战代码笔记"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184753.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-20</div><div class="title">Node爬取数据到数据库实战代码笔记</div></div></a></div><div><a href="/archives/44b2b83c.html" title="Node学习总结笔记"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184621.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-12-13</div><div class="title">Node学习总结笔记</div></div></a></div><div><a href="/archives/a26302a1.html" title="Node原生fs模块的promise封装"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184621.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2019-11-20</div><div class="title">Node原生fs模块的promise封装</div></div></a></div><div><a href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2023-06-15</div><div class="title">mac OS 配置前端开发环境</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%88%AC%E5%8F%96%E7%BD%91%E9%A1%B5%E4%BF%A1%E6%81%AF"><span class="toc-text">爬取网页信息</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%88%AC%E5%8F%96%E8%A1%A8%E6%83%85%E5%8C%85"><span class="toc-text">爬取表情包</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%88%AC%E5%8F%96%E9%9F%B3%E4%B9%90"><span class="toc-text">爬取音乐</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%BF%83%E5%BE%97%E6%80%BB%E7%BB%93"><span class="toc-text">心得总结</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>