<link rel="stylesheet" href="<%- theme.jsDelivr.url %><%- url_for(theme.libs.css.gitalk) %>">
<link rel="stylesheet" href="<%- theme.jsDelivr.url %><%- url_for('/css/my-gitalk.css') %>">

<div class="card gitalk-card" data-aos="fade-up">
    <div class="comment_headling" style="font-size: 20px; font-weight: 700; position: relative; padding-left: 20px; top: 15px; padding-bottom: 5px;">
        <i class="fas fa-comments fa-fw" aria-hidden="true"></i>
        <span>评论</span>
    </div>
    <div id="gitalk-container" class="card-content"></div>
</div>

<script src="<%- theme.jsDelivr.url %><%- url_for(theme.libs.js.gitalk) %>"></script>
<script>
    let gitalk = new Gitalk({
        clientID: '<%- theme.gitalk.oauth.clientId %>',
        clientSecret: '<%- theme.gitalk.oauth.clientSecret %>',
        repo: '<%- theme.gitalk.repo %>',
        owner: '<%- theme.gitalk.owner %>',
        admin: <%- JSON.stringify(theme.gitalk.admin) %>,
        id: '<%- date(page.date, 'YYYY-MM-DDTHH-mm-ss') %>',
        distractionFreeMode: false  // Facebook-like distraction free mode
    });

    gitalk.render('gitalk-container');
</script>
