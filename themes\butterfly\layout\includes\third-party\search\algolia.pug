#algolia-search
  .search-dialog
    nav.search-nav
      span.search-dialog-title= _p('search.title')
      button.search-close-button
        i.fas.fa-times

    .search-wrap
      #algolia-search-input
      hr
      #algolia-search-results
        #algolia-hits
        #algolia-pagination
        #algolia-info
          .algolia-stats
          .algolia-poweredBy

  #search-mask

  script(src=url_for(theme.asset.algolia_search))
  script(src=url_for(theme.asset.instantsearch))
  script(src=url_for(theme.asset.algolia_js))