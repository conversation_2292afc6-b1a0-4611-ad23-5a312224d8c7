<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Hexo框架(二)：配置文件详解与常用命令 | 你真是一个美好的人类</title><meta name="keywords" content="博客搭建,<PERSON>x<PERSON>,Hexo配置,Hexo命令"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="（1）Hexo站点配置文件详解；（2）Hexo常用命令介绍；">
<meta property="og:type" content="article">
<meta property="og:title" content="Hexo框架(二)：配置文件详解与常用命令">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/a2423b27.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="（1）Hexo站点配置文件详解；（2）Hexo常用命令介绍；">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png">
<meta property="article:published_time" content="2020-02-06T10:23:33.000Z">
<meta property="article:modified_time" content="2020-02-06T10:23:33.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="博客搭建">
<meta property="article:tag" content="Hexo">
<meta property="article:tag" content="Hexo配置">
<meta property="article:tag" content="Hexo命令">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/a2423b27"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Hexo框架(二)：配置文件详解与常用命令',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-02-06 10:23:33'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Hexo框架(二)：配置文件详解与常用命令</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-02-06T10:23:33.000Z" title="发表于 2020-02-06 10:23:33">2020-02-06</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-02-06T10:23:33.000Z" title="更新于 2020-02-06 10:23:33">2020-02-06</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><div class="note danger flat"><h2 id="前言："><a href="#前言：" class="headerlink" title="前言："></a>前言：</h2><p>本篇设置基于 NEXT 主题 7.7.2 版本！！</p>
</div>

<p>之前我们快速搭建了 Hexo 博客，并成功上传到了 GitHub 仓库，现在我们开始配置博客，对博客进行自定义。本篇主要是配置文件的详解，以及熟悉常用的一些 hexo 命令，可以帮我们更有效率的开发和管理博客。</p>
<h2 id="配置文件详解"><a href="#配置文件详解" class="headerlink" title="配置文件详解"></a>配置文件详解</h2><p>这里所说的配置文件，是位于站点根目录下的 <code>_config.yml</code> 文件，可以直接用记事本打开进行编辑,当然我们还是选择使用 VScode 进行编辑。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200321222422.png" alt="配置文件"></p>
<h3 id="Site"><a href="#Site" class="headerlink" title="Site"></a>Site</h3><p>网站的个性化描述，大家需要根据自己的实际情况认真填写</p>
<table>
<thead>
<tr>
<th align="center">Setting</th>
<th align="center">描述</th>
</tr>
</thead>
<tbody><tr>
<td align="center">title</td>
<td align="center">网站标题</td>
</tr>
<tr>
<td align="center">subtitle</td>
<td align="center">网站副标题</td>
</tr>
<tr>
<td align="center">description</td>
<td align="center">网站描述</td>
</tr>
<tr>
<td align="center">keywords</td>
<td align="center">网站关键字</td>
</tr>
<tr>
<td align="center">author</td>
<td align="center">网站作者</td>
</tr>
<tr>
<td align="center">language</td>
<td align="center">网站使用的语言，默认是<code>en</code> ，中文网站填<code>zh-CN</code></td>
</tr>
<tr>
<td align="center">Hans`zone</td>
<td align="center">网站使用的时区，默认为 <code>计算机的预设置</code>，可以不填</td>
</tr>
</tbody></table>
<h3 id="URL"><a href="#URL" class="headerlink" title="URL"></a>URL</h3><p>关于博客文章 URL 的设置，一般不用进行更改</p>
<table>
<thead>
<tr>
<th align="center">Setting</th>
<th align="center">描述</th>
</tr>
</thead>
<tbody><tr>
<td align="center">url</td>
<td align="center">网站的网址</td>
</tr>
<tr>
<td align="center">root</td>
<td align="center">网站的根目录， 也是存放文章的目录</td>
</tr>
<tr>
<td align="center">permalink</td>
<td align="center">文章的链接格式 ，默认为 <code>:year/:month/:day/:title/</code></td>
</tr>
<tr>
<td align="center">permalink_defaults</td>
<td align="center">永久链接中每个段的默认值</td>
</tr>
</tbody></table>
<h3 id="Directory"><a href="#Directory" class="headerlink" title="Directory"></a>Directory</h3><p>关于文件夹的设置，也是一般不用进行更改</p>
<table>
<thead>
<tr>
<th align="center">Setting</th>
<th align="center">描述</th>
</tr>
</thead>
<tbody><tr>
<td align="center">source_dir</td>
<td align="center">资源文件夹 ，存放用户的资源文件，默认为 <code>source</code></td>
</tr>
<tr>
<td align="center">public_dir</td>
<td align="center">公用文件夹 ，存放生成的静态文件，默认为 <code>public</code></td>
</tr>
<tr>
<td align="center">tag_dir</td>
<td align="center">标签目录 ，默认为 <code>tags</code></td>
</tr>
<tr>
<td align="center">archive_dir</td>
<td align="center">档案目录 ，默认为 <code>archives</code></td>
</tr>
<tr>
<td align="center">category_dir</td>
<td align="center">分类目录 ，默认为 <code>categories</code></td>
</tr>
<tr>
<td align="center">code_dir</td>
<td align="center">代码目录 ，默认为 <code>downloads/code</code></td>
</tr>
<tr>
<td align="center">i18n_dir</td>
<td align="center">i18n 目录 ，默认为 <code>:lang</code></td>
</tr>
<tr>
<td align="center">skip_render</td>
<td align="center">储存站长验证文件，跳过指定文件的渲染</td>
</tr>
</tbody></table>
<h3 id="Writing"><a href="#Writing" class="headerlink" title="Writing"></a>Writing</h3><p>比较常用的写作设置，可以根据自己的写作习惯随时进行调整</p>
<table>
<thead>
<tr>
<th align="center">Setting</th>
<th align="center">描述</th>
</tr>
</thead>
<tbody><tr>
<td align="center">new_post_name</td>
<td align="center">文章的文件名格式，默认为 <code>:title.md</code></td>
</tr>
<tr>
<td align="center">default_layout</td>
<td align="center">预设的布局模板，默认为 <code>post</code></td>
</tr>
<tr>
<td align="center">titlecase</td>
<td align="center">标题是否使用首字母大写 ，默认为 <code>false</code></td>
</tr>
<tr>
<td align="center">external_link</td>
<td align="center">链接是否在新标签页中打开，默认为 <code>true</code></td>
</tr>
<tr>
<td align="center">filename_case</td>
<td align="center">将文件名转换为 <code>1</code> 小写 或 <code>2</code> 大写，默认为 <code>0</code></td>
</tr>
<tr>
<td align="center">render_drafts</td>
<td align="center">是否显示渲染草稿，默认为 <code>false</code></td>
</tr>
<tr>
<td align="center">post_asset_folder</td>
<td align="center">是否启用 Asset 文件夹，默认为 <code>false</code></td>
</tr>
<tr>
<td align="center">relative_link</td>
<td align="center">是否建立相对于根文件夹的链接，默认为 <code>false</code></td>
</tr>
<tr>
<td align="center">future</td>
<td align="center">是否显示未来文章，默认为 <code>true</code></td>
</tr>
<tr>
<td align="center">highlight</td>
<td align="center">代码块设置</td>
</tr>
</tbody></table>
<ul>
<li><p><strong>highlight</strong></p>
<table>
<thead>
<tr>
<th align="center">Setting</th>
<th align="center">描述</th>
</tr>
</thead>
<tbody><tr>
<td align="center">enable</td>
<td align="center">是否使用代码高亮 ，默认为 <code>true</code></td>
</tr>
<tr>
<td align="center">line_number</td>
<td align="center">是否显示行号 ，默认为 <code>true</code></td>
</tr>
<tr>
<td align="center">auto_detect</td>
<td align="center">是否自动检测语言 ，默认为 <code>false</code></td>
</tr>
<tr>
<td align="center">tab_replace</td>
<td align="center">tab 替代设置</td>
</tr>
</tbody></table>
</li>
</ul>
<h3 id="Home-page-setting"><a href="#Home-page-setting" class="headerlink" title="Home page setting"></a>Home page setting</h3><p>首页设置，可以自己决定每页显示的文章数量和显示文章的顺序</p>
<table>
<thead>
<tr>
<th align="center"><strong>Setting</strong></th>
<th align="center"><strong>描述</strong></th>
</tr>
</thead>
<tbody><tr>
<td align="center">index_generator</td>
<td align="center">主页设置</td>
</tr>
</tbody></table>
<ul>
<li><p><strong>index_generator</strong></p>
<table>
<thead>
<tr>
<th align="center"><strong>Setting</strong></th>
<th align="center"><strong>描述</strong></th>
</tr>
</thead>
<tbody><tr>
<td align="center">path</td>
<td align="center">首页的根目录</td>
</tr>
<tr>
<td align="center">per_page</td>
<td align="center">每页显示文章的数量，默认为 <code>10</code></td>
</tr>
<tr>
<td align="center">order_by</td>
<td align="center">显示文章的顺序，默认为 <code>-date</code></td>
</tr>
</tbody></table>
</li>
</ul>
<h3 id="Category-amp-Tag"><a href="#Category-amp-Tag" class="headerlink" title="Category &amp; Tag"></a>Category &amp; Tag</h3><p>这里是关于分类和标签的配置</p>
<table>
<thead>
<tr>
<th align="center"><strong>Setting</strong></th>
<th align="center"><strong>描述</strong></th>
</tr>
</thead>
<tbody><tr>
<td align="center">default_category</td>
<td align="center">预设分类，默认为 <code>uncategorized</code></td>
</tr>
<tr>
<td align="center">category_map</td>
<td align="center">分类别名</td>
</tr>
<tr>
<td align="center">tag_map</td>
<td align="center">标签别名</td>
</tr>
</tbody></table>
<h3 id="Date-Time-format"><a href="#Date-Time-format" class="headerlink" title="Date / Time format"></a>Date / Time format</h3><p>时间和日期的显示格式</p>
<table>
<thead>
<tr>
<th align="center"><strong>Setting</strong></th>
<th align="center"><strong>描述</strong></th>
</tr>
</thead>
<tbody><tr>
<td align="center">date_format</td>
<td align="center">日期格式，默认为 <code>YYYY-MM-DD</code></td>
</tr>
<tr>
<td align="center">time_format</td>
<td align="center">时间格式，默认为 <code>HH:mm:ss</code></td>
</tr>
</tbody></table>
<h3 id="Pagination"><a href="#Pagination" class="headerlink" title="Pagination"></a>Pagination</h3><p>分页设置，可以自己决定单个页面上显示的文章数量和分页目录</p>
<table>
<thead>
<tr>
<th align="center"><strong>Setting</strong></th>
<th align="center"><strong>描述</strong></th>
</tr>
</thead>
<tbody><tr>
<td align="center">per_page</td>
<td align="center">单个页面上显示的文章数量，默认为 <code>10</code> ，用 <code>0</code> 表示禁用分页</td>
</tr>
<tr>
<td align="center">pagination_dir</td>
<td align="center">分页目录，默认为 <code>page</code></td>
</tr>
</tbody></table>
<h3 id="Extensions"><a href="#Extensions" class="headerlink" title="Extensions"></a>Extensions</h3><p>这里可以设置主题类型和插件，之后讲到更换博客主题时需要进行更改</p>
<table>
<thead>
<tr>
<th align="center"><strong>Setting</strong></th>
<th align="center"><strong>描述</strong></th>
</tr>
</thead>
<tbody><tr>
<td align="center">theme</td>
<td align="center">theme</td>
</tr>
</tbody></table>
<h3 id="Deployment"><a href="#Deployment" class="headerlink" title="Deployment"></a>Deployment</h3><p>这里是关于网站部署的配置，常用的有部署类型和部署地址</p>
<table>
<thead>
<tr>
<th align="center"><strong>Setting</strong></th>
<th align="center"><strong>描述</strong></th>
</tr>
</thead>
<tbody><tr>
<td align="center">deploy</td>
<td align="center">网站部署配置</td>
</tr>
</tbody></table>
<ul>
<li><p><strong>deploy</strong></p>
<table>
<thead>
<tr>
<th align="center"><strong>Setting</strong></th>
<th align="center"><strong>描述</strong></th>
</tr>
</thead>
<tbody><tr>
<td align="center">type</td>
<td align="center">网站部署类型</td>
</tr>
<tr>
<td align="center">repo</td>
<td align="center">网站部署地址</td>
</tr>
</tbody></table>
</li>
</ul>
<h2 id="常用命令"><a href="#常用命令" class="headerlink" title="常用命令"></a>常用命令</h2><h3 id="hexo-init"><a href="#hexo-init" class="headerlink" title="hexo init"></a>hexo init</h3><p><code>hexo init</code> 命令用于 **初始化 ** 本地文件夹为博客的根目录。</p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo init [<span class="type">folder</span>]</span><br></pre></td></tr></table></figure>

<ul>
<li><code>folder</code> 可选参数，用以指定初始化目录的路径，若无指定则默认为当前目录</li>
</ul>
<h3 id="hexo-new"><a href="#hexo-new" class="headerlink" title="hexo new"></a>hexo new</h3><p><code>hexo new</code> 命令用于 <strong>新建文章</strong> ，一般可以简写为 <code>hexo n</code></p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo new [<span class="type">layout</span>] &lt;title&gt;</span><br></pre></td></tr></table></figure>

<ul>
<li><code>layout</code> 可选参数，用以指定文章类型，若无指定则默认由配置文件中的 default_layout 选项决定</li>
<li><code>title</code> 必填参数，用以指定文章标题，如果参数值中含有空格，则需要使用双引号包围</li>
</ul>
<h3 id="hexo-generate"><a href="#hexo-generate" class="headerlink" title="hexo generate"></a>hexo generate</h3><p><code>hexo generate</code> 命令用于 <strong>生成静态文件</strong>，一般可以简写为 <code>hexo g</code></p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo generate</span><br></pre></td></tr></table></figure>

<ul>
<li><code>-d</code> 选项，指定生成后部署，与 <code>hexo d -g</code> 等价</li>
</ul>
<h3 id="hexo-server"><a href="#hexo-server" class="headerlink" title="hexo server"></a>hexo server</h3><p><code>hexo server</code> 命令用于 <strong>启动本地服务器</strong>，一般可以简写为 <code>hexo s</code></p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo server</span><br></pre></td></tr></table></figure>

<ul>
<li><code>-p</code> 选项，指定服务器端口，默认为 4000</li>
<li><code>-i</code> 选项，指定服务器 IP 地址，默认为 0.0.0.0</li>
<li><code>-s</code> 选项，静态模式 ，仅提供 public 文件夹中的文件并禁用文件监视</li>
</ul>
<p><strong><em>说明：</em></strong> 运行本地服务器需要安装 hexo-server 插件</p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install hexo<span class="literal">-server</span> <span class="literal">--save</span></span><br></pre></td></tr></table></figure>

<h3 id="hexo-deploy"><a href="#hexo-deploy" class="headerlink" title="hexo deploy"></a>hexo deploy</h3><p><code>hexo deploy</code> 命令用于 **部署 ** 网站，一般可以简写为 <code>hexo d</code></p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo deploy</span><br></pre></td></tr></table></figure>

<ul>
<li><code>-g</code> 选项，指定生成后部署，与 <code>hexo g -d</code> 等价</li>
</ul>
<p><strong>说明</strong> ：部署前需要修改 _config.yml 配置文件，下面以 部署到 GitHub 为例进行说明</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">deploy:</span></span><br><span class="line">  <span class="attr">type:</span> <span class="string">git</span></span><br><span class="line">  <span class="attr">repo:</span> <span class="string">https://github.com/YourgithubName/YourgithubName.github.io.git</span></span><br><span class="line">  <span class="attr">branch:</span> <span class="string">master</span></span><br><span class="line">  <span class="attr">message:</span> <span class="string">自定义提交消息，默认为Site</span> <span class="attr">updated:</span> &#123;&#123; <span class="string">now(&#x27;YYYY-MM-DD</span> <span class="string">HH:mm:ss&#x27;)</span> &#125;&#125;</span><br></pre></td></tr></table></figure>

<h3 id="hexo-clean"><a href="#hexo-clean" class="headerlink" title="hexo clean"></a>hexo clean</h3><p><code>hexo clean</code> 命令用于 **清理缓存文件 **。网站显示异常的时候可以尝试此操作</p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo clean</span><br></pre></td></tr></table></figure>

<h3 id="Option"><a href="#Option" class="headerlink" title="Option"></a>Option</h3><h4 id="hexo-–safe"><a href="#hexo-–safe" class="headerlink" title="hexo –safe"></a>hexo –safe</h4><p><code>hexo --safe</code> 表示安全模式，用于禁用加载插件和脚本，安装新插件时遇到问题可以尝试此操作。</p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo <span class="literal">--safe</span></span><br></pre></td></tr></table></figure>

<h4 id="hexo-–debug"><a href="#hexo-–debug" class="headerlink" title="hexo –debug"></a>hexo –debug</h4><p><code>hexo --debug</code> 表示调试模式，用于将消息详细记录到终端和 <code>debug.log</code> 文件</p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo <span class="literal">--debug</span></span><br></pre></td></tr></table></figure>

<h4 id="hexo-–silent"><a href="#hexo-–silent" class="headerlink" title="hexo –silent"></a>hexo –silent</h4><p><code>hexo --silent</code> 表示静默模式，用于静默输出到终端</p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">hexo <span class="literal">--silent</span></span><br></pre></td></tr></table></figure>

<h2 id="结语"><a href="#结语" class="headerlink" title="结语"></a>结语</h2><p>主要配置文件信息和常用命令就是这样了，</p>
<p>更多配置文件请参考官方文档：</p>
<p>配置：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://hexo.io/docs/configuration">https://hexo.io/docs/configuration</a></p>
<p>命令：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://hexo.io/docs">https://hexo.io/docs</a></p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/a2423b27.html">http://blog.mhy.loc.cc/archives/a2423b27.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a><a class="post-meta__tags" href="/tags/Hexo/">Hexo</a><a class="post-meta__tags" href="/tags/Hexo%E9%85%8D%E7%BD%AE/">Hexo配置</a><a class="post-meta__tags" href="/tags/Hexo%E5%91%BD%E4%BB%A4/">Hexo命令</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/264a3045.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Hexo框架(三)：Next主题配置及美化</div></div></a></div><div class="next-post pull-right"><a href="/archives/477f8de2.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718185138.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Express框架：session</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2022-02-10</div><div class="title">使用GitHub Actions 实现自动化部署和部署到服务器</div></div></a></div><div><a href="/archives/353666f0.html" title="给你的博客添加一个收藏页"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200719133158.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-19</div><div class="title">给你的博客添加一个收藏页</div></div></a></div><div><a href="/archives/f36eea83.html" title="Hexo博客添加emoji表情支持"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">Hexo博客添加emoji表情支持</div></div></a></div><div><a href="/archives/2798a2b1.html" title="给文章标题添加一个emoji表情"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">给文章标题添加一个emoji表情</div></div></a></div><div><a href="/archives/f06684a1.html" title="NexT版本更新V8.0记录"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-19</div><div class="title">NexT版本更新V8.0记录</div></div></a></div><div><a href="/archives/b1e33f9d.html" title="sitemeta渐变背景实现"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-03</div><div class="title">sitemeta渐变背景实现</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%89%8D%E8%A8%80%EF%BC%9A"><span class="toc-text">前言：</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6%E8%AF%A6%E8%A7%A3"><span class="toc-text">配置文件详解</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#Site"><span class="toc-text">Site</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#URL"><span class="toc-text">URL</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Directory"><span class="toc-text">Directory</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Writing"><span class="toc-text">Writing</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Home-page-setting"><span class="toc-text">Home page setting</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Category-amp-Tag"><span class="toc-text">Category &amp; Tag</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Date-Time-format"><span class="toc-text">Date &#x2F; Time format</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Pagination"><span class="toc-text">Pagination</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Extensions"><span class="toc-text">Extensions</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Deployment"><span class="toc-text">Deployment</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%B8%B8%E7%94%A8%E5%91%BD%E4%BB%A4"><span class="toc-text">常用命令</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#hexo-init"><span class="toc-text">hexo init</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#hexo-new"><span class="toc-text">hexo new</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#hexo-generate"><span class="toc-text">hexo generate</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#hexo-server"><span class="toc-text">hexo server</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#hexo-deploy"><span class="toc-text">hexo deploy</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#hexo-clean"><span class="toc-text">hexo clean</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Option"><span class="toc-text">Option</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#hexo-%E2%80%93safe"><span class="toc-text">hexo –safe</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#hexo-%E2%80%93debug"><span class="toc-text">hexo –debug</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#hexo-%E2%80%93silent"><span class="toc-text">hexo –silent</span></a></li></ol></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%BB%93%E8%AF%AD"><span class="toc-text">结语</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>