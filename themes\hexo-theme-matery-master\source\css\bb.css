div.timenode {
    position: relative;
}

div.timenode:before,
div.timenode:after {
    content: '';
    z-index: 1;
    position: absolute;
    background: rgba(68, 215, 182, 0.5);
    width: 2px;
    left: 7px;
}

div.timenode:before {
    top: 0px;
    height: 6px;
}

div.timenode:after {
    top: 26px;
    height: calc(100% - 26px);
}

div.timenode:last-child:after {
    height: calc(100% - 26px - 16px);
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
}

div.timenode .meta,
div.timenode .body {
    max-width: calc(100% - 24px);
}

div.timenode .meta {
    position: relative;
    color: var(--color-meta);
    font-size: 0.875rem;
    line-height: 32px;
    height: 32px;
}

div.timenode .meta:before,
div.timenode .meta:after {
    content: '';
    position: absolute;
    top: 8px;
    z-index: 2;
}

div.timenode .meta:before {
    background: rgba(68, 215, 182, 0.5);
    width: 16px;
    height: 16px;
    border-radius: 8px;
}

div.timenode .meta:after {
    background: #44d7b6;
    margin-left: 2px;
    margin-top: 2px;
    width: 12px;
    height: 12px;
    border-radius: 6px;
    transform: scale(0.5);
    transition: all 0.28s ease;
    -moz-transition: all 0.28s ease;
    -webkit-transition: all 0.28s ease;
    -o-transition: all 0.28s ease;
}

div.timenode .meta p {
    font-weight: bold;
    margin: 0 0 0 24px;
}

div.timenode .body {
    margin: 4px 0 16px 24px;
    padding: 16px;
    border-radius: 8px;
    background: var(--color-block);
    display: inline-block;
}

div.timenode .body:empty {
    display: none;
}

div.timenode .body > *:first-child {
    margin-top: 0.25em;
}

div.timenode .body > *:last-child {
    margin-bottom: 0.25em;
}

div.timenode .body .highlight {
    border: 1px solid #e4e4e4;
}

div.timenode:hover .meta {
    color: var(--color-text);
}

div.timenode:hover .meta:before {
    background: rgba(255, 87, 34, 0.5);
}

div.timenode:hover .meta:after {
    background: #ff5722;
    transform: scale(1);
}

div.timenode .body {
    margin: 0 0 0 24px;
    padding: 16px;
    border-radius: 8px;
    background: #f6f6f6;
    display: inline-block;
}

div.timenode time {
    margin-left: 20px;
}
