<!-- 文章内容详情 -->
<div id="artDetail">
    <div class="card">
        <div class="card-content article-info">
            <div class="row tag-cate">
                <div class="col s7">
                    <% if (page.tags && page.tags.length) { %>
                    <div class="article-tag">
                        <% page.tags.forEach(function(tag) { %>
                            <a href="<%- url_for(tag.path) %>">
                                <span class="chip bg-color"><%= tag.name %></span>
                            </a>
                        <% }); %>
                    </div>
                    <% } else { %>
                          <div class="article-tag">
                            <span class="chip bg-color"><%- __("notag")  %></span>
                          </div>
                    <% } %>
                </div>
                <div class="col s5 right-align">
                    <% if (page.categories && page.categories.length > 0) { %>
                    <div class="post-cate">
                        <i class="fas fa-bookmark fa-fw icon-category"></i>
                        <% page.categories.forEach(category => { %>
                            <a href="<%- url_for(category.path) %>" class="post-category">
                                <%- category.name %>
                            </a>
                        <% }); %>
                    </div>
                    <% } %>
                </div>
            </div>

            <div class="post-info">
                <% if (theme.postInfo.date) { %>
                <div class="post-date info-break-policy">
                    <i class="far fa-calendar-minus fa-fw"></i><%- __('publishDate') %>:&nbsp;&nbsp;
                    <%- date(page.date, 'YYYY-MM-DD') %>
                </div>
                <% } %>

                <% if (theme.postInfo.update) { %>
                <div class="post-date info-break-policy">
                    <i class="far fa-calendar-check fa-fw"></i><%- __('updateDate') %>:&nbsp;&nbsp;
                    <%- date(page.updated, 'YYYY-MM-DD') %>
                </div>
                <% } %>

                <% if (theme.postInfo.wordCount) { %>
                <div class="info-break-policy">
                    <i class="far fa-file-word fa-fw"></i><%- __('wordCount') %>:&nbsp;&nbsp;
                    <%= wordcount(page.content) %>
                </div>
                <% } %>

                <% if (theme.postInfo.min2read) { %>
                <div class="info-break-policy">
                    <i class="far fa-clock fa-fw"></i><%- __('readTimes') %>:&nbsp;&nbsp;
                    <%= min2read(page.content) %> <%= __('Minutes') %>
                </div>
                <% } %>

                <% if (theme.postInfo.readCount && theme.busuanziStatistics && theme.busuanziStatistics.enable) { %>
                    <div id="busuanzi_container_page_pv" class="info-break-policy">
                        <i class="far fa-eye fa-fw"></i><%- __('readCount') %>:&nbsp;&nbsp;
                        <span id="busuanzi_value_page_pv"></span>
                    </div>
				<% } %>
            </div>
        </div>
        <hr class="clearfix">

        <% if (config.prismjs && config.prismjs.enable) { %>
        <!-- 是否加载使用自带的 prismjs. -->
        <link rel="stylesheet" href="<%- theme.jsDelivr.url %><%- url_for(theme.libs.css.prism) %>">
        <% } %>

        <% if (theme.code.break) { %>
        <!-- 代码块折行 -->
        <style type="text/css">
            code[class*="language-"], pre[class*="language-"] { white-space: pre-wrap !important; }
        </style>
        <% } %>

        <div class="card-content article-card-content">
            <div id="articleContent">
                <%- page.content %>
                <% if (theme.bbtime.enable && page.layout == 'bb') { %>
                    <link rel="stylesheet" href="<%- theme.jsDelivr.url %><%- url_for('/css/bb.css') %>">
                    <style>
                        @font-face {
                            font-family: "iconfont";
                            src: url('//at.alicdn.com/t/font_1755564_z4mhxbw13mq.eot?t=1586882866168');
                            /* IE9 */
                            src: url('//at.alicdn.com/t/font_1755564_z4mhxbw13mq.eot?t=1586882866168#iefix') format('embedded-opentype'),
                                /* IE6-IE8 */
                            url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALcAAsAAAAAB2gAAAKPAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCCfgqCUII8ATYCJAMMCwgABCAFhG0HQBuEBsiuMRkynL4YFCHtLKs4b+k7/80U8DwHIB6+xtr7u3vniDZI6tmnkyDS8NC5iFgJlE7pTPcO8e5b+803aYQy8YtH1JPKyZzY3ttb1EonioVGSBAiRJp4JV9cFppCxLpe2z8sUs37O4QEDFRM0lFeAw4XN6bQPG06zTG8DRwHFOBeGEVWIjE+mP8wduVFPCbQNK4ktN8/OgFJhbEqEFemLkEyFVcUVqoL1ZKDRbyIqafb9AA8+9+PX41IklQyY+PBSZ8KnR+9iBx0OxZcp8PxvGDHyFgACnFY6j8QCfMLIppKPmoCrGtW8OG/y0eufLX/8AiJqDKa22AGWpMzUth6p6aARySQQRUUL4AnSBmuyvIwPdbz21OX5ybO5duOJifaLl6cvkrd1HR7kWEy6yikff29PvgO17V/Tf/9f/oN3PDqz22a719IYlZJHQetpLY08MV6fwFDrF0B6F4XyB3/FyEEnZnU2XGtVwHvpxtA1d6pftKB4AcoWo4U1QQbiiy35Xow27whbwK2NNFlhAc99zeOtM4S6ikFQ1IzhqxuglAoC6ho2UBV3Raa5g0cbxnCRURpYM4pQOh3h6TXG7J+j4RCeULFqFdU9UcETYfRdGHLVJjpHk6SIBXlTTQV5mmWS6SD1jHSVx2JJ1U+a4p4YKawvrSSLQ2QR3yMLcGa3hBCQ40zF/vBc+Q4DH3OLFJExRDCb64un5S9qaIwF9L2cEQiEBWSbUImBePR3PFCOvf5MUS3yiHhNR0VySmEC5jTo7qKWgvCgN5r1XEtrwTW6BoEQYM0HONC/cBLHDPPIL98noUohAqjR7uvWVV1HlVbfWV+o/t8J6DJOJ4jRY6idmVKnmVS1q8tK8zfBAAA') format('woff2'),
                            url('//at.alicdn.com/t/font_1755564_z4mhxbw13mq.woff?t=1586882866168') format('woff'),
                            url('//at.alicdn.com/t/font_1755564_z4mhxbw13mq.ttf?t=1586882866168') format('truetype'),
                                /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
                            url('//at.alicdn.com/t/font_1755564_z4mhxbw13mq.svg?t=1586882866168#iconfont') format('svg');
                            /* iOS 4.1- */
                        }

                        .iconfont {
                            font-family: "iconfont" !important;
                            font-size: 16px;
                            font-style: normal;
                            -webkit-font-smoothing: antialiased;
                            -moz-osx-font-smoothing: grayscale;
                        }

                        .icon-lianjie:before {
                            content: "\e6a3";
                        }

                        .icon-lianjie-copy:before {
                            content: "\e6a4";
                        }
                    </style>
                    <main id="app">
                        <p class="tip">共计发送 {{count}} 条说说</p>
                        <div class="timenode" v-for="item in contents" v-cloak>
                            <div class="meta">
                                <p><time v-bind:datetime="item.attributes.time">{{item.attributes.time}}</time></p>
                            </div>
                            <div class="body">
                                <p v-html='item.attributes.content'></p>
                            </div>
                        </div>
                    </main>
                <% } %>
            </div>
            <hr/>

            <%- partial('_partial/reprint-statement') %>

            <div class="tag_share" style="display: block;">
                <div class="post-meta__tag-list" style="display: inline-block;">
                    <% if (page.tags && page.tags.length) { %>
                        <div class="article-tag">
                            <% page.tags.forEach(function(tag) { %>
                                <a href="<%- url_for(tag.path) %>">
                                    <span class="chip bg-color"><%= tag.name %></span>
                                </a>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <div class="article-tag">
                            <span class="chip bg-color"><%- __("notag")  %></span>
                        </div>
                    <% } %>
                </div>
                <div class="post_share" style="zoom: 80%; width: fit-content; display: inline-block; float: right; margin: -0.15rem 0;">
                    <%- partial('_partial/share') %>
                </div>
            </div>
            <% if (theme.reward && theme.reward.enable) { %>
                <%- partial('_partial/reward') %>
            <% } %>
        </div>
    </div>

    <% if (theme.gitalk && theme.gitalk.enable) { %>
        <%- partial('_partial/gitalk') %>
    <% } %>

    <% if (theme.gitment.enable) { %>
        <%- partial('_partial/gitment') %>
    <% } %>

    <% if (theme.disqus.enable) { %>
        <%- partial('_partial/disqus') %>
    <% } %>

    <% if (theme.livere && theme.livere.enable) { %>
    <%- partial('_partial/livere') %>
    <% } %>

    <% if (theme.valine && theme.valine.enable) { %>
        <%- partial('_partial/valine') %>
    <% } %>

    <% if (theme.minivaline && theme.minivaline.enable) { %>
        <%- partial('_partial/minivaline') %>
    <% } %>

    <% if (theme.changyan && theme.changyan.enable) { %>
        <%- partial('_partial/changyan') %>
    <% } %>

    <%- partial('_partial/prev-next') %>
</div>

<% if (theme.copyright.enable) { %>
<script>
    $('#articleContent').on('copy', function (e) {
        // IE8 or earlier browser is 'undefined'
        if (typeof window.getSelection === 'undefined') return;

        var selection = window.getSelection();
        // if the selection is short let's not annoy our users.
        if (('' + selection).length < Number.parseInt('<%- theme.copyright.minCharNumber %>')) {
            return;
        }

        // create a div outside of the visible area and fill it with the selected text.
        var bodyElement = document.getElementsByTagName('body')[0];
        var newdiv = document.createElement('div');
        newdiv.style.position = 'absolute';
        newdiv.style.left = '-99999px';
        bodyElement.appendChild(newdiv);
        newdiv.appendChild(selection.getRangeAt(0).cloneContents());

        // we need a <pre> tag workaround.
        // otherwise the text inside "pre" loses all the line breaks!
        if (selection.getRangeAt(0).commonAncestorContainer.nodeName === 'PRE') {
            newdiv.innerHTML = "<pre>" + newdiv.innerHTML + "</pre>";
        }

        var url = document.location.href;
        newdiv.innerHTML += '<br />'
            + '<%- __("from")  %>: <%= config.title %><br />'
            + '<%- __("author")  %>: <%= config.author %><br />'
            + '<%- __("link")  %>: <a href="' + url + '">' + url + '</a><br />'
            + '<%- theme.copyright.description %>';

        selection.selectAllChildren(newdiv);
        window.setTimeout(function () {bodyElement.removeChild(newdiv);}, 200);
    });
</script>
<% } %>

<!-- 代码块功能依赖 -->
<script type="text/javascript" src="<%- theme.jsDelivr.url %><%- url_for('/libs/codeBlock/codeBlockFuction.js') %>"></script>

<!-- 代码语言 -->
<% if (theme.code.lang) { %>
<script type="text/javascript" src="<%- theme.jsDelivr.url %><%- url_for('/libs/codeBlock/codeLang.js') %>"></script>
<% } %>

<!-- 代码块复制 -->
<% if (theme.code.copy) { %>
<script type="text/javascript" src="<%- theme.jsDelivr.url %><%- url_for('/libs/codeBlock/codeCopy.js') %>"></script>
<% } %>

<!-- 代码块收缩 -->
<% if (theme.code.shrink) { %>
<script type="text/javascript" src="<%- theme.jsDelivr.url %><%- url_for('/libs/codeBlock/codeShrink.js') %>"></script>
<% } %>
