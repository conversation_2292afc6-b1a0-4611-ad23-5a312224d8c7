<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Hexo博客添加emoji表情支持 | 你真是一个美好的人类</title><meta name="keywords" content="博客搭建,Hexo"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="更换博客的渲染引擎，添加emoji表情支持">
<meta property="og:type" content="article">
<meta property="og:title" content="Hexo博客添加emoji表情支持">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/f36eea83.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="更换博客的渲染引擎，添加emoji表情支持">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png">
<meta property="article:published_time" content="2020-05-20T16:00:20.000Z">
<meta property="article:modified_time" content="2020-05-20T16:00:20.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="博客搭建">
<meta property="article:tag" content="Hexo">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/f36eea83"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Hexo博客添加emoji表情支持',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-05-20 16:00:20'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Hexo博客添加emoji表情支持</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-05-20T16:00:20.000Z" title="发表于 2020-05-20 16:00:20">2020-05-20</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-05-20T16:00:20.000Z" title="更新于 2020-05-20 16:00:20">2020-05-20</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><p>Hexo 默认的 markdown 渲染引擎不支持将 Github emoji 渲染到静态的 html 页面中，我们换一个支持 emoji 的引擎，再增加一个 emoji 插件即可.</p>
<h2 id="方法一"><a href="#方法一" class="headerlink" title="方法一"></a>方法一</h2><h3 id="安装"><a href="#安装" class="headerlink" title="安装"></a>安装</h3><p>命令行如下：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line">npm uninstall hexo-renderer-marked</span><br><span class="line">npm install hexo-renderer-markdown-it --save</span><br><span class="line">npm install markdown-it-emoji --save</span><br></pre></td></tr></table></figure>

<p>Tips：据说 <a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/hexojs/hexo-renderer-markdown-it">hexo-renderer-markdown-it</a> 的速度要比 Hexo 原装插件要快，而且功能更多</p>
<h3 id="配置"><a href="#配置" class="headerlink" title="配置"></a>配置</h3><p>完成插件安装后还需要修改 Hexo 站点配置文件 <code>_config.yml</code>（不是主题配置哦）</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line">## markdown 渲染引擎配置，默认是hexo-renderer-marked，这个插件渲染速度更快，且有新特性</span><br><span class="line">markdown:</span><br><span class="line">  render:</span><br><span class="line">    html: true</span><br><span class="line">    xhtmlOut: false</span><br><span class="line">    breaks: true</span><br><span class="line">    linkify: true</span><br><span class="line">    typographer: true</span><br><span class="line">    quotes: &#x27;“”‘’&#x27;</span><br><span class="line">  plugins:</span><br><span class="line">    - markdown-it-footnote</span><br><span class="line">    - markdown-it-sup</span><br><span class="line">    - markdown-it-sub</span><br><span class="line">    - markdown-it-abbr</span><br><span class="line">    - markdown-it-emoji</span><br><span class="line">  anchors:</span><br><span class="line">    level: 2</span><br><span class="line">    collisionSuffix: &#x27;v&#x27;</span><br><span class="line">    permalink: true  ## 永久连接</span><br><span class="line">    permalinkClass: &#x27;header-anchor&#x27;</span><br><span class="line">    permalinkSymbol: &#x27;¶&#x27;</span><br></pre></td></tr></table></figure>

<p>这里需要注意 <code>render</code> 下的 <code>html</code> 配置项，它的作用是控制 <code>Markdown</code> 渲染引擎是否转义文档中出现的 <code>html</code> 标签，默认为 <code>false</code> ，这里要设置为 <code>true</code>，否则会导致 `` 渲染失败。</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line">html: true # 不转义 HTML 内容，即允许 HTML</span><br><span class="line">    ## OR</span><br><span class="line">html: false # 转义 HTML，&lt; &gt; 尖括号会被转义成 &amp;lt; &amp;gt;等</span><br></pre></td></tr></table></figure>

<p><code>plugins:</code> 中的最后一项 <code>- markdown-it-emoji</code> 是手动添加的，官方 Github Wiki 中给出的配置不包含这一项，其他配置参照的 Github Wiki 中的默认配置，hexo-renderer-markdown-it 提供的其他新特性还没有一一尝试，暂时只想用它的 emoji 功能。✌️</p>
<div class="note danger flat"><p>上面的方法有个问题就是会导致有些东西解析失败比如：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200520202340.png" alt="image-20200520202332978"></p>
<p>所以我们可以采用另外一个插件：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/next-theme/hexo-filter-emoji">hexo-filter-emoji</a></p>
</div>

<h2 id="方法二"><a href="#方法二" class="headerlink" title="方法二"></a>方法二</h2><h3 id="安装-1"><a href="#安装-1" class="headerlink" title="安装"></a>安装</h3><p>采用另外一个插件：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/next-theme/hexo-filter-emoji">hexo-filter-emoji</a> 。我们只需要安装这一个插件就好了，渲染的话，还是采用默认的渲染引擎。</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install hexo-filter-emoji</span><br></pre></td></tr></table></figure>

<h3 id="配置-1"><a href="#配置-1" class="headerlink" title="配置"></a>配置</h3><p>在站点配置文件 <code>.config.yml</code> 中增加：</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">emoji:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">className:</span> <span class="string">github-emoji</span></span><br><span class="line">  <span class="attr">styles:</span></span><br><span class="line">  <span class="attr">customEmojis:</span></span><br></pre></td></tr></table></figure>

<h2 id="使用方法"><a href="#使用方法" class="headerlink" title="使用方法"></a>使用方法</h2><p>输入对应的 emoji 编码就行了，这个是两种方法都通用的<br>例如：输入笑脸对应的 emoji 编码 <code>:smile:</code>就可以得到 😄</p>
<h3 id="方法二额外的用法："><a href="#方法二额外的用法：" class="headerlink" title="方法二额外的用法："></a>方法二额外的用法：</h3><p>如果你不喜欢 <code>::</code> 这种方法，你还可以这样采用<code>tags</code>的方式：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">&#123;% emoji sparkles %&#125;</span><br></pre></td></tr></table></figure>

<p>为了避免有时候 <code>::</code> 带来的解析问题，我们可以在 <code>front-matter</code> 中添加 <code>no-emoji: true</code> 来禁用 <code>::</code> 的方式：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">---</span><br><span class="line">title: Hello World</span><br><span class="line">no-emoji: true</span><br><span class="line">---</span><br><span class="line">:tada: as it is.</span><br><span class="line">&#123;% emoji tada %&#125; still works.</span><br></pre></td></tr></table></figure>

<p>这样的话 <code>::</code>的方式就不会被解析出来，但是第二种方法（tags）仍然可以工作</p>
<h2 id="emoji-编码合集"><a href="#emoji-编码合集" class="headerlink" title="emoji 编码合集"></a>emoji 编码合集</h2><p>你也可以点击这里进行查看： <a target="_blank" rel="noopener external nofollow noreferrer" href="https://www.webfx.com/tools/emoji-cheat-sheet/">EMOJI CHEAT SHEET</a></p>
<h3 id="People"><a href="#People" class="headerlink" title="People"></a>People</h3><table>
<thead>
<tr>
<th align="left">😐 <code>:neutral_face:</code></th>
<th align="left">😄 <code>:smile:</code></th>
<th align="left">😆 <code>:laughing:</code></th>
</tr>
</thead>
<tbody><tr>
<td align="left">😊 <code>:blush:</code></td>
<td align="left">😃 <code>:smiley:</code></td>
<td align="left">☺️ <code>:relaxed:</code></td>
</tr>
<tr>
<td align="left">😏 <code>:smirk:</code></td>
<td align="left">😍 <code>:heart_eyes:</code></td>
<td align="left">😘 <code>:kissing_heart:</code></td>
</tr>
<tr>
<td align="left">😚 <code>:kissing_closed_eyes:</code></td>
<td align="left">😳 <code>:flushed:</code></td>
<td align="left">😌 <code>:relieved:</code></td>
</tr>
<tr>
<td align="left">😆 <code>:satisfied:</code></td>
<td align="left">😁 <code>:grin:</code></td>
<td align="left">😉 <code>:wink:</code></td>
</tr>
<tr>
<td align="left">😜 <code>:stuck_out_tongue_winking_eye:</code></td>
<td align="left">😝 <code>:stuck_out_tongue_closed_eyes:</code></td>
<td align="left">😀 <code>:grinning:</code></td>
</tr>
<tr>
<td align="left">😗 <code>:kissing:</code></td>
<td align="left">😙 <code>:kissing_smiling_eyes:</code></td>
<td align="left">😛 <code>:stuck_out_tongue:</code></td>
</tr>
<tr>
<td align="left">😴 <code>:sleeping:</code></td>
<td align="left">😟 <code>:worried:</code></td>
<td align="left">😦 <code>:frowning:</code></td>
</tr>
<tr>
<td align="left">😧 <code>:anguished:</code></td>
<td align="left">😮 <code>:open_mouth:</code></td>
<td align="left">😬 <code>:grimacing:</code></td>
</tr>
<tr>
<td align="left">😕 <code>:confused:</code></td>
<td align="left">😯 <code>:hushed:</code></td>
<td align="left">😑 <code>:expressionless:</code></td>
</tr>
<tr>
<td align="left">😒 <code>:unamused:</code></td>
<td align="left">😅 <code>:sweat_smile:</code></td>
<td align="left">😓 <code>:sweat:</code></td>
</tr>
<tr>
<td align="left">😥 <code>:disappointed_relieved:</code></td>
<td align="left">😩 <code>:weary:</code></td>
<td align="left">😔 <code>:pensive:</code></td>
</tr>
<tr>
<td align="left">😞 <code>:disappointed:</code></td>
<td align="left">😖 <code>:confounded:</code></td>
<td align="left">😨 <code>:fearful:</code></td>
</tr>
<tr>
<td align="left">😰 <code>:cold_sweat:</code></td>
<td align="left">😣 <code>:persevere:</code></td>
<td align="left">😢 <code>:cry:</code></td>
</tr>
<tr>
<td align="left">😭 <code>:sob:</code></td>
<td align="left">😂 <code>:joy:</code></td>
<td align="left">😲 <code>:astonished:</code></td>
</tr>
<tr>
<td align="left">😱 <code>:scream:</code></td>
<td align="left">💭 <code>:thought_balloon:</code></td>
<td align="left">😫 <code>:tired_face:</code></td>
</tr>
<tr>
<td align="left">😠 <code>:angry:</code></td>
<td align="left">😡 <code>:rage:</code></td>
<td align="left">😤 <code>:triumph:</code></td>
</tr>
<tr>
<td align="left">😪 <code>:sleepy:</code></td>
<td align="left">😋 <code>:yum:</code></td>
<td align="left">😷 <code>:mask:</code></td>
</tr>
<tr>
<td align="left">😎 <code>:sunglasses:</code></td>
<td align="left">😵 <code>:dizzy_face:</code></td>
<td align="left">👿 <code>:imp:</code></td>
</tr>
<tr>
<td align="left">😈 <code>:smiling_imp:</code></td>
<td align="left">💬 <code>:speech_balloon:</code></td>
<td align="left">😶 <code>:no_mouth:</code></td>
</tr>
<tr>
<td align="left">😇 <code>:innocent:</code></td>
<td align="left">👽 <code>:alien:</code></td>
<td align="left">💛 <code>:yellow_heart:</code></td>
</tr>
<tr>
<td align="left">💙 <code>:blue_heart:</code></td>
<td align="left">💜 <code>:purple_heart:</code></td>
<td align="left">❤️ <code>:heart:</code></td>
</tr>
<tr>
<td align="left">💚 <code>:green_heart:</code></td>
<td align="left">💔 <code>:broken_heart:</code></td>
<td align="left">💓 <code>:heartbeat:</code></td>
</tr>
<tr>
<td align="left">💗 <code>:heartpulse:</code></td>
<td align="left">💕 <code>:two_hearts:</code></td>
<td align="left">💞 <code>:revolving_hearts:</code></td>
</tr>
<tr>
<td align="left">💘 <code>:cupid:</code></td>
<td align="left">💖 <code>:sparkling_heart:</code></td>
<td align="left">✨ <code>:sparkles:</code></td>
</tr>
<tr>
<td align="left">⭐️ <code>:star:</code></td>
<td align="left">🌟 <code>:star2:</code></td>
<td align="left">💫 <code>:dizzy:</code></td>
</tr>
<tr>
<td align="left">💥 <code>:boom:</code></td>
<td align="left">💥 <code>:collision:</code></td>
<td align="left">💢 <code>:anger:</code></td>
</tr>
<tr>
<td align="left">❗️ <code>:exclamation:</code></td>
<td align="left">❓ <code>:question:</code></td>
<td align="left">❕ <code>:grey_exclamation:</code></td>
</tr>
<tr>
<td align="left">❔ <code>:grey_question:</code></td>
<td align="left">💤 <code>:zzz:</code></td>
<td align="left">💨 <code>:dash:</code></td>
</tr>
<tr>
<td align="left">💦 <code>:sweat_drops:</code></td>
<td align="left">🎶 <code>:notes:</code></td>
<td align="left">🎵 <code>:musical_note:</code></td>
</tr>
<tr>
<td align="left">🔥 <code>:fire:</code></td>
<td align="left">💩 <code>:hankey:</code></td>
<td align="left">💩 <code>:poop:</code></td>
</tr>
<tr>
<td align="left">💩 <code>:shit:</code></td>
<td align="left">👍 <code>:+1:</code></td>
<td align="left">👍 <code>:thumbsup:</code></td>
</tr>
<tr>
<td align="left">👎 <code>:-1:</code></td>
<td align="left">👎 <code>:thumbsdown:</code></td>
<td align="left">👌 <code>:ok_hand:</code></td>
</tr>
<tr>
<td align="left">👊 <code>:punch:</code></td>
<td align="left">👊 <code>:facepunch:</code></td>
<td align="left">✊ <code>:fist:</code></td>
</tr>
<tr>
<td align="left">✌️ <code>:v:</code></td>
<td align="left">👋 <code>:wave:</code></td>
<td align="left">✋ <code>:hand:</code></td>
</tr>
<tr>
<td align="left">✋ <code>:raised_hand:</code></td>
<td align="left">👐 <code>:open_hands:</code></td>
<td align="left">☝️ <code>:point_up:</code></td>
</tr>
<tr>
<td align="left">👇 <code>:point_down:</code></td>
<td align="left">👈 <code>:point_left:</code></td>
<td align="left">👉 <code>:point_right:</code></td>
</tr>
<tr>
<td align="left">🙌 <code>:raised_hands:</code></td>
<td align="left">🙏 <code>:pray:</code></td>
<td align="left">👆 <code>:point_up_2:</code></td>
</tr>
<tr>
<td align="left">👏 <code>:clap:</code></td>
<td align="left">💪 <code>:muscle:</code></td>
<td align="left">🤘 <code>:metal:</code></td>
</tr>
<tr>
<td align="left">🖕 <code>:fu:</code></td>
<td align="left">🚶 <code>:walking:</code></td>
<td align="left">🏃 <code>:runner:</code></td>
</tr>
<tr>
<td align="left">🏃 <code>:running:</code></td>
<td align="left">👫 <code>:couple:</code></td>
<td align="left">👪 <code>:family:</code></td>
</tr>
<tr>
<td align="left">👬 <code>:two_men_holding_hands:</code></td>
<td align="left">👭 <code>:two_women_holding_hands:</code></td>
<td align="left">💃 <code>:dancer:</code></td>
</tr>
<tr>
<td align="left">👯 <code>:dancers:</code></td>
<td align="left">🙆 <code>:ok_woman:</code></td>
<td align="left">🙅 <code>:no_good:</code></td>
</tr>
<tr>
<td align="left">💁 <code>:information_desk_person:</code></td>
<td align="left">🙋 <code>:raising_hand:</code></td>
<td align="left">👰 <code>:bride_with_veil:</code></td>
</tr>
<tr>
<td align="left">🙎 <code>:person_with_pouting_face:</code></td>
<td align="left">🙍 <code>:person_frowning:</code></td>
<td align="left">🙇 <code>:bow:</code></td>
</tr>
<tr>
<td align="left">:couplekiss: <code>:couplekiss:</code></td>
<td align="left">💑 <code>:couple_with_heart:</code></td>
<td align="left">💆 <code>:massage:</code></td>
</tr>
<tr>
<td align="left">💇 <code>:haircut:</code></td>
<td align="left">💅 <code>:nail_care:</code></td>
<td align="left">👦 <code>:boy:</code></td>
</tr>
<tr>
<td align="left">👧 <code>:girl:</code></td>
<td align="left">👩 <code>:woman:</code></td>
<td align="left">👨 <code>:man:</code></td>
</tr>
<tr>
<td align="left">👶 <code>:baby:</code></td>
<td align="left">👵 <code>:older_woman:</code></td>
<td align="left">👴 <code>:older_man:</code></td>
</tr>
<tr>
<td align="left">👱 <code>:person_with_blond_hair:</code></td>
<td align="left">👲 <code>:man_with_gua_pi_mao:</code></td>
<td align="left">👳 <code>:man_with_turban:</code></td>
</tr>
<tr>
<td align="left">👷 <code>:construction_worker:</code></td>
<td align="left">👮 <code>:cop:</code></td>
<td align="left">👼 <code>:angel:</code></td>
</tr>
<tr>
<td align="left">👸 <code>:princess:</code></td>
<td align="left">😺 <code>:smiley_cat:</code></td>
<td align="left">😸 <code>:smile_cat:</code></td>
</tr>
<tr>
<td align="left">😻 <code>:heart_eyes_cat:</code></td>
<td align="left">😽 <code>:kissing_cat:</code></td>
<td align="left">😼 <code>:smirk_cat:</code></td>
</tr>
<tr>
<td align="left">🙀 <code>:scream_cat:</code></td>
<td align="left">😿 <code>:crying_cat_face:</code></td>
<td align="left">😹 <code>:joy_cat:</code></td>
</tr>
<tr>
<td align="left">😾 <code>:pouting_cat:</code></td>
<td align="left">👹 <code>:japanese_ogre:</code></td>
<td align="left">👺 <code>:japanese_goblin:</code></td>
</tr>
<tr>
<td align="left">🙈 <code>:see_no_evil:</code></td>
<td align="left">🙉 <code>:hear_no_evil:</code></td>
<td align="left">🙊 <code>:speak_no_evil:</code></td>
</tr>
<tr>
<td align="left">💂 <code>:guardsman:</code></td>
<td align="left">💀 <code>:skull:</code></td>
<td align="left">🐾 <code>:feet:</code></td>
</tr>
<tr>
<td align="left">👄 <code>:lips:</code></td>
<td align="left">💋 <code>:kiss:</code></td>
<td align="left">💧 <code>:droplet:</code></td>
</tr>
<tr>
<td align="left">👂 <code>:ear:</code></td>
<td align="left">👀 <code>:eyes:</code></td>
<td align="left">👃 <code>:nose:</code></td>
</tr>
<tr>
<td align="left">👅 <code>:tongue:</code></td>
<td align="left">💌 <code>:love_letter:</code></td>
<td align="left">👤 <code>:bust_in_silhouette:</code></td>
</tr>
<tr>
<td align="left">👥 <code>:busts_in_silhouette:</code></td>
<td align="left"></td>
<td align="left"></td>
</tr>
</tbody></table>
<h3 id="Nature"><a href="#Nature" class="headerlink" title="Nature"></a>Nature</h3><table>
<thead>
<tr>
<th align="left">☀️ <code>:sunny:</code></th>
<th align="left">☔️ <code>:umbrella:</code></th>
<th align="left">☁️ <code>:cloud:</code></th>
</tr>
</thead>
<tbody><tr>
<td align="left">❄️ <code>:snowflake:</code></td>
<td align="left">⛄️ <code>:snowman:</code></td>
<td align="left">⚡️ <code>:zap:</code></td>
</tr>
<tr>
<td align="left">🌀 <code>:cyclone:</code></td>
<td align="left">🌁 <code>:foggy:</code></td>
<td align="left">🌊 <code>:ocean:</code></td>
</tr>
<tr>
<td align="left">🐱 <code>:cat:</code></td>
<td align="left">🐶 <code>:dog:</code></td>
<td align="left">🐭 <code>:mouse:</code></td>
</tr>
<tr>
<td align="left">🐹 <code>:hamster:</code></td>
<td align="left">🐰 <code>:rabbit:</code></td>
<td align="left">🐺 <code>:wolf:</code></td>
</tr>
<tr>
<td align="left">🐸 <code>:frog:</code></td>
<td align="left">🐯 <code>:tiger:</code></td>
<td align="left">🐨 <code>:koala:</code></td>
</tr>
<tr>
<td align="left">🐻 <code>:bear:</code></td>
<td align="left">🐷 <code>:pig:</code></td>
<td align="left">🐽 <code>:pig_nose:</code></td>
</tr>
<tr>
<td align="left">🐮 <code>:cow:</code></td>
<td align="left">🐗 <code>:boar:</code></td>
<td align="left">🐵 <code>:monkey_face:</code></td>
</tr>
<tr>
<td align="left">🐒 <code>:monkey:</code></td>
<td align="left">🐴 <code>:horse:</code></td>
<td align="left">🐎 <code>:racehorse:</code></td>
</tr>
<tr>
<td align="left">🐫 <code>:camel:</code></td>
<td align="left">🐑 <code>:sheep:</code></td>
<td align="left">🐘 <code>:elephant:</code></td>
</tr>
<tr>
<td align="left">🐼 <code>:panda_face:</code></td>
<td align="left">🐍 <code>:snake:</code></td>
<td align="left">🐦 <code>:bird:</code></td>
</tr>
<tr>
<td align="left">🐤 <code>:baby_chick:</code></td>
<td align="left">🐥 <code>:hatched_chick:</code></td>
<td align="left">🐣 <code>:hatching_chick:</code></td>
</tr>
<tr>
<td align="left">🐔 <code>:chicken:</code></td>
<td align="left">🐧 <code>:penguin:</code></td>
<td align="left">🐢 <code>:turtle:</code></td>
</tr>
<tr>
<td align="left">🐛 <code>:bug:</code></td>
<td align="left">🐝 <code>:honeybee:</code></td>
<td align="left">🐜 <code>:ant:</code></td>
</tr>
<tr>
<td align="left">🐞 <code>:beetle:</code></td>
<td align="left">🐌 <code>:snail:</code></td>
<td align="left">🐙 <code>:octopus:</code></td>
</tr>
<tr>
<td align="left">🐠 <code>:tropical_fish:</code></td>
<td align="left">🐟 <code>:fish:</code></td>
<td align="left">🐳 <code>:whale:</code></td>
</tr>
<tr>
<td align="left">🐋 <code>:whale2:</code></td>
<td align="left">🐬 <code>:dolphin:</code></td>
<td align="left">🐄 <code>:cow2:</code></td>
</tr>
<tr>
<td align="left">🐏 <code>:ram:</code></td>
<td align="left">🐀 <code>:rat:</code></td>
<td align="left">🐃 <code>:water_buffalo:</code></td>
</tr>
<tr>
<td align="left">🐅 <code>:tiger2:</code></td>
<td align="left">🐇 <code>:rabbit2:</code></td>
<td align="left">🐉 <code>:dragon:</code></td>
</tr>
<tr>
<td align="left">🐐 <code>:goat:</code></td>
<td align="left">🐓 <code>:rooster:</code></td>
<td align="left">🐕 <code>:dog2:</code></td>
</tr>
<tr>
<td align="left">🐖 <code>:pig2:</code></td>
<td align="left">🐁 <code>:mouse2:</code></td>
<td align="left">🐂 <code>:ox:</code></td>
</tr>
<tr>
<td align="left">🐲 <code>:dragon_face:</code></td>
<td align="left">🐡 <code>:blowfish:</code></td>
<td align="left">🐊 <code>:crocodile:</code></td>
</tr>
<tr>
<td align="left">🐪 <code>:dromedary_camel:</code></td>
<td align="left">🐆 <code>:leopard:</code></td>
<td align="left">🐈 <code>:cat2:</code></td>
</tr>
<tr>
<td align="left">🐩 <code>:poodle:</code></td>
<td align="left">🐾 <code>:paw_prints:</code></td>
<td align="left">💐 <code>:bouquet:</code></td>
</tr>
<tr>
<td align="left">🌸 <code>:cherry_blossom:</code></td>
<td align="left">🌷 <code>:tulip:</code></td>
<td align="left">🍀 <code>:four_leaf_clover:</code></td>
</tr>
<tr>
<td align="left">🌹 <code>:rose:</code></td>
<td align="left">🌻 <code>:sunflower:</code></td>
<td align="left">🌺 <code>:hibiscus:</code></td>
</tr>
<tr>
<td align="left">🍁 <code>:maple_leaf:</code></td>
<td align="left">🍃 <code>:leaves:</code></td>
<td align="left">🍂 <code>:fallen_leaf:</code></td>
</tr>
<tr>
<td align="left">🌿 <code>:herb:</code></td>
<td align="left">🍄 <code>:mushroom:</code></td>
<td align="left">🌵 <code>:cactus:</code></td>
</tr>
<tr>
<td align="left">🌴 <code>:palm_tree:</code></td>
<td align="left">🌲 <code>:evergreen_tree:</code></td>
<td align="left">🌳 <code>:deciduous_tree:</code></td>
</tr>
<tr>
<td align="left">🌰 <code>:chestnut:</code></td>
<td align="left">🌱 <code>:seedling:</code></td>
<td align="left">🌼 <code>:blossom:</code></td>
</tr>
<tr>
<td align="left">🌾 <code>:ear_of_rice:</code></td>
<td align="left">🐚 <code>:shell:</code></td>
<td align="left">🌐 <code>:globe_with_meridians:</code></td>
</tr>
<tr>
<td align="left">🌞 <code>:sun_with_face:</code></td>
<td align="left">🌝 <code>:full_moon_with_face:</code></td>
<td align="left">🌚 <code>:new_moon_with_face:</code></td>
</tr>
<tr>
<td align="left">🌑 <code>:new_moon:</code></td>
<td align="left">🌒 <code>:waxing_crescent_moon:</code></td>
<td align="left">🌓 <code>:first_quarter_moon:</code></td>
</tr>
<tr>
<td align="left">🌔 <code>:waxing_gibbous_moon:</code></td>
<td align="left">🌕 <code>:full_moon:</code></td>
<td align="left">🌖 <code>:waning_gibbous_moon:</code></td>
</tr>
<tr>
<td align="left">🌗 <code>:last_quarter_moon:</code></td>
<td align="left">🌘 <code>:waning_crescent_moon:</code></td>
<td align="left">🌜 <code>:last_quarter_moon_with_face:</code></td>
</tr>
<tr>
<td align="left">🌛 <code>:first_quarter_moon_with_face:</code></td>
<td align="left">🌔 <code>:moon:</code></td>
<td align="left">🌍 <code>:earth_africa:</code></td>
</tr>
<tr>
<td align="left">🌎 <code>:earth_americas:</code></td>
<td align="left">🌏 <code>:earth_asia:</code></td>
<td align="left">🌋 <code>:volcano:</code></td>
</tr>
<tr>
<td align="left">🌌 <code>:milky_way:</code></td>
<td align="left">⛅️ <code>:partly_sunny:</code></td>
<td align="left"></td>
</tr>
</tbody></table>
<h3 id="Objects"><a href="#Objects" class="headerlink" title="Objects"></a>Objects</h3><table>
<thead>
<tr>
<th align="left">🎍 <code>:bamboo:</code></th>
<th align="left">💝 <code>:gift_heart:</code></th>
<th align="left">🎎 <code>:dolls:</code></th>
</tr>
</thead>
<tbody><tr>
<td align="left">🎒 <code>:school_satchel:</code></td>
<td align="left">🎓 <code>:mortar_board:</code></td>
<td align="left">🎏 <code>:flags:</code></td>
</tr>
<tr>
<td align="left">🎆 <code>:fireworks:</code></td>
<td align="left">🎇 <code>:sparkler:</code></td>
<td align="left">🎐 <code>:wind_chime:</code></td>
</tr>
<tr>
<td align="left">🎑 <code>:rice_scene:</code></td>
<td align="left">🎃 <code>:jack_o_lantern:</code></td>
<td align="left">👻 <code>:ghost:</code></td>
</tr>
<tr>
<td align="left">🎅 <code>:santa:</code></td>
<td align="left">🎄 <code>:christmas_tree:</code></td>
<td align="left">🎁 <code>:gift:</code></td>
</tr>
<tr>
<td align="left">🔔 <code>:bell:</code></td>
<td align="left">🔕 <code>:no_bell:</code></td>
<td align="left">🎋 <code>:tanabata_tree:</code></td>
</tr>
<tr>
<td align="left">🎉 <code>:tada:</code></td>
<td align="left">🎊 <code>:confetti_ball:</code></td>
<td align="left">🎈 <code>:balloon:</code></td>
</tr>
<tr>
<td align="left">🔮 <code>:crystal_ball:</code></td>
<td align="left">💿 <code>:cd:</code></td>
<td align="left">📀 <code>:dvd:</code></td>
</tr>
<tr>
<td align="left">💾 <code>:floppy_disk:</code></td>
<td align="left">📷 <code>:camera:</code></td>
<td align="left">📹 <code>:video_camera:</code></td>
</tr>
<tr>
<td align="left">🎥 <code>:movie_camera:</code></td>
<td align="left">💻 <code>:computer:</code></td>
<td align="left">📺 <code>:tv:</code></td>
</tr>
<tr>
<td align="left">📱 <code>:iphone:</code></td>
<td align="left">☎️ <code>:phone:</code></td>
<td align="left">☎️ <code>:telephone:</code></td>
</tr>
<tr>
<td align="left">📞 <code>:telephone_receiver:</code></td>
<td align="left">📟 <code>:pager:</code></td>
<td align="left">📠 <code>:fax:</code></td>
</tr>
<tr>
<td align="left">💽 <code>:minidisc:</code></td>
<td align="left">📼 <code>:vhs:</code></td>
<td align="left">🔉 <code>:sound:</code></td>
</tr>
<tr>
<td align="left">🔈 <code>:speaker:</code></td>
<td align="left">🔇 <code>:mute:</code></td>
<td align="left">📢 <code>:loudspeaker:</code></td>
</tr>
<tr>
<td align="left">📣 <code>:mega:</code></td>
<td align="left">⌛️ <code>:hourglass:</code></td>
<td align="left">⏳ <code>:hourglass_flowing_sand:</code></td>
</tr>
<tr>
<td align="left">⏰ <code>:alarm_clock:</code></td>
<td align="left">⌚️ <code>:watch:</code></td>
<td align="left">📻 <code>:radio:</code></td>
</tr>
<tr>
<td align="left">📡 <code>:satellite:</code></td>
<td align="left">➿ <code>:loop:</code></td>
<td align="left">🔍 <code>:mag:</code></td>
</tr>
<tr>
<td align="left">🔎 <code>:mag_right:</code></td>
<td align="left">🔓 <code>:unlock:</code></td>
<td align="left">🔒 <code>:lock:</code></td>
</tr>
<tr>
<td align="left">🔏 <code>:lock_with_ink_pen:</code></td>
<td align="left">🔐 <code>:closed_lock_with_key:</code></td>
<td align="left">🔑 <code>:key:</code></td>
</tr>
<tr>
<td align="left">💡 <code>:bulb:</code></td>
<td align="left">🔦 <code>:flashlight:</code></td>
<td align="left">🔆 <code>:high_brightness:</code></td>
</tr>
<tr>
<td align="left">🔅 <code>:low_brightness:</code></td>
<td align="left">🔌 <code>:electric_plug:</code></td>
<td align="left">🔋 <code>:battery:</code></td>
</tr>
<tr>
<td align="left">📲 <code>:calling:</code></td>
<td align="left">✉️ <code>:email:</code></td>
<td align="left">📫 <code>:mailbox:</code></td>
</tr>
<tr>
<td align="left">📮 <code>:postbox:</code></td>
<td align="left">🛀 <code>:bath:</code></td>
<td align="left">🛁 <code>:bathtub:</code></td>
</tr>
<tr>
<td align="left">🚿 <code>:shower:</code></td>
<td align="left">🚽 <code>:toilet:</code></td>
<td align="left">🔧 <code>:wrench:</code></td>
</tr>
<tr>
<td align="left">🔩 <code>:nut_and_bolt:</code></td>
<td align="left">🔨 <code>:hammer:</code></td>
<td align="left">💺 <code>:seat:</code></td>
</tr>
<tr>
<td align="left">💰 <code>:moneybag:</code></td>
<td align="left">💴 <code>:yen:</code></td>
<td align="left">💵 <code>:dollar:</code></td>
</tr>
<tr>
<td align="left">💷 <code>:pound:</code></td>
<td align="left">💶 <code>:euro:</code></td>
<td align="left">💳 <code>:credit_card:</code></td>
</tr>
<tr>
<td align="left">💸 <code>:money_with_wings:</code></td>
<td align="left">📧 <code>:e-mail:</code></td>
<td align="left">📥 <code>:inbox_tray:</code></td>
</tr>
<tr>
<td align="left">📤 <code>:outbox_tray:</code></td>
<td align="left">✉️ <code>:envelope:</code></td>
<td align="left">📨 <code>:incoming_envelope:</code></td>
</tr>
<tr>
<td align="left">📯 <code>:postal_horn:</code></td>
<td align="left">📪 <code>:mailbox_closed:</code></td>
<td align="left">📬 <code>:mailbox_with_mail:</code></td>
</tr>
<tr>
<td align="left">📭 <code>:mailbox_with_no_mail:</code></td>
<td align="left">🚪 <code>:door:</code></td>
<td align="left">🚬 <code>:smoking:</code></td>
</tr>
<tr>
<td align="left">💣 <code>:bomb:</code></td>
<td align="left">🔫 <code>:gun:</code></td>
<td align="left">🔪 <code>:hocho:</code></td>
</tr>
<tr>
<td align="left">💊 <code>:pill:</code></td>
<td align="left">💉 <code>:syringe:</code></td>
<td align="left">📄 <code>:page_facing_up:</code></td>
</tr>
<tr>
<td align="left">📃 <code>:page_with_curl:</code></td>
<td align="left">📑 <code>:bookmark_tabs:</code></td>
<td align="left">📊 <code>:bar_chart:</code></td>
</tr>
<tr>
<td align="left">📈 <code>:chart_with_upwards_trend:</code></td>
<td align="left">📉 <code>:chart_with_downwards_trend:</code></td>
<td align="left">📜 <code>:scroll:</code></td>
</tr>
<tr>
<td align="left">📋 <code>:clipboard:</code></td>
<td align="left">📆 <code>:calendar:</code></td>
<td align="left">📅 <code>:date:</code></td>
</tr>
<tr>
<td align="left">📇 <code>:card_index:</code></td>
<td align="left">📁 <code>:file_folder:</code></td>
<td align="left">📂 <code>:open_file_folder:</code></td>
</tr>
<tr>
<td align="left">✂️ <code>:scissors:</code></td>
<td align="left">📌 <code>:pushpin:</code></td>
<td align="left">📎 <code>:paperclip:</code></td>
</tr>
<tr>
<td align="left">✒️ <code>:black_nib:</code></td>
<td align="left">✏️ <code>:pencil2:</code></td>
<td align="left">📏 <code>:straight_ruler:</code></td>
</tr>
<tr>
<td align="left">📐 <code>:triangular_ruler:</code></td>
<td align="left">📕 <code>:closed_book:</code></td>
<td align="left">📗 <code>:green_book:</code></td>
</tr>
<tr>
<td align="left">📘 <code>:blue_book:</code></td>
<td align="left">📙 <code>:orange_book:</code></td>
<td align="left">📓 <code>:notebook:</code></td>
</tr>
<tr>
<td align="left">📔 <code>:notebook_with_decorative_cover:</code></td>
<td align="left">📒 <code>:ledger:</code></td>
<td align="left">📚 <code>:books:</code></td>
</tr>
<tr>
<td align="left">🔖 <code>:bookmark:</code></td>
<td align="left">📛 <code>:name_badge:</code></td>
<td align="left">🔬 <code>:microscope:</code></td>
</tr>
<tr>
<td align="left">🔭 <code>:telescope:</code></td>
<td align="left">📰 <code>:newspaper:</code></td>
<td align="left">🏈 <code>:football:</code></td>
</tr>
<tr>
<td align="left">🏀 <code>:basketball:</code></td>
<td align="left">⚽️ <code>:soccer:</code></td>
<td align="left">⚾️ <code>:baseball:</code></td>
</tr>
<tr>
<td align="left">🎾 <code>:tennis:</code></td>
<td align="left">🎱 <code>:8ball:</code></td>
<td align="left">🏉 <code>:rugby_football:</code></td>
</tr>
<tr>
<td align="left">🎳 <code>:bowling:</code></td>
<td align="left">⛳️ <code>:golf:</code></td>
<td align="left">🚵 <code>:mountain_bicyclist:</code></td>
</tr>
<tr>
<td align="left">🚴 <code>:bicyclist:</code></td>
<td align="left">🏇 <code>:horse_racing:</code></td>
<td align="left">🏂 <code>:snowboarder:</code></td>
</tr>
<tr>
<td align="left">🏊 <code>:swimmer:</code></td>
<td align="left">🏄 <code>:surfer:</code></td>
<td align="left">🎿 <code>:ski:</code></td>
</tr>
<tr>
<td align="left">♠️ <code>:spades:</code></td>
<td align="left">♥️ <code>:hearts:</code></td>
<td align="left">♣️ <code>:clubs:</code></td>
</tr>
<tr>
<td align="left">♦️ <code>:diamonds:</code></td>
<td align="left">💎 <code>:gem:</code></td>
<td align="left">💍 <code>:ring:</code></td>
</tr>
<tr>
<td align="left">🏆 <code>:trophy:</code></td>
<td align="left">🎼 <code>:musical_score:</code></td>
<td align="left">🎹 <code>:musical_keyboard:</code></td>
</tr>
<tr>
<td align="left">🎻 <code>:violin:</code></td>
<td align="left">👾 <code>:space_invader:</code></td>
<td align="left">🎮 <code>:video_game:</code></td>
</tr>
<tr>
<td align="left">🃏 <code>:black_joker:</code></td>
<td align="left">🎴 <code>:flower_playing_cards:</code></td>
<td align="left">🎲 <code>:game_die:</code></td>
</tr>
<tr>
<td align="left">🎯 <code>:dart:</code></td>
<td align="left">🀄️ <code>:mahjong:</code></td>
<td align="left">🎬 <code>:clapper:</code></td>
</tr>
<tr>
<td align="left">📝 <code>:memo:</code></td>
<td align="left">📝 <code>:pencil:</code></td>
<td align="left">📖 <code>:book:</code></td>
</tr>
<tr>
<td align="left">🎨 <code>:art:</code></td>
<td align="left">🎤 <code>:microphone:</code></td>
<td align="left">🎧 <code>:headphones:</code></td>
</tr>
<tr>
<td align="left">🎺 <code>:trumpet:</code></td>
<td align="left">🎷 <code>:saxophone:</code></td>
<td align="left">🎸 <code>:guitar:</code></td>
</tr>
<tr>
<td align="left">👞 <code>:shoe:</code></td>
<td align="left">👡 <code>:sandal:</code></td>
<td align="left">👠 <code>:high_heel:</code></td>
</tr>
<tr>
<td align="left">💄 <code>:lipstick:</code></td>
<td align="left">👢 <code>:boot:</code></td>
<td align="left">👕 <code>:shirt:</code></td>
</tr>
<tr>
<td align="left">👕 <code>:tshirt:</code></td>
<td align="left">👔 <code>:necktie:</code></td>
<td align="left">👚 <code>:womans_clothes:</code></td>
</tr>
<tr>
<td align="left">👗 <code>:dress:</code></td>
<td align="left">🎽 <code>:running_shirt_with_sash:</code></td>
<td align="left">👖 <code>:jeans:</code></td>
</tr>
<tr>
<td align="left">👘 <code>:kimono:</code></td>
<td align="left">👙 <code>:bikini:</code></td>
<td align="left">🎀 <code>:ribbon:</code></td>
</tr>
<tr>
<td align="left">🎩 <code>:tophat:</code></td>
<td align="left">👑 <code>:crown:</code></td>
<td align="left">👒 <code>:womans_hat:</code></td>
</tr>
<tr>
<td align="left">👞 <code>:mans_shoe:</code></td>
<td align="left">🌂 <code>:closed_umbrella:</code></td>
<td align="left">💼 <code>:briefcase:</code></td>
</tr>
<tr>
<td align="left">👜 <code>:handbag:</code></td>
<td align="left">👝 <code>:pouch:</code></td>
<td align="left">👛 <code>:purse:</code></td>
</tr>
<tr>
<td align="left">👓 <code>:eyeglasses:</code></td>
<td align="left">🎣 <code>:fishing_pole_and_fish:</code></td>
<td align="left">☕️ <code>:coffee:</code></td>
</tr>
<tr>
<td align="left">🍵 <code>:tea:</code></td>
<td align="left">🍶 <code>:sake:</code></td>
<td align="left">🍼 <code>:baby_bottle:</code></td>
</tr>
<tr>
<td align="left">🍺 <code>:beer:</code></td>
<td align="left">🍻 <code>:beers:</code></td>
<td align="left">🍸 <code>:cocktail:</code></td>
</tr>
<tr>
<td align="left">🍹 <code>:tropical_drink:</code></td>
<td align="left">🍷 <code>:wine_glass:</code></td>
<td align="left">🍴 <code>:fork_and_knife:</code></td>
</tr>
<tr>
<td align="left">🍕 <code>:pizza:</code></td>
<td align="left">🍔 <code>:hamburger:</code></td>
<td align="left">🍟 <code>:fries:</code></td>
</tr>
<tr>
<td align="left">🍗 <code>:poultry_leg:</code></td>
<td align="left">🍖 <code>:meat_on_bone:</code></td>
<td align="left">🍝 <code>:spaghetti:</code></td>
</tr>
<tr>
<td align="left">🍛 <code>:curry:</code></td>
<td align="left">🍤 <code>:fried_shrimp:</code></td>
<td align="left">🍱 <code>:bento:</code></td>
</tr>
<tr>
<td align="left">🍣 <code>:sushi:</code></td>
<td align="left">🍥 <code>:fish_cake:</code></td>
<td align="left">🍙 <code>:rice_ball:</code></td>
</tr>
<tr>
<td align="left">🍘 <code>:rice_cracker:</code></td>
<td align="left">🍚 <code>:rice:</code></td>
<td align="left">🍜 <code>:ramen:</code></td>
</tr>
<tr>
<td align="left">🍲 <code>:stew:</code></td>
<td align="left">🍢 <code>:oden:</code></td>
<td align="left">🍡 <code>:dango:</code></td>
</tr>
<tr>
<td align="left">🥚 <code>:egg:</code></td>
<td align="left">🍞 <code>:bread:</code></td>
<td align="left">🍩 <code>:doughnut:</code></td>
</tr>
<tr>
<td align="left">🍮 <code>:custard:</code></td>
<td align="left">🍦 <code>:icecream:</code></td>
<td align="left">🍨 <code>:ice_cream:</code></td>
</tr>
<tr>
<td align="left">🍧 <code>:shaved_ice:</code></td>
<td align="left">🎂 <code>:birthday:</code></td>
<td align="left">🍰 <code>:cake:</code></td>
</tr>
<tr>
<td align="left">🍪 <code>:cookie:</code></td>
<td align="left">🍫 <code>:chocolate_bar:</code></td>
<td align="left">🍬 <code>:candy:</code></td>
</tr>
<tr>
<td align="left">🍭 <code>:lollipop:</code></td>
<td align="left">🍯 <code>:honey_pot:</code></td>
<td align="left">🍎 <code>:apple:</code></td>
</tr>
<tr>
<td align="left">🍏 <code>:green_apple:</code></td>
<td align="left">🍊 <code>:tangerine:</code></td>
<td align="left">🍋 <code>:lemon:</code></td>
</tr>
<tr>
<td align="left">🍒 <code>:cherries:</code></td>
<td align="left">🍇 <code>:grapes:</code></td>
<td align="left">🍉 <code>:watermelon:</code></td>
</tr>
<tr>
<td align="left">🍓 <code>:strawberry:</code></td>
<td align="left">🍑 <code>:peach:</code></td>
<td align="left">🍈 <code>:melon:</code></td>
</tr>
<tr>
<td align="left">🍌 <code>:banana:</code></td>
<td align="left">🍐 <code>:pear:</code></td>
<td align="left">🍍 <code>:pineapple:</code></td>
</tr>
<tr>
<td align="left">🍠 <code>:sweet_potato:</code></td>
<td align="left">🍆 <code>:eggplant:</code></td>
<td align="left">🍅 <code>:tomato:</code></td>
</tr>
<tr>
<td align="left">🌽 <code>:corn:</code></td>
<td align="left"></td>
<td align="left"></td>
</tr>
</tbody></table>
<h3 id="Places"><a href="#Places" class="headerlink" title="Places"></a>Places</h3><table>
<thead>
<tr>
<th align="left">🏠 <code>:house:</code></th>
<th align="left">🏡 <code>:house_with_garden:</code></th>
<th align="left">🏫 <code>:school:</code></th>
</tr>
</thead>
<tbody><tr>
<td align="left">🏢 <code>:office:</code></td>
<td align="left">🏣 <code>:post_office:</code></td>
<td align="left">🏥 <code>:hospital:</code></td>
</tr>
<tr>
<td align="left">🏦 <code>:bank:</code></td>
<td align="left">🏪 <code>:convenience_store:</code></td>
<td align="left">🏩 <code>:love_hotel:</code></td>
</tr>
<tr>
<td align="left">🏨 <code>:hotel:</code></td>
<td align="left">💒 <code>:wedding:</code></td>
<td align="left">⛪️ <code>:church:</code></td>
</tr>
<tr>
<td align="left">🏬 <code>:department_store:</code></td>
<td align="left">🏤 <code>:european_post_office:</code></td>
<td align="left">🌇 <code>:city_sunrise:</code></td>
</tr>
<tr>
<td align="left">🌆 <code>:city_sunset:</code></td>
<td align="left">🏯 <code>:japanese_castle:</code></td>
<td align="left">🏰 <code>:european_castle:</code></td>
</tr>
<tr>
<td align="left">⛺️ <code>:tent:</code></td>
<td align="left">🏭 <code>:factory:</code></td>
<td align="left">🗼 <code>:tokyo_tower:</code></td>
</tr>
<tr>
<td align="left">🗾 <code>:japan:</code></td>
<td align="left">🗻 <code>:mount_fuji:</code></td>
<td align="left">🌄 <code>:sunrise_over_mountains:</code></td>
</tr>
<tr>
<td align="left">🌅 <code>:sunrise:</code></td>
<td align="left">🌠 <code>:stars:</code></td>
<td align="left">🗽 <code>:statue_of_liberty:</code></td>
</tr>
<tr>
<td align="left">🌉 <code>:bridge_at_night:</code></td>
<td align="left">🎠 <code>:carousel_horse:</code></td>
<td align="left">🌈 <code>:rainbow:</code></td>
</tr>
<tr>
<td align="left">🎡 <code>:ferris_wheel:</code></td>
<td align="left">⛲️ <code>:fountain:</code></td>
<td align="left">🎢 <code>:roller_coaster:</code></td>
</tr>
<tr>
<td align="left">🚢 <code>:ship:</code></td>
<td align="left">🚤 <code>:speedboat:</code></td>
<td align="left">⛵️ <code>:boat:</code></td>
</tr>
<tr>
<td align="left">⛵️ <code>:sailboat:</code></td>
<td align="left">🚣 <code>:rowboat:</code></td>
<td align="left">⚓️ <code>:anchor:</code></td>
</tr>
<tr>
<td align="left">🚀 <code>:rocket:</code></td>
<td align="left">✈️ <code>:airplane:</code></td>
<td align="left">🚁 <code>:helicopter:</code></td>
</tr>
<tr>
<td align="left">🚂 <code>:steam_locomotive:</code></td>
<td align="left">🚊 <code>:tram:</code></td>
<td align="left">🚞 <code>:mountain_railway:</code></td>
</tr>
<tr>
<td align="left">🚲 <code>:bike:</code></td>
<td align="left">🚡 <code>:aerial_tramway:</code></td>
<td align="left">🚟 <code>:suspension_railway:</code></td>
</tr>
<tr>
<td align="left">🚠 <code>:mountain_cableway:</code></td>
<td align="left">🚜 <code>:tractor:</code></td>
<td align="left">🚙 <code>:blue_car:</code></td>
</tr>
<tr>
<td align="left">🚘 <code>:oncoming_automobile:</code></td>
<td align="left">🚗 <code>:car:</code></td>
<td align="left">🚗 <code>:red_car:</code></td>
</tr>
<tr>
<td align="left">🚕 <code>:taxi:</code></td>
<td align="left">🚖 <code>:oncoming_taxi:</code></td>
<td align="left">🚛 <code>:articulated_lorry:</code></td>
</tr>
<tr>
<td align="left">🚌 <code>:bus:</code></td>
<td align="left">🚍 <code>:oncoming_bus:</code></td>
<td align="left">🚨 <code>:rotating_light:</code></td>
</tr>
<tr>
<td align="left">🚓 <code>:police_car:</code></td>
<td align="left">🚔 <code>:oncoming_police_car:</code></td>
<td align="left">🚒 <code>:fire_engine:</code></td>
</tr>
<tr>
<td align="left">🚑 <code>:ambulance:</code></td>
<td align="left">🚐 <code>:minibus:</code></td>
<td align="left">🚚 <code>:truck:</code></td>
</tr>
<tr>
<td align="left">🚋 <code>:train:</code></td>
<td align="left">🚉 <code>:station:</code></td>
<td align="left">🚆 <code>:train2:</code></td>
</tr>
<tr>
<td align="left">🚅 <code>:bullettrain_front:</code></td>
<td align="left">🚄 <code>:bullettrain_side:</code></td>
<td align="left">🚈 <code>:light_rail:</code></td>
</tr>
<tr>
<td align="left">🚝 <code>:monorail:</code></td>
<td align="left">🚃 <code>:railway_car:</code></td>
<td align="left">🚎 <code>:trolleybus:</code></td>
</tr>
<tr>
<td align="left">🎫 <code>:ticket:</code></td>
<td align="left">⛽️ <code>:fuelpump:</code></td>
<td align="left">🚦 <code>:vertical_traffic_light:</code></td>
</tr>
<tr>
<td align="left">🚥 <code>:traffic_light:</code></td>
<td align="left">⚠️ <code>:warning:</code></td>
<td align="left">🚧 <code>:construction:</code></td>
</tr>
<tr>
<td align="left">🔰 <code>:beginner:</code></td>
<td align="left">🏧 <code>:atm:</code></td>
<td align="left">🎰 <code>:slot_machine:</code></td>
</tr>
<tr>
<td align="left">🚏 <code>:busstop:</code></td>
<td align="left">💈 <code>:barber:</code></td>
<td align="left">♨️ <code>:hotsprings:</code></td>
</tr>
<tr>
<td align="left">🏁 <code>:checkered_flag:</code></td>
<td align="left">🎌 <code>:crossed_flags:</code></td>
<td align="left">🏮 <code>:izakaya_lantern:</code></td>
</tr>
<tr>
<td align="left">🗿 <code>:moyai:</code></td>
<td align="left">🎪 <code>:circus_tent:</code></td>
<td align="left">🎭 <code>:performing_arts:</code></td>
</tr>
<tr>
<td align="left">📍 <code>:round_pushpin:</code></td>
<td align="left">🚩 <code>:triangular_flag_on_post:</code></td>
<td align="left"></td>
</tr>
</tbody></table>
<h3 id="Symbols"><a href="#Symbols" class="headerlink" title="Symbols"></a>Symbols</h3><table>
<thead>
<tr>
<th align="left">1️⃣ <code>:one:</code></th>
<th align="left">2️⃣ <code>:two:</code></th>
<th align="left">3️⃣ <code>:three:</code></th>
</tr>
</thead>
<tbody><tr>
<td align="left">4️⃣ <code>:four:</code></td>
<td align="left">5️⃣ <code>:five:</code></td>
<td align="left">6️⃣ <code>:six:</code></td>
</tr>
<tr>
<td align="left">7️⃣ <code>:seven:</code></td>
<td align="left">8️⃣ <code>:eight:</code></td>
<td align="left">9️⃣ <code>:nine:</code></td>
</tr>
<tr>
<td align="left">🔟 <code>:keycap_ten:</code></td>
<td align="left">🔢 <code>:1234:</code></td>
<td align="left">0️⃣ <code>:zero:</code></td>
</tr>
<tr>
<td align="left">#️⃣ <code>:hash:</code></td>
<td align="left">🔣 <code>:symbols:</code></td>
<td align="left">◀️ <code>:arrow_backward:</code></td>
</tr>
<tr>
<td align="left">⬇️ <code>:arrow_down:</code></td>
<td align="left">▶️ <code>:arrow_forward:</code></td>
<td align="left">⬅️ <code>:arrow_left:</code></td>
</tr>
<tr>
<td align="left">🔠 <code>:capital_abcd:</code></td>
<td align="left">🔡 <code>:abcd:</code></td>
<td align="left">🔤 <code>:abc:</code></td>
</tr>
<tr>
<td align="left">↙️ <code>:arrow_lower_left:</code></td>
<td align="left">↘️ <code>:arrow_lower_right:</code></td>
<td align="left">➡️ <code>:arrow_right:</code></td>
</tr>
<tr>
<td align="left">⬆️ <code>:arrow_up:</code></td>
<td align="left">↖️ <code>:arrow_upper_left:</code></td>
<td align="left">↗️ <code>:arrow_upper_right:</code></td>
</tr>
<tr>
<td align="left">⏬ <code>:arrow_double_down:</code></td>
<td align="left">⏫ <code>:arrow_double_up:</code></td>
<td align="left">🔽 <code>:arrow_down_small:</code></td>
</tr>
<tr>
<td align="left">⤵️ <code>:arrow_heading_down:</code></td>
<td align="left">⤴️ <code>:arrow_heading_up:</code></td>
<td align="left">↩️ <code>:leftwards_arrow_with_hook:</code></td>
</tr>
<tr>
<td align="left">↪️ <code>:arrow_right_hook:</code></td>
<td align="left">↔️ <code>:left_right_arrow:</code></td>
<td align="left">↕️ <code>:arrow_up_down:</code></td>
</tr>
<tr>
<td align="left">🔼 <code>:arrow_up_small:</code></td>
<td align="left">🔃 <code>:arrows_clockwise:</code></td>
<td align="left">🔄 <code>:arrows_counterclockwise:</code></td>
</tr>
<tr>
<td align="left">⏪ <code>:rewind:</code></td>
<td align="left">⏩ <code>:fast_forward:</code></td>
<td align="left">ℹ️ <code>:information_source:</code></td>
</tr>
<tr>
<td align="left">🆗 <code>:ok:</code></td>
<td align="left">🔀 <code>:twisted_rightwards_arrows:</code></td>
<td align="left">🔁 <code>:repeat:</code></td>
</tr>
<tr>
<td align="left">🔂 <code>:repeat_one:</code></td>
<td align="left">🆕 <code>:new:</code></td>
<td align="left">🔝 <code>:top:</code></td>
</tr>
<tr>
<td align="left">🆙 <code>:up:</code></td>
<td align="left">🆒 <code>:cool:</code></td>
<td align="left">🆓 <code>:free:</code></td>
</tr>
<tr>
<td align="left">🆖 <code>:ng:</code></td>
<td align="left">🎦 <code>:cinema:</code></td>
<td align="left">🈁 <code>:koko:</code></td>
</tr>
<tr>
<td align="left">📶 <code>:signal_strength:</code></td>
<td align="left"></td>
<td align="left"></td>
</tr>
<tr>
<td align="left">🈂️ <code>:sa:</code></td>
<td align="left">🚻 <code>:restroom:</code></td>
<td align="left">🚹 <code>:mens:</code></td>
</tr>
<tr>
<td align="left">🚺 <code>:womens:</code></td>
<td align="left">🚼 <code>:baby_symbol:</code></td>
<td align="left">🚭 <code>:no_smoking:</code></td>
</tr>
<tr>
<td align="left">🅿️ <code>:parking:</code></td>
<td align="left">♿️ <code>:wheelchair:</code></td>
<td align="left">🚇 <code>:metro:</code></td>
</tr>
<tr>
<td align="left">🛄 <code>:baggage_claim:</code></td>
<td align="left">🉑 <code>:accept:</code></td>
<td align="left">🚾 <code>:wc:</code></td>
</tr>
<tr>
<td align="left">🚰 <code>:potable_water:</code></td>
<td align="left">🚮 <code>:put_litter_in_its_place:</code></td>
<td align="left">㊙️ <code>:secret:</code></td>
</tr>
<tr>
<td align="left">㊗️ <code>:congratulations:</code></td>
<td align="left">Ⓜ️ <code>:m:</code></td>
<td align="left">🛂 <code>:passport_control:</code></td>
</tr>
<tr>
<td align="left">🛅 <code>:left_luggage:</code></td>
<td align="left">🛃 <code>:customs:</code></td>
<td align="left">🉐 <code>:ideograph_advantage:</code></td>
</tr>
<tr>
<td align="left">🆑 <code>:cl:</code></td>
<td align="left">🆘 <code>:sos:</code></td>
<td align="left">🆔 <code>:id:</code></td>
</tr>
<tr>
<td align="left">🚫 <code>:no_entry_sign:</code></td>
<td align="left">🔞 <code>:underage:</code></td>
<td align="left">📵 <code>:no_mobile_phones:</code></td>
</tr>
<tr>
<td align="left">🚯 <code>:do_not_litter:</code></td>
<td align="left">🚱 <code>:non-potable_water:</code></td>
<td align="left">🚳 <code>:no_bicycles:</code></td>
</tr>
<tr>
<td align="left">🚷 <code>:no_pedestrians:</code></td>
<td align="left">🚸 <code>:children_crossing:</code></td>
<td align="left">⛔️ <code>:no_entry:</code></td>
</tr>
<tr>
<td align="left">✳️ <code>:eight_spoked_asterisk:</code></td>
<td align="left">✴️ <code>:eight_pointed_black_star:</code></td>
<td align="left">💟 <code>:heart_decoration:</code></td>
</tr>
<tr>
<td align="left">🆚 <code>:vs:</code></td>
<td align="left">📳 <code>:vibration_mode:</code></td>
<td align="left">📴 <code>:mobile_phone_off:</code></td>
</tr>
<tr>
<td align="left">💹 <code>:chart:</code></td>
<td align="left">💱 <code>:currency_exchange:</code></td>
<td align="left">♈️ <code>:aries:</code></td>
</tr>
<tr>
<td align="left">♉️ <code>:taurus:</code></td>
<td align="left">♊️ <code>:gemini:</code></td>
<td align="left">♋️ <code>:cancer:</code></td>
</tr>
<tr>
<td align="left">♌️ <code>:leo:</code></td>
<td align="left">♍️ <code>:virgo:</code></td>
<td align="left">♎️ <code>:libra:</code></td>
</tr>
<tr>
<td align="left">♏️ <code>:scorpius:</code></td>
<td align="left">♐️ <code>:sagittarius:</code></td>
<td align="left">♑️ <code>:capricorn:</code></td>
</tr>
<tr>
<td align="left">♒️ <code>:aquarius:</code></td>
<td align="left">♓️ <code>:pisces:</code></td>
<td align="left">⛎ <code>:ophiuchus:</code></td>
</tr>
<tr>
<td align="left">🔯 <code>:six_pointed_star:</code></td>
<td align="left">❎ <code>:negative_squared_cross_mark:</code></td>
<td align="left">🅰️ <code>:a:</code></td>
</tr>
<tr>
<td align="left">🅱️ <code>:b:</code></td>
<td align="left">🆎 <code>:ab:</code></td>
<td align="left">🅾️ <code>:o2:</code></td>
</tr>
<tr>
<td align="left">💠 <code>:diamond_shape_with_a_dot_inside:</code></td>
<td align="left">♻️ <code>:recycle:</code></td>
<td align="left">🔚 <code>:end:</code></td>
</tr>
<tr>
<td align="left">🔛 <code>:on:</code></td>
<td align="left">🔜 <code>:soon:</code></td>
<td align="left">🕐 <code>:clock1:</code></td>
</tr>
<tr>
<td align="left">🕜 <code>:clock130:</code></td>
<td align="left">🕙 <code>:clock10:</code></td>
<td align="left">🕥 <code>:clock1030:</code></td>
</tr>
<tr>
<td align="left">🕚 <code>:clock11:</code></td>
<td align="left">🕦 <code>:clock1130:</code></td>
<td align="left">🕛 <code>:clock12:</code></td>
</tr>
<tr>
<td align="left">🕧 <code>:clock1230:</code></td>
<td align="left">🕑 <code>:clock2:</code></td>
<td align="left">🕝 <code>:clock230:</code></td>
</tr>
<tr>
<td align="left">🕒 <code>:clock3:</code></td>
<td align="left">🕞 <code>:clock330:</code></td>
<td align="left">🕓 <code>:clock4:</code></td>
</tr>
<tr>
<td align="left">🕟 <code>:clock430:</code></td>
<td align="left">🕔 <code>:clock5:</code></td>
<td align="left">🕠 <code>:clock530:</code></td>
</tr>
<tr>
<td align="left">🕕 <code>:clock6:</code></td>
<td align="left">🕡 <code>:clock630:</code></td>
<td align="left">🕖 <code>:clock7:</code></td>
</tr>
<tr>
<td align="left">🕢 <code>:clock730:</code></td>
<td align="left">🕗 <code>:clock8:</code></td>
<td align="left">🕣 <code>:clock830:</code></td>
</tr>
<tr>
<td align="left">🕘 <code>:clock9:</code></td>
<td align="left">🕤 <code>:clock930:</code></td>
<td align="left">💲 <code>:heavy_dollar_sign:</code></td>
</tr>
<tr>
<td align="left">©️ <code>:copyright:</code></td>
<td align="left">®️ <code>:registered:</code></td>
<td align="left">™️ <code>:tm:</code></td>
</tr>
<tr>
<td align="left">❌ <code>:x:</code></td>
<td align="left">❗️ <code>:heavy_exclamation_mark:</code></td>
<td align="left">‼️ <code>:bangbang:</code></td>
</tr>
<tr>
<td align="left">⁉️ <code>:interrobang:</code></td>
<td align="left">⭕️ <code>:o:</code></td>
<td align="left">✖️ <code>:heavy_multiplication_x:</code></td>
</tr>
<tr>
<td align="left">➕ <code>:heavy_plus_sign:</code></td>
<td align="left">➖ <code>:heavy_minus_sign:</code></td>
<td align="left">➗ <code>:heavy_division_sign:</code></td>
</tr>
<tr>
<td align="left">💮 <code>:white_flower:</code></td>
<td align="left">💯 <code>:100:</code></td>
<td align="left">✔️ <code>:heavy_check_mark:</code></td>
</tr>
<tr>
<td align="left">☑️ <code>:ballot_box_with_check:</code></td>
<td align="left">🔘 <code>:radio_button:</code></td>
<td align="left">🔗 <code>:link:</code></td>
</tr>
<tr>
<td align="left">➰ <code>:curly_loop:</code></td>
<td align="left">〰️ <code>:wavy_dash:</code></td>
<td align="left">〽️ <code>:part_alternation_mark:</code></td>
</tr>
<tr>
<td align="left">🔱 <code>:trident:</code></td>
<td align="left">🔻 <code>:small_red_triangle_down:</code></td>
<td align="left">🔺 <code>:small_red_triangle:</code></td>
</tr>
<tr>
<td align="left">✅ <code>:white_check_mark:</code></td>
<td align="left">🔲 <code>:black_square_button:</code></td>
<td align="left">🔳 <code>:white_square_button:</code></td>
</tr>
<tr>
<td align="left">⚫️ <code>:black_circle:</code></td>
<td align="left">⚪️ <code>:white_circle:</code></td>
<td align="left">🔴 <code>:red_circle:</code></td>
</tr>
<tr>
<td align="left">🔵 <code>:large_blue_circle:</code></td>
<td align="left">🔷 <code>:large_blue_diamond:</code></td>
<td align="left">🔶 <code>:large_orange_diamond:</code></td>
</tr>
<tr>
<td align="left">🔹 <code>:small_blue_diamond:</code></td>
<td align="left">🔸 <code>:small_orange_diamond:</code></td>
<td align="left"></td>
</tr>
</tbody></table>
<h2 id="参考资料："><a href="#参考资料：" class="headerlink" title="参考资料："></a>参考资料：</h2><p><a target="_blank" rel="noopener external nofollow noreferrer" href="https://hasaik.com/posts/9b280ea3.html">Hexo 中使用 emoji 表情</a></p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/f36eea83.html">http://blog.mhy.loc.cc/archives/f36eea83.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a><a class="post-meta__tags" href="/tags/Hexo/">Hexo</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/838e5b66.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717232609.jpg" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">JavaScript数据结构和算法：数组</div></div></a></div><div class="next-post pull-right"><a href="/archives/2798a2b1.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">给文章标题添加一个emoji表情</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2022-02-10</div><div class="title">使用GitHub Actions 实现自动化部署和部署到服务器</div></div></a></div><div><a href="/archives/353666f0.html" title="给你的博客添加一个收藏页"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200719133158.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-19</div><div class="title">给你的博客添加一个收藏页</div></div></a></div><div><a href="/archives/2798a2b1.html" title="给文章标题添加一个emoji表情"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">给文章标题添加一个emoji表情</div></div></a></div><div><a href="/archives/f06684a1.html" title="NexT版本更新V8.0记录"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-19</div><div class="title">NexT版本更新V8.0记录</div></div></a></div><div><a href="/archives/b1e33f9d.html" title="sitemeta渐变背景实现"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-03</div><div class="title">sitemeta渐变背景实现</div></div></a></div><div><a href="/archives/285695a6.html" title="解决滚动条导致页面跳动的问题"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184106.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-04-27</div><div class="title">解决滚动条导致页面跳动的问题</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%96%B9%E6%B3%95%E4%B8%80"><span class="toc-text">方法一</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AE%89%E8%A3%85"><span class="toc-text">安装</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%85%8D%E7%BD%AE"><span class="toc-text">配置</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E6%96%B9%E6%B3%95%E4%BA%8C"><span class="toc-text">方法二</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AE%89%E8%A3%85-1"><span class="toc-text">安装</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%85%8D%E7%BD%AE-1"><span class="toc-text">配置</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E4%BD%BF%E7%94%A8%E6%96%B9%E6%B3%95"><span class="toc-text">使用方法</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%96%B9%E6%B3%95%E4%BA%8C%E9%A2%9D%E5%A4%96%E7%9A%84%E7%94%A8%E6%B3%95%EF%BC%9A"><span class="toc-text">方法二额外的用法：</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#emoji-%E7%BC%96%E7%A0%81%E5%90%88%E9%9B%86"><span class="toc-text">emoji 编码合集</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#People"><span class="toc-text">People</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Nature"><span class="toc-text">Nature</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Objects"><span class="toc-text">Objects</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Places"><span class="toc-text">Places</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#Symbols"><span class="toc-text">Symbols</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%8F%82%E8%80%83%E8%B5%84%E6%96%99%EF%BC%9A"><span class="toc-text">参考资料：</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>