<%
var hashCode = function (str) {
    if (!str && str.length === 0) {
        return 0;
    }

    var hash = 0;
    for (var i = 0, len = str.length; i < len; i++) {
        hash = ((hash << 5) - hash) + str.charCodeAt(i);
        hash |= 0;
    }
    return hash;
};

var featureimg = '/medias/featureimages/0.jpg';
var featureImages = theme.featureImages;
%>

<article id="prenext-posts" class="prev-next articles">
    <div class="row article-row">
        <% if (page.prev) { %>
        <div class="article col s12 m6" data-aos="fade-up">
            <div class="article-badge left-badge text-color">
                <i class="fas fa-chevron-left"></i>&nbsp;<%= __('prev') %></div>
            <div class="card">
                <a href="<%- url_for(page.prev.path) %>">
                    <div class="card-image">
                        <% if (page.prev.img) { %>
                        <img src="<%- url_for(page.prev.img) %>" class="responsive-img" alt="<%= page.prev.title %>">
                        <% } else { %>
                        <%
                            var len = featureImages.length;
                            var num = Math.abs(hashCode(page.prev.title) % len);
                            featureimg = featureImages[num];
                        %>
                        <img src="<%- theme.jsDelivr.url %><%- url_for(featureimg) %>" class="responsive-img" alt="<%= page.prev.title %>">
                        <% } %>
                        <span class="card-title"><%= page.prev.title %></span>
                    </div>
                </a>
                <div class="card-content article-content">
                    <div class="summary block-with-text">
                        <% if (page.prev.summary && page.prev.summary.length > 0) { %>
                            <%- page.prev.summary %>
                        <% } else { %>
                            <%- strip_html(page.prev.excerpt).substring(0, 120) %>
                        <% } %>
                    </div>
                    <div class="publish-info">
                        <span class="publish-date">
                            <i class="far fa-clock fa-fw icon-date"></i><%= date(page.prev.date, config.date_format) %>
                        </span>
                        <span class="publish-author">
                            <% if (page.prev.categories && page.prev.categories.length > 0) { %>
                            <i class="fas fa-bookmark fa-fw icon-category"></i>
                            <% page.prev.categories.forEach(category => { %>
                            <a href="<%- url_for(category.path) %>" class="post-category">
                                    <%- category.name %>
                                </a>
                            <% }); %>
                            <% } else if (page.prev.author && page.prev.author.length > 0) { %>
                            <i class="fas fa-user fa-fw"></i>
                            <%- page.prev.author %>
                            <% } else { %>
                            <i class="fas fa-user fa-fw"></i>
                            <%- config.author %>
                            <% } %>
                        </span>
                    </div>
                </div>
                <% if(page.prev.tags && page.prev.tags.length > 0) { %>
                <div class="card-action article-tags">
                    <% page.prev.tags.forEach(tag => { %>
                    <a href="<%- url_for(tag.path) %>">
                        <span class="chip bg-color"><%= tag.name %></span>
                    </a>
                    <% }); %>
                </div>
                <% } %>
            </div>
        </div>
        <% } else { %>
        <div class="article col s12 m6" data-aos="fade-up" data-aos="fade-up">
            <div class="article-badge left-badge text-color">
                <i class="far fa-dot-circle"></i>&nbsp;<%= __('curr') %>
            </div>
            <div class="card">
                <a href="<%- url_for(page.path) %>">
                    <div class="card-image">
                        <% if (page.img) { %>
                        <img src="<%- url_for(page.img) %>" class="responsive-img" alt="<%= page.title %>">
                        <% } else { %>
                        <%
                            featureimg = featureImages[Math.abs(hashCode(page.title) % featureImages.length)];
                        %>
                        <img src="<%- theme.jsDelivr.url %><%- url_for(featureimg) %>" class="responsive-img" alt="<%= page.title %>">
                        <% } %>
                        <span class="card-title"><%= page.title %></span>
                    </div>
                </a>
                <div class="card-content article-content">
                    <div class="summary block-with-text">
                        <% if (page.summary && page.summary.length > 0) { %>
                            <%- page.summary %>
                        <% } else { %>
                            <%- strip_html(page.excerpt).substring(0, 120) %>
                        <% } %>
                    </div>
                    <div class="publish-info">
                            <span class="publish-date">
                                <i class="far fa-clock fa-fw icon-date"></i><%= date(page.date, config.date_format) %>
                            </span>
                        <span class="publish-author">
                            <% if (page.categories && page.categories.length > 0) { %>
                            <i class="fas fa-bookmark fa-fw icon-category"></i>
                            <% page.categories.forEach(category => { %>
                            <a href="<%- url_for(category.path) %>" class="post-category">
                                    <%- category.name %>
                                </a>
                            <% }); %>
                            <% } else if (page.author && page.author.length > 0) { %>
                            <i class="fas fa-user fa-fw"></i>
                            <%- page.author %>
                            <% } else { %>
                            <i class="fas fa-user fa-fw"></i>
                            <%- config.author %>
                            <% } %>
                        </span>
                    </div>
                </div>

                <% if(page.tags && page.tags.length > 0) { %>
                <div class="card-action article-tags">
                    <% page.tags.forEach(tag => { %>
                    <a href="<%- url_for(tag.path) %>">
                        <span class="chip bg-color"><%= tag.name %></span>
                    </a>
                    <% }); %>
                </div>
                <% } %>
            </div>
        </div>
        <% } %>
        <% if (page.next) { %>
        <div class="article col s12 m6" data-aos="fade-up">
            <div class="article-badge right-badge text-color">
                <%= __('next') %>&nbsp;<i class="fas fa-chevron-right"></i>
            </div>
            <div class="card">
                <a href="<%- url_for(page.next.path) %>">
                    <div class="card-image">
                        <% if (page.next.img) { %>
                        <img src="<%- url_for(page.next.img) %>" class="responsive-img" alt="<%= page.next.title %>">
                        <% } else { %>
                        <%
                            featureimg = featureImages[Math.abs(hashCode(page.next.title) % featureImages.length)];
                        %>
                        <img src="<%- theme.jsDelivr.url %><%- url_for(featureimg) %>" class="responsive-img" alt="<%= page.next.title %>">
                        <% } %>
                        <span class="card-title"><%= page.next.title %></span>
                    </div>
                </a>
                <div class="card-content article-content">
                    <div class="summary block-with-text">
                        <% if (page.next.summary && page.next.summary.length > 0) { %>
                            <%- page.next.summary %>
                        <% } else { %>
                            <%- strip_html(page.next.excerpt).substring(0, 120) %>
                        <% } %>
                    </div>
                    <div class="publish-info">
                            <span class="publish-date">
                                <i class="far fa-clock fa-fw icon-date"></i><%= date(page.next.date, config.date_format) %>
                            </span>
                        <span class="publish-author">
                            <% if (page.next.categories && page.next.categories.length > 0) { %>
                            <i class="fas fa-bookmark fa-fw icon-category"></i>
                            <% page.next.categories.forEach(category => { %>
                            <a href="<%- url_for(category.path) %>" class="post-category">
                                    <%- category.name %>
                                </a>
                            <% }); %>
                            <% } else if (page.next.author && page.next.author.length > 0) { %>
                            <i class="fas fa-user fa-fw"></i>
                            <%- page.next.author %>
                            <% } else { %>
                            <i class="fas fa-user fa-fw"></i>
                            <%- config.author %>
                            <% } %>
                        </span>
                    </div>
                </div>
                <% if(page.next.tags && page.next.tags.length > 0) { %>
                <div class="card-action article-tags">
                    <% page.next.tags.forEach(tag => { %>
                    <a href="<%- url_for(tag.path) %>">
                        <span class="chip bg-color"><%= tag.name %></span>
                    </a>
                    <% }); %>
                </div>
                <% } %>
            </div>
        </div>
        <% } else { %>
        <div class="article col s12 m6" data-aos="fade-up">
            <div class="article-badge right-badge text-color">
                <%= __('curr') %>&nbsp;<i class="far fa-dot-circle"></i>
            </div>
            <div class="card">
                <a href="<%- url_for(page.path) %>">
                    <div class="card-image">
                        <% if (page.img) { %>
                        <img src="<%- url_for(page.img) %>" class="responsive-img" alt="<%= page.title %>">
                        <% } else { %>
                        <%
                            featureimg = featureImages[Math.abs(hashCode(page.title) % featureImages.length)];
                        %>
                        <img src="<%- theme.jsDelivr.url %><%- url_for(featureimg) %>" class="responsive-img" alt="<%= page.title %>">
                        <% } %>
                        <span class="card-title"><%= page.title %></span>
                    </div>
                </a>
                <div class="card-content article-content">
                    <div class="summary block-with-text">
                        <% if (page.summary && page.summary.length > 0) { %>
                            <%- page.summary %>
                        <% } else { %>
                            <%- strip_html(page.excerpt).substring(0, 120) %>
                        <% } %>
                    </div>
                    <div class="publish-info">
                            <span class="publish-date">
                                <i class="far fa-clock fa-fw icon-date"></i><%= date(page.date, config.date_format) %>
                            </span>
                        <span class="publish-author">
                            <% if (page.categories && page.categories.length > 0) { %>
                            <i class="fas fa-bookmark fa-fw icon-category"></i>
                            <% page.categories.forEach(category => { %>
                            <a href="<%- url_for(category.path) %>" class="post-category">
                                    <%- category.name %>
                                </a>
                            <% }); %>
                            <% } else if (page.author && page.author.length > 0) { %>
                            <i class="fas fa-user fa-fw"></i>
                            <%- page.author %>
                            <% } else { %>
                            <i class="fas fa-user fa-fw"></i>
                            <%- config.author %>
                            <% } %>
                        </span>
                    </div>
                </div>

                <% if(page.tags && page.tags.length > 0) { %>
                <div class="card-action article-tags">
                    <% page.tags.forEach(tag => { %>
                    <a href="<%- url_for(tag.path) %>">
                        <span class="chip bg-color"><%= tag.name %></span>
                    </a>
                    <% }); %>
                </div>
                <% } %>
            </div>
        </div>
        <% } %>
    </div>
</article>
