<!DOCTYPE html><html lang="zh-CN" data-theme="light"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Hexo框架(三)：Next主题配置及美化 | 你真是一个美好的人类</title><meta name="keywords" content="博客搭建,Hexo,Next主题,Hexo美化"><meta name="author" content="ConstOwn"><meta name="copyright" content="ConstOwn"><meta name="format-detection" content="telephone=no"><meta name="theme-color" content="#ffffff"><meta name="description" content="（1）安装Next主题；（2）Hexo的基础配置； （3）Hexo常用美化；">
<meta property="og:type" content="article">
<meta property="og:title" content="Hexo框架(三)：Next主题配置及美化">
<meta property="og:url" content="http://blog.mhy.loc.cc/archives/264a3045.html">
<meta property="og:site_name" content="你真是一个美好的人类">
<meta property="og:description" content="（1）安装Next主题；（2）Hexo的基础配置； （3）Hexo常用美化；">
<meta property="og:locale" content="zh_CN">
<meta property="og:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png">
<meta property="article:published_time" content="2020-02-10T09:20:42.000Z">
<meta property="article:modified_time" content="2020-04-19T22:00:20.000Z">
<meta property="article:author" content="ConstOwn">
<meta property="article:tag" content="博客搭建">
<meta property="article:tag" content="Hexo">
<meta property="article:tag" content="Next主题">
<meta property="article:tag" content="Hexo美化">
<meta name="twitter:card" content="summary">
<meta name="twitter:image" content="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png"><link rel="shortcut icon" href="/images/favicon-32x32.png"><link rel="canonical" href="http://blog.mhy.loc.cc/archives/264a3045"><link rel="preconnect" href="//cdn.jsdelivr.net"/><link rel="preconnect" href="//www.google-analytics.com" crossorigin=""/><link rel="preconnect" href="//hm.baidu.com"/><link rel="preconnect" href="//busuanzi.ibruce.info"/><link rel="stylesheet" href="/css/index.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" media="print" onload="this.media='all'"><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?8eb1533491dd67c83f8cff0c82eb29c6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script><script async="async" src="https://www.googletagmanager.com/gtag/js?id=KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U"></script><script>window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());
gtag('config', 'KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U');
</script><script>const GLOBAL_CONFIG = { 
  root: '/',
  algolia: undefined,
  localSearch: {"path":"search.xml","languages":{"hits_empty":"找不到您查询的内容：${query}"}},
  translate: undefined,
  noticeOutdate: {"limitDay":30,"position":"top","messagePrev":"这篇文章最后更新于","messageNext":"天前，文章内容可能已经过时，请优先查看最新文章！"},
  highlight: {"plugin":"highlighjs","highlightCopy":true,"highlightLang":true,"highlightHeightLimit":false},
  copy: {
    success: '复制成功',
    error: '复制错误',
    noSupport: '浏览器不支持'
  },
  relativeDate: {
    homepage: false,
    post: false
  },
  runtime: '天',
  date_suffix: {
    just: '刚刚',
    min: '分钟前',
    hour: '小时前',
    day: '天前',
    month: '个月前'
  },
  copyright: {"limitCount":50,"languages":{"author":"作者: ConstOwn","link":"链接: ","source":"来源: 你真是一个美好的人类","info":"著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。"}},
  lightbox: 'null',
  Snackbar: undefined,
  source: {
    jQuery: 'https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js',
    justifiedGallery: {
      js: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/js/jquery.justifiedGallery.min.js',
      css: 'https://cdn.jsdelivr.net/npm/justifiedGallery/dist/css/justifiedGallery.min.css'
    },
    fancybox: {
      js: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@latest/dist/jquery.fancybox.min.css'
    }
  },
  isPhotoFigcaption: false,
  islazyload: true,
  isanchor: false
}</script><script id="config-diff">var GLOBAL_CONFIG_SITE = {
  title: 'Hexo框架(三)：Next主题配置及美化',
  isPost: true,
  isHome: false,
  isHighlightShrink: false,
  isToc: true,
  postUpdate: '2020-04-19 22:00:20'
}</script><noscript><style type="text/css">
  #nav {
    opacity: 1
  }
  .justified-gallery img {
    opacity: 1
  }

  #recent-posts time,
  #post-meta time {
    display: inline !important
  }
</style></noscript><script>(win=>{
    win.saveToLocal = {
      set: function setWithExpiry(key, value, ttl) {
        if (ttl === 0) return
        const now = new Date()
        const expiryDay = ttl * 86400000
        const item = {
          value: value,
          expiry: now.getTime() + expiryDay,
        }
        localStorage.setItem(key, JSON.stringify(item))
      },

      get: function getWithExpiry(key) {
        const itemStr = localStorage.getItem(key)

        if (!itemStr) {
          return undefined
        }
        const item = JSON.parse(itemStr)
        const now = new Date()

        if (now.getTime() > item.expiry) {
          localStorage.removeItem(key)
          return undefined
        }
        return item.value
      }
    }
  
    win.getScript = url => new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.async = true
      script.onerror = reject
      script.onload = script.onreadystatechange = function() {
        const loadState = this.readyState
        if (loadState && loadState !== 'loaded' && loadState !== 'complete') return
        script.onload = script.onreadystatechange = null
        resolve()
      }
      document.head.appendChild(script)
    })
  
      const asideStatus = saveToLocal.get('aside-status')
      if (asideStatus !== undefined) {
        if (asideStatus === 'hide') {
          document.documentElement.classList.add('hide-aside')
        } else {
          document.documentElement.classList.remove('hide-aside')
        }
      }
    
    const detectApple = () => {
      if (GLOBAL_CONFIG_SITE.isHome && /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)){
        document.documentElement.classList.add('apple')
      }
    }
    detectApple()
    })(window)</script><meta name="google-site-verification" content="KF2A9kM-tDfaKXgg0ZdpvUpi45b1qlFmyXR6xCyV79U" /><link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/constown/HexoCustomFile@0.0.4/dist/css/custom.min.css"><meta name="generator" content="Hexo 5.4.2"></head><body><div id="web_bg"></div><div id="sidebar"><div id="menu-mask"></div><div id="sidebar-menus"><div class="avatar-img is-center"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="onerror=null;src='/img/friend_404.gif'" alt="avatar"/></div><div class="site-data"><div class="data-item is-center"><div class="data-item-link"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div></div><div class="data-item is-center"><div class="data-item-link"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div></div><hr/><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div></div></div><div class="post" id="body-wrap"><header class="post-bg" id="page-header" style="background-image: url('https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200717233539.jpg')"><nav id="nav"><span id="blog_name"><a id="site-name" href="/">你真是一个美好的人类</a></span><div id="menus"><div id="search-button"><a class="site-page social-icon search"><i class="fas fa-search fa-fw"></i><span> 搜索</span></a></div><div class="menus_items"><div class="menus_item"><a class="site-page" href="/"><i class="fa-fw fas fa-home"></i><span> 首页</span></a></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-th"></i><span> 找文章</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/categories/"><i class="fa-fw fas fa-folder-open"></i><span> 分类</span></a></li><li><a class="site-page child" href="/tags/"><i class="fa-fw fas fa-tags"></i><span> 标签</span></a></li><li><a class="site-page child" href="/archives/"><i class="fa-fw fas fa-archive"></i><span> 归档</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-link"></i><span> 找链接</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/link/"><i class="fa-fw fas fa-user-plus"></i><span> 友情链接</span></a></li><li><a class="site-page child" href="/stars/"><i class="fa-fw fas fa-star"></i><span> 收藏网址</span></a></li></ul></div><div class="menus_item"><a class="site-page" href="javascript:void(0);" rel="external nofollow noreferrer"><i class="fa-fw fas fa-heart"></i><span> 关注我</span><i class="fas fa-chevron-down expand hide"></i></a><ul class="menus_item_child"><li><a class="site-page child" href="/about/"><i class="fa-fw fas fa-user"></i><span> 关于我</span></a></li><li><a class="site-page child" href="/message/"><i class="fa-fw fas fa-comments"></i><span> 留言板</span></a></li></ul></div></div><div id="toggle-menu"><a class="site-page"><i class="fas fa-bars fa-fw"></i></a></div></div></nav><div id="post-info"><h1 class="post-title">Hexo框架(三)：Next主题配置及美化</h1><div id="post-meta"><div class="meta-firstline"><span class="post-meta-date"><i class="far fa-calendar-alt fa-fw post-meta-icon"></i><span class="post-meta-label">发表于</span><time class="post-meta-date-created" datetime="2020-02-10T09:20:42.000Z" title="发表于 2020-02-10 09:20:42">2020-02-10</time><span class="post-meta-separator">|</span><i class="fas fa-history fa-fw post-meta-icon"></i><span class="post-meta-label">更新于</span><time class="post-meta-date-updated" datetime="2020-04-19T22:00:20.000Z" title="更新于 2020-04-19 22:00:20">2020-04-19</time></span><span class="post-meta-categories"><span class="post-meta-separator">|</span><i class="fas fa-inbox fa-fw post-meta-icon"></i><a class="post-meta-categories" href="/categories/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a></span></div><div class="meta-secondline"></div></div></div></header><main class="layout" id="content-inner"><div id="post"><article class="post-content" id="article-container"><div class="note danger flat"><h2 id="前言"><a href="#前言" class="headerlink" title="前言"></a>前言</h2><p>本篇设置基于 NEXT 主题 7.7.2 版本！</p>
</div>

<p>Hexo 框架允许我们更换自己喜欢的主题，用于构件不同风格的主题，因为我个人比较喜欢简洁风，所以我选择了 Next 主题。Next 主题也是特别常用的一款主题，如果你也和我一样喜欢简洁的风格，可以对我的配置进行参考。</p>
<p><strong>在正式开始配置之前，我们需要明确以下东西</strong> : <code>站点配置文件</code> `主题配置文件</p>
<ul>
<li><p>在 Hexo 中有<strong>两份</strong>主要的配置文件，其名称都是 <code>_config.yml</code>。</p>
</li>
<li><p><code>站点配置文件</code>，位于<strong>站点根目录</strong>下，用于网站的基础配置</p>
</li>
<li><p><code>主题配置文件 </code>，位于<strong>thems</strong>目录下，用于主题相关的配置</p>
</li>
<li><p>不同的主题会有不同的配置文件，<code>主题配置文件 </code> 通常由主题作者提供</p>
</li>
</ul>
<div class="note danger flat"><p><strong>注意：目前我已经更新至最新版本，目前采用了全新的配置方式，这里的主题配置文件，均指 hexo/source/_data 目录下的 next.yml 文件，你可以点击这里查看：<a href="/archives/5b20fbd0.html">关于博客主题持续更新的问题和我的新配置方式</a></strong></p>
</div>

<h2 id="安装-Next-主题"><a href="#安装-Next-主题" class="headerlink" title="安装 Next 主题"></a>安装 Next 主题</h2><h3 id="下载-Next"><a href="#下载-Next" class="headerlink" title="下载 Next"></a>下载 Next</h3><ul>
<li><p>Hexo 安装主题的方式非常简单，只需要将主题文件拷贝至站点目录的 <code>themes</code> 目录下， 然后修改下配置文件即可。具体到 NexT 来说，安装步骤如下。</p>
<p><strong>注</strong>：我们这里使用社区维护的最新版本。</p>
</li>
<li><p>我们使用 Git，克隆最新版本的方式进行安装，之后的更新我们也可以用 <code>git pull</code> 来进行快速更新，而不用再次下载压缩包替换</p>
</li>
<li><p>在终端窗口下，定位到 Hexo 站点根目录，在终端中输入：</p>
</li>
</ul>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">git clone https://github.com/theme<span class="literal">-next</span>/hexo<span class="literal">-theme-next</span> themes/next</span><br></pre></td></tr></table></figure>

<h3 id="启用主题"><a href="#启用主题" class="headerlink" title="启用主题"></a>启用主题</h3><ul>
<li>与所有 Hexo 主题启用的模式一样。 当 克隆/下载 完成后，打开 <code> 站点配置文件</code>， 找到 <code>theme</code> 字段，并将其值更改为 <code>next</code>。</li>
</ul>
<figure class="highlight yml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Extensions</span></span><br><span class="line"><span class="comment">## Plugins: https://hexo.io/plugins/</span></span><br><span class="line"><span class="comment">## Themes: https://hexo.io/themes/</span></span><br><span class="line"><span class="attr">theme:</span> <span class="string">next</span></span><br></pre></td></tr></table></figure>

<p>到此，NexT 主题安装完成。下一步我们将验证主题是否正确启用。在切换主题之后、验证之前， 我们最好使用 <code>hexo clean</code> 来清除 Hexo 的缓存。</p>
<h3 id="验证主题"><a href="#验证主题" class="headerlink" title="验证主题"></a>验证主题</h3><p>首先使用<code>hexo clean</code>清除缓存，然后使用<code>hexo g</code> 生成文件，然后使用<code>hexo s</code>来运行本地服务。当命令行输出中提示：</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">INFO  Hexo is running at http://localhost:4000 . Press Ctrl+C to stop.</span><br></pre></td></tr></table></figure>

<p>此时即可使用浏览器访问 <code>http://localhost:4000</code>，检查站点是否正确运行。</p>
<p>当你看到站点的外观与下图所示类似时即说明你已成功安装 NexT 主题。</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200321144826.png" alt="image-20200321144825051"></p>
<p>现在，你已经成功安装并启用了 NexT 主题。下一步我们将要更改一些主题的设定，包括个性化以及集成第三方服务。</p>
<h2 id="Hexo-基础配置"><a href="#Hexo-基础配置" class="headerlink" title="Hexo 基础配置"></a>Hexo 基础配置</h2><p>现在我们就正式开始配置，以下内容均为 <code>站点配置文件</code>：</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">title:</span> <span class="comment">#网站的标题</span></span><br><span class="line"><span class="attr">subtitle:</span> <span class="string">&#x27;&#x27;</span> <span class="comment">#网站副标题</span></span><br><span class="line"><span class="attr">description:</span> <span class="string">&#x27;&#x27;</span> <span class="comment">#网站描述</span></span><br><span class="line"><span class="attr">keywords:</span> <span class="comment">#网站关键字</span></span><br><span class="line"><span class="attr">author:</span> <span class="comment">#网站作者</span></span><br><span class="line"><span class="attr">language:</span> <span class="string">zh-CN</span> <span class="comment">#语言 zh-CN简体中文</span></span><br><span class="line"><span class="attr">timezone:</span> <span class="string">&#x27;&#x27;</span> <span class="comment">#时区，默认就好</span></span><br></pre></td></tr></table></figure>

<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">url:</span> <span class="comment">#网站地址 比如：https://yourname.github.io</span></span><br><span class="line"><span class="attr">root:</span> <span class="string">/</span> <span class="comment">#网站根目录</span></span><br></pre></td></tr></table></figure>

<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">#网站的url格式</span></span><br><span class="line"><span class="attr">permalink:</span> <span class="string">:category/:title/</span></span><br></pre></td></tr></table></figure>

<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">#自动提交到仓库，可以设置多个仓库</span></span><br><span class="line"><span class="attr">deploy:</span></span><br><span class="line">  <span class="attr">type:</span> <span class="string">git</span></span><br><span class="line">  <span class="attr">repo:</span></span><br><span class="line">    <span class="attr">GitHub:</span> <span class="string">https://github.com/yourname/yourname.github.io.git</span></span><br><span class="line">    <span class="attr">Gitee:</span> <span class="string">https://gitee.com/yourname/blog.git</span></span><br><span class="line">  <span class="attr">branch:</span> <span class="string">master</span></span><br></pre></td></tr></table></figure>

<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">#设置主题为next</span></span><br><span class="line"><span class="attr">theme:</span> <span class="string">next</span></span><br></pre></td></tr></table></figure>

<h2 id="Next-主题设置"><a href="#Next-主题设置" class="headerlink" title="Next 主题设置"></a>Next 主题设置</h2><p><strong>以下配置主要基于更高版本的 Next7，下面配置项如无特别指明，均为<code>主题配置文件</code>中的配置项</strong>，</p>
<h3 id="选择-Scheme"><a href="#选择-Scheme" class="headerlink" title="选择 Scheme"></a>选择 Scheme</h3><p>Scheme 是 NexT 提供的一种特性，借助于 Scheme，NexT 为你提供多种不同的外观。同时，几乎所有的配置都可以 在 Scheme 之间共用。目前 NexT 支持四种 Scheme，他们是：</p>
<ul>
<li>scheme: Muse</li>
<li>scheme: Mist</li>
<li>scheme: Pisces</li>
<li>scheme: Gemini</li>
</ul>
<p>Scheme 的切换通过更改 <code>主题配置文件</code>，搜索 scheme 关键字。 你会看到有四行 scheme 的配置，将你需用启用的 scheme 前面注释 <code>#</code> 去除即可。我个人选择 Gemini。</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Schemes</span></span><br><span class="line"><span class="comment">#scheme: Muse</span></span><br><span class="line"><span class="comment">#scheme: Mist</span></span><br><span class="line"><span class="comment">#scheme: Pisces</span></span><br><span class="line"><span class="attr">scheme:</span> <span class="string">Gemini</span></span><br></pre></td></tr></table></figure>

<h3 id="设置语言"><a href="#设置语言" class="headerlink" title="设置语言"></a>设置语言</h3><p>编辑 <code>站点配置文件</code>， 将 <code>language</code> 设置成你所需要的语言。建议明确设置你所需要的语言，例如选用简体中文，配置如下：</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">language:</span> <span class="string">zh-CN</span></span><br></pre></td></tr></table></figure>

<p>目前 NexT 支持的语言如以下表格所示:</p>
<table>
<thead>
<tr>
<th align="left">语言</th>
<th align="left">代码</th>
<th align="left">设定示例</th>
</tr>
</thead>
<tbody><tr>
<td align="left">English</td>
<td align="left"><code>en</code></td>
<td align="left"><code>language: en</code></td>
</tr>
<tr>
<td align="left">简体中文</td>
<td align="left"><code>zh-Hans</code></td>
<td align="left"><code>language: zh-CN</code></td>
</tr>
<tr>
<td align="left">Français</td>
<td align="left"><code>fr-FR</code></td>
<td align="left"><code>language: fr-FR</code></td>
</tr>
<tr>
<td align="left">Português</td>
<td align="left"><code>pt</code></td>
<td align="left"><code>language: pt</code> or <code>language: pt-BR</code></td>
</tr>
<tr>
<td align="left">繁體中文</td>
<td align="left"><code>zh-hk</code> 或者 <code>zh-tw</code></td>
<td align="left"><code>language: zh-hk</code></td>
</tr>
<tr>
<td align="left">Русский язык</td>
<td align="left"><code>ru</code></td>
<td align="left"><code>language: ru</code></td>
</tr>
<tr>
<td align="left">Deutsch</td>
<td align="left"><code>de</code></td>
<td align="left"><code>language: de</code></td>
</tr>
<tr>
<td align="left">日本語</td>
<td align="left"><code>ja</code></td>
<td align="left"><code>language: ja</code></td>
</tr>
<tr>
<td align="left">Indonesian</td>
<td align="left"><code>id</code></td>
<td align="left"><code>language: id</code></td>
</tr>
<tr>
<td align="left">Korean</td>
<td align="left"><code>ko</code></td>
<td align="left"><code>language: ko</code></td>
</tr>
</tbody></table>
<h3 id="设置菜单"><a href="#设置菜单" class="headerlink" title="设置菜单"></a>设置菜单</h3><p>菜单配置包括三个部分，第一是菜单项（名称和链接），第二是菜单项的显示文本，第三是菜单项对应的图标。 NexT 使用的是 <a target="_blank" rel="noopener external nofollow noreferrer" href="http://fontawesome.io/">Font Awesome</a> 提供的图标， Font Awesome 提供了 600+ 的图标，可以满足绝大的多数的场景，同时无须担心在 Retina 屏幕下 图标模糊的问题。</p>
<p>编辑 <code>主题配置文件</code>，修改以下内容：</p>
<ul>
<li>设定菜单内容，对应的字段是 <code>menu</code>。 菜单内容的设置格式是：<code>item name: link</code>。其中 <code>item name </code>是一个名称，这个名称并不直接显示在页面上，她将用于匹配图标以及翻译。</li>
</ul>
<h4 id="设置菜单项的显示中文文本"><a href="#设置菜单项的显示中文文本" class="headerlink" title="设置菜单项的显示中文文本"></a>设置菜单项的显示中文文本</h4><p><del>打开 <code>themes/next/languages/zh-Hans.yml</code> 文件,搜索 <code>menu</code> 关键字，修改对应中文或者新增。</del><br>最新版本此方法已经修改：你可以点击 <a href="/archives/5b20fbd0.html">这里</a> 查看其中的第 3.2 节。</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">menu:</span></span><br><span class="line">  <span class="attr">home:</span> <span class="string">首页</span></span><br><span class="line">  <span class="attr">archives:</span> <span class="string">归档</span></span><br><span class="line">  <span class="attr">categories:</span> <span class="string">分类</span></span><br><span class="line">  <span class="attr">tags:</span> <span class="string">标签</span></span><br><span class="line">  <span class="attr">about:</span> <span class="string">关于</span></span><br><span class="line">  <span class="attr">search:</span> <span class="string">搜索</span></span><br><span class="line">  <span class="attr">schedule:</span> <span class="string">日程表</span></span><br><span class="line">  <span class="attr">sitemap:</span> <span class="string">站点地图</span></span><br><span class="line">  <span class="attr">commonweal:</span> <span class="string">公益</span> <span class="number">404</span></span><br><span class="line">  <span class="comment"># 新增menu</span></span><br><span class="line">  <span class="attr">catalogue:</span> <span class="string">目录</span></span><br></pre></td></tr></table></figure>

<h4 id="设定菜单项的文件目录和对应图标"><a href="#设定菜单项的文件目录和对应图标" class="headerlink" title="设定菜单项的文件目录和对应图标"></a>设定菜单项的文件目录和对应图标</h4><p>打开 <code>themes/next/_config.yml</code> 文件，搜索 <code>menu</code> 关键字，修改对应图标名称或者新增对应 <code>menu</code> 的图标。各个菜单项通过 # 注释开启或关闭。</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">menu:</span></span><br><span class="line">  <span class="attr">home:</span> <span class="string">/</span> <span class="string">||</span> <span class="string">home</span></span><br><span class="line">  <span class="attr">about:</span> <span class="string">/about/</span> <span class="string">||</span> <span class="string">user</span></span><br><span class="line">  <span class="attr">tags:</span> <span class="string">/tags/</span> <span class="string">||</span> <span class="string">tags</span></span><br><span class="line">  <span class="attr">categories:</span> <span class="string">/categories/</span> <span class="string">||</span> <span class="string">th</span></span><br><span class="line">  <span class="attr">archives:</span> <span class="string">/archives/</span> <span class="string">||</span> <span class="string">archive</span></span><br><span class="line">  <span class="comment">#schedule: /schedule/ || calendar</span></span><br><span class="line">  <span class="comment">#sitemap: /sitemap.xml || sitemap</span></span><br><span class="line">  <span class="comment">#commonweal: /404/ || heartbeat</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Enable / Disable menu icons / item badges.</span></span><br><span class="line"><span class="attr">menu_settings:</span></span><br><span class="line">  <span class="attr">icons:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">badges:</span> <span class="literal">false</span></span><br></pre></td></tr></table></figure>

<p>除了 <code>home</code>， <code>archives</code> , 后面都需要手动创建这个页面</p>
<h4 id="创建菜单项对应文件目录"><a href="#创建菜单项对应文件目录" class="headerlink" title="创建菜单项对应文件目录"></a>创建菜单项对应文件目录</h4><p>设定好上面后，我们<code>hexo s</code>启动本地服务器预览，就可以看到网页上出现了对应的文件目录。更改目录的位置即可调整顺序。比如我习惯的：</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">menu:</span></span><br><span class="line">  <span class="attr">home:</span> <span class="string">/</span> <span class="string">||</span> <span class="string">home</span></span><br><span class="line">  <span class="attr">categories:</span> <span class="string">/categories/</span> <span class="string">||</span> <span class="string">th</span></span><br><span class="line">  <span class="attr">tags:</span> <span class="string">/tags/</span> <span class="string">||</span> <span class="string">tags</span></span><br><span class="line">  <span class="attr">archives:</span> <span class="string">/archives/</span> <span class="string">||</span> <span class="string">archive</span></span><br><span class="line">  <span class="attr">about:</span> <span class="string">/about/</span> <span class="string">||</span> <span class="string">user</span></span><br><span class="line">  <span class="comment">#schedule: /schedule/ || calendar</span></span><br><span class="line">  <span class="comment">#sitemap: /sitemap.xml || sitemap</span></span><br><span class="line">  <span class="comment">#commonweal: /404/ || heartbeat</span></span><br></pre></td></tr></table></figure>

<p>网页显示：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320153920.png" alt="image-20200320102717504"></p>
<p>但是，此时我们还没有创建对应的页面，比如点击分类，会报错：<code>Cannot GET /categories/</code></p>
<p>下面我们就创建上面对应的页面：</p>
<p>在终端窗口下，定位到 <code>Hexo</code> <strong>站点目录</strong>下。使用 <code>hexo new page</code> 新建页面:</p>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">#分类</span></span><br><span class="line">hexo new page <span class="string">&quot;categories&quot;</span></span><br><span class="line"><span class="comment">#标签</span></span><br><span class="line">hexo new page <span class="string">&quot;tags&quot;</span></span><br><span class="line"><span class="comment">#关于</span></span><br><span class="line">hexo new page <span class="string">&quot;about&quot;</span></span><br></pre></td></tr></table></figure>

<p>这时候，<strong>站点根目录</strong><code>source</code>文件夹下面新增一个<code>categories</code>文件夹，打开里面的<code>index.md</code>文件。在<strong>文档头部</strong>添加以下描述,其他页面类似。</p>
<figure class="highlight markdown"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line">---</span><br><span class="line">&lt;!-- 页面的名字，可以更改为你自己喜欢的名字 --&gt;</span><br><span class="line">title: categories </span><br><span class="line">date: 2020-03-20 10:32:05</span><br><span class="line">type: &quot;categories&quot;</span><br><span class="line">&lt;!-- 评论功能默认关闭 --&gt;</span><br><span class="line"><span class="section">comments: false</span></span><br><span class="line"><span class="section">---</span></span><br></pre></td></tr></table></figure>

<h3 id="设置头像"><a href="#设置头像" class="headerlink" title="设置头像"></a>设置头像</h3><p>打开<code>主题配置文件</code>，搜索关键字<code>Sidebar Avatar</code> 进行定位，将 <code>avatar</code> 的值设置成头像图片的链接地址即可。</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Sidebar Avatar</span></span><br><span class="line"><span class="attr">avatar:</span></span><br><span class="line">  <span class="comment"># Replace the default image and set the url here.</span></span><br><span class="line">  <span class="attr">url:</span> <span class="comment">#/images/avatar.gif</span></span><br><span class="line">  <span class="comment"># 修改头像边框为圆形</span></span><br><span class="line">  <span class="attr">rounded:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># 鼠标放置头像时，头像旋转特效</span></span><br><span class="line">  <span class="attr">rotated:</span> <span class="literal">true</span></span><br></pre></td></tr></table></figure>

<p>头像图片的完整连接地址可以是：</p>
<ul>
<li>完整的互联网地址：例如，<a target="_blank" rel="noopener external nofollow noreferrer" href="https://www.example.com/avatar.jpg">https://www.example.com/avatar.jpg</a></li>
<li>站点内的相对地址：例如，假设图片命名为 avatar.jpg，存放在 source/images/ 目录下，则链接地址可以写成 /images/avatar.jpg</li>
</ul>
<h3 id="侧边栏设置"><a href="#侧边栏设置" class="headerlink" title="侧边栏设置"></a>侧边栏设置</h3><p>默认情况下，侧栏仅在文章页面（拥有目录列表）时才显示，并放置于右侧位置。 可以通过修改 <code>主题配置文件</code> 中的 <code>sidebar</code> 字段来控制侧栏的行为。侧栏的设置包括两个部分，其一是侧栏的位置， 其二是侧栏显示的时机。</p>
<h4 id="设置侧边栏的位置"><a href="#设置侧边栏的位置" class="headerlink" title="设置侧边栏的位置"></a>设置侧边栏的位置</h4><p>修改 <code>sidebar.position</code> 的值，支持的选项有：</p>
<ul>
<li>left - 靠左放置</li>
<li>right - 靠右放置</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">sidebar:</span></span><br><span class="line">  <span class="attr">position:</span> <span class="string">left</span></span><br></pre></td></tr></table></figure>

<h4 id="设置侧边栏显示的时机"><a href="#设置侧边栏显示的时机" class="headerlink" title="设置侧边栏显示的时机"></a>设置侧边栏显示的时机</h4><p>修改 <code>sidebar.display</code> 的值，支持的选项有：</p>
<ul>
<li><p><code>post</code> - 默认行为，在文章页面（拥有目录列表）时显示</p>
</li>
<li><p><code>always</code> - 在所有页面中都显示</p>
</li>
<li><p><code>hide</code> - 在所有页面中都隐藏（可以手动展开）</p>
</li>
<li><p><code>remove</code> - 完全移除</p>
</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">sidebar:</span></span><br><span class="line">  <span class="attr">display:</span> <span class="string">post</span></span><br></pre></td></tr></table></figure>

<h4 id="设置侧边栏社交链接"><a href="#设置侧边栏社交链接" class="headerlink" title="设置侧边栏社交链接"></a>设置侧边栏社交链接</h4><p>打开 <code>主题配置文件</code>，搜索关键字<code>social</code>，然后添加社交站点名称与地址即可。</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># ---------------------------------------------------------------</span></span><br><span class="line"><span class="comment"># Sidebar Settings</span></span><br><span class="line"><span class="comment"># ---------------------------------------------------------------</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Social Links.</span></span><br><span class="line"><span class="comment"># Usage: `Key: permalink || icon`</span></span><br><span class="line"><span class="comment"># Key is the link label showing to end users.</span></span><br><span class="line"><span class="comment"># Value before `||` delimeter is the target permalink.</span></span><br><span class="line"><span class="comment"># Value after `||` delimeter is the name of FontAwesome icon. If icon (with or without delimeter) is not specified, globe icon will be loaded.</span></span><br><span class="line"><span class="attr">social:</span></span><br><span class="line">  <span class="attr">GitHub:</span> <span class="string">https://github.com/yourname</span> <span class="string">||</span> <span class="string">github</span></span><br><span class="line">  <span class="attr">E-Mail:</span> <span class="string">http://mail.com</span> <span class="string">||</span> <span class="string">envelope</span></span><br><span class="line">  <span class="comment">#Weibo: https://weibo.com/yourname || weibo</span></span><br><span class="line">  <span class="comment">#Google: https://plus.google.com/yourname || google</span></span><br><span class="line">  <span class="comment">#Twitter: https://twitter.com/yourname || twitter</span></span><br><span class="line">  <span class="comment">#FB Page: https://www.facebook.com/yourname || facebook</span></span><br><span class="line">  <span class="comment">#StackOverflow: https://stackoverflow.com/yourname || stack-overflow</span></span><br><span class="line">  <span class="comment">#YouTube: https://youtube.com/yourname || youtube</span></span><br><span class="line">  <span class="comment">#Instagram: https://instagram.com/yourname || instagram</span></span><br><span class="line">  <span class="comment">#Skype: skype:yourname?call|chat || skype</span></span><br><span class="line">  <span class="comment"># 等等</span></span><br></pre></td></tr></table></figure>

<p>如果要自定义图标或者新增链接，搜索关键字<code>social_icons</code> ，添加社交站点名称（注意大小写）图标，<a target="_blank" rel="noopener external nofollow noreferrer" href="http://fontawesome.dashgame.com/">Font Awesome</a>图标地。</p>
<h4 id="友情链接"><a href="#友情链接" class="headerlink" title="友情链接"></a>友情链接</h4><p>打开 <code>主题配置文件</code> ，搜索关键字<code>blog rolls</code> :</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Blog rolls</span></span><br><span class="line"><span class="attr">links_settings:</span></span><br><span class="line">	<span class="comment">#图标样式</span></span><br><span class="line">  <span class="attr">icon:</span> <span class="string">link</span></span><br><span class="line">  <span class="comment">#链接名称</span></span><br><span class="line">  <span class="attr">title:</span> <span class="string">Links</span></span><br><span class="line">  <span class="comment"># Available values: block | inline 布局</span></span><br><span class="line">  <span class="attr">layout:</span> <span class="string">block</span></span><br><span class="line"><span class="comment">#链接</span></span><br><span class="line"><span class="attr">links:</span></span><br><span class="line">  <span class="comment">#Title: http://yoursite.com</span></span><br></pre></td></tr></table></figure>

<h3 id="提醒版本更新"><a href="#提醒版本更新" class="headerlink" title="提醒版本更新"></a>提醒版本更新</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Console reminder if new version released.</span></span><br><span class="line"><span class="attr">reminder:</span> <span class="literal">true</span> <span class="comment"># 提醒版本更新</span></span><br></pre></td></tr></table></figure>

<h3 id="缓存"><a href="#缓存" class="headerlink" title="缓存"></a>缓存</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Allow to cache content generation.</span></span><br><span class="line"><span class="attr">cache:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span> <span class="comment"># 缓存</span></span><br></pre></td></tr></table></figure>

<h3 id="自动清除无用文件"><a href="#自动清除无用文件" class="headerlink" title="自动清除无用文件"></a>自动清除无用文件</h3><figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"># Remove unnecessary files after hexo generate.</span><br><span class="line">minify: true    # 压缩</span><br></pre></td></tr></table></figure>

<h3 id="不蒜子统计"><a href="#不蒜子统计" class="headerlink" title="不蒜子统计"></a>不蒜子统计</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Show Views / Visitors of the website / page with busuanzi.</span></span><br><span class="line"><span class="comment"># Get more information on http://ibruce.info/2015/04/04/busuanzi</span></span><br><span class="line"><span class="comment">#文章阅读数。站点访问数和点击数。</span></span><br><span class="line"><span class="attr">busuanzi_count:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">total_visitors:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">total_visitors_icon:</span> <span class="string">user</span></span><br><span class="line">  <span class="attr">total_views:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">total_views_icon:</span> <span class="string">eye</span></span><br><span class="line">  <span class="attr">post_views:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">post_views_icon:</span> <span class="string">eye</span></span><br></pre></td></tr></table></figure>

<h3 id="valine-评论"><a href="#valine-评论" class="headerlink" title="valine 评论"></a>valine 评论</h3><h4 id="获取-APP-id-和-APP-key"><a href="#获取-APP-id-和-APP-key" class="headerlink" title="获取 APP id 和 APP key"></a>获取 APP id 和 APP key</h4><ul>
<li>你可以点击 <a target="_blank" rel="noopener external nofollow noreferrer" href="https://leancloud.cn/">LeanCloud</a> ，注册登录，进入控制台后点击创建应用。</li>
<li>进入刚刚创建的应用，选择<code>设置</code> 》<code>应用Keys</code>，就能看到你的 APP ID 和 APP Key</li>
</ul>
<h4 id="修改主题配置文件"><a href="#修改主题配置文件" class="headerlink" title="修改主题配置文件"></a>修改<code>主题配置文件</code></h4><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Valine</span></span><br><span class="line"><span class="comment"># You can get your appid and appkey from https://leancloud.cn</span></span><br><span class="line"><span class="comment"># For more information: https://valine.js.org, https://github.com/xCss/Valine</span></span><br><span class="line"><span class="comment"># 配置项详情请查阅官方文档。</span></span><br><span class="line"><span class="attr">valine:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span> <span class="comment"># 开启评论功能</span></span><br><span class="line">  <span class="attr">appid:</span> <span class="comment"># 填入刚刚获取的APP ID</span></span><br><span class="line">  <span class="attr">appkey:</span> <span class="comment"># 填入刚刚获取的APP key</span></span><br><span class="line">  <span class="attr">notify:</span> <span class="literal">false</span> <span class="comment"># 邮件通知默认关闭</span></span><br><span class="line">  <span class="attr">verify:</span> <span class="literal">false</span> <span class="comment"># 验证码默认关闭</span></span><br><span class="line">  <span class="attr">placeholder:</span> <span class="string">在这里写下你的评论吧！</span> <span class="comment"># 评论框默认文字</span></span><br><span class="line">  <span class="attr">avatar:</span> <span class="string">mm</span> <span class="comment"># 头像风格</span></span><br><span class="line">  <span class="attr">guest_info:</span> <span class="string">nick,mail</span> <span class="comment">#,link # Custom comment header</span></span><br><span class="line">  <span class="attr">pageSize:</span> <span class="number">10</span> <span class="comment"># Pagination size</span></span><br><span class="line">  <span class="attr">language:</span> <span class="comment"># Language, available values: en, zh-cn</span></span><br><span class="line">  <span class="attr">visitor:</span> <span class="literal">false</span> <span class="comment"># leancloud-counter-security is not supported for now. When visitor is set to be true, appid and appkey are recommended to be the same as leancloud_visitors&#x27; for counter compatibility. Article reading statistic https://valine.js.org/visitor.html</span></span><br><span class="line">  <span class="attr">comment_count:</span> <span class="literal">false</span> <span class="comment"># If false, comment count will only be displayed in post page, not in home page</span></span><br><span class="line">  <span class="attr">recordIP:</span> <span class="literal">false</span> <span class="comment"># 是否记录IP</span></span><br><span class="line">  <span class="attr">serverURLs:</span> <span class="comment"># When the custom domain name is enabled, fill it in here (it will be detected automatically by default, no need to fill in)</span></span><br><span class="line">  <span class="comment">#post_meta_order: 0</span></span><br></pre></td></tr></table></figure>

<p>配置参考：<a target="_blank" rel="noopener external nofollow noreferrer" href="https://valine.js.org/configuration.html">https://valine.js.org/configuration.html</a></p>
<h3 id="定制"><a href="#定制" class="headerlink" title="定制"></a>定制</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Define custom file paths.</span></span><br><span class="line"><span class="comment"># Create your custom files in site directory `source/_data` and uncomment needed files below.</span></span><br><span class="line"><span class="comment"># 路径为根目录下的source，新建一个_data文件夹，不是主题下的source，去掉#即生效</span></span><br><span class="line"><span class="attr">custom_file_path:</span></span><br><span class="line">  <span class="comment">#head: source/_data/head.swig</span></span><br><span class="line">  <span class="comment">#header: source/_data/header.swig</span></span><br><span class="line">  <span class="comment">#sidebar: source/_data/sidebar.swig</span></span><br><span class="line">  <span class="comment">#postMeta: source/_data/post-meta.swig</span></span><br><span class="line">  <span class="attr">postBodyEnd:</span> <span class="string">source/_data/post-body-end.swig</span> <span class="comment"># 本文结束放这里</span></span><br><span class="line">  <span class="attr">footer:</span> <span class="string">source/_data/footer.swig</span> <span class="comment"># js 代码放这里</span></span><br><span class="line">  <span class="comment">#bodyEnd: source/_data/body-end.swig</span></span><br><span class="line">  <span class="comment">#variable: source/_data/variables.styl</span></span><br><span class="line">  <span class="comment">#mixin: source/_data/mixins.styl</span></span><br><span class="line">  <span class="attr">style:</span> <span class="string">source/_data/styles.styl</span> <span class="comment"># css 代码放这里</span></span><br></pre></td></tr></table></figure>

<h4 id="修改-valine-及不蒜子样式"><a href="#修改-valine-及不蒜子样式" class="headerlink" title="修改 valine 及不蒜子样式"></a>修改 valine 及不蒜子样式</h4><p><code>source/_data/styles.styl</code> 文件内容：</p>
<figure class="highlight stylus"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 隐藏 valine 的 powered by</span></span><br><span class="line"><span class="selector-class">.power</span><span class="selector-class">.txt-right</span> &#123;</span><br><span class="line">	<span class="attribute">display</span>: none;</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="comment">// 修改不蒜子数据颜色</span></span><br><span class="line"><span class="selector-id">#busuanzi_value_site_pv</span>,#busuanzi_value_site_uv&#123;</span><br><span class="line">  <span class="attribute">color</span>: <span class="number">#00BFFF</span>;</span><br><span class="line">&#125;</span><br><span class="line"></span><br><span class="line"><span class="comment">// valine 评论框对齐文章</span></span><br><span class="line">div<span class="selector-id">#comments</span><span class="selector-class">.comments</span>.v&#123;</span><br><span class="line">  <span class="attribute">margin-left</span>: <span class="number">0px</span> <span class="meta">!important</span>;</span><br><span class="line">  <span class="attribute">margin-right</span>: <span class="number">0px</span> <span class="meta">!important</span>;</span><br><span class="line">  <span class="attribute">border</span>: <span class="number">0px</span>;</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<h4 id="个性网页标题"><a href="#个性网页标题" class="headerlink" title="个性网页标题"></a>个性网页标题</h4><p>新建<code>source/_data/footer.swig</code> 文件，添加内容：</p>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br></pre></td><td class="code"><pre><span class="line">&#123;# 搞怪网页标题 #&#125;</span><br><span class="line">&#123;<span class="operator">%</span> <span class="keyword">if</span> theme.title_trick.enable <span class="operator">%</span>&#125;</span><br><span class="line">  <span class="operator">&lt;</span>script<span class="operator">&gt;</span></span><br><span class="line">    <span class="keyword">var</span> <span class="type">OriginTitle</span> <span class="operator">=</span> document.title;</span><br><span class="line">    <span class="keyword">var</span> titleTime;</span><br><span class="line">    document.addEventListener(&#x27;visibilitychange&#x27;, function() &#123;</span><br><span class="line">      <span class="keyword">if</span> (document.hidden) &#123;</span><br><span class="line">        document.title <span class="operator">=</span> &#x27;&#123;&#123; theme.title_trick.leave &#125;&#125;&#x27; <span class="operator">+</span> <span class="type">OriginTitle</span>;</span><br><span class="line">        clearTimeout(titleTime);</span><br><span class="line">      &#125; <span class="keyword">else</span> &#123;</span><br><span class="line">        document.title <span class="operator">=</span> &#x27;&#123;&#123; theme.title_trick.enter &#125;&#125;&#x27; <span class="operator">+</span> <span class="type">OriginTitle</span>;</span><br><span class="line">        titleTime <span class="operator">=</span> setTimeout(function() &#123;</span><br><span class="line">          document.title <span class="operator">=</span> <span class="type">OriginTitle</span>;</span><br><span class="line">        &#125;, <span class="number">2000</span>);</span><br><span class="line">      &#125;</span><br><span class="line">    &#125;);</span><br><span class="line">  <span class="operator">&lt;/</span>script<span class="operator">&gt;</span></span><br><span class="line">&#123;<span class="operator">%</span> endif <span class="operator">%</span>&#125;</span><br></pre></td></tr></table></figure>

<p>在<code>主题配置文件</code>添加以下内容：</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># 搞怪网站标题</span></span><br><span class="line"><span class="attr">title_trick:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">leave:</span> <span class="string">&#x27;(*^▽^*)我藏好了哦~&#x27;</span></span><br><span class="line">  <span class="attr">enter:</span> <span class="string">&#x27;q(≧▽≦q)被你发现啦~&#x27;</span></span><br></pre></td></tr></table></figure>

<h4 id="文章末尾统一添加本文结束分割线"><a href="#文章末尾统一添加本文结束分割线" class="headerlink" title="文章末尾统一添加本文结束分割线"></a>文章末尾统一添加本文结束分割线</h4><p>新建 <code>source/_data/post-body-end.swig</code> 文件，添加内容：</p>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">&#123;# 本文结束 #&#125;</span><br><span class="line"><span class="operator">&lt;</span>div<span class="operator">&gt;</span></span><br><span class="line">    &#123;<span class="operator">%</span> <span class="keyword">if</span> not is_index <span class="operator">%</span>&#125;</span><br><span class="line">        <span class="operator">&lt;</span>div style<span class="operator">=</span><span class="string">&quot;text-align:center;color: #ccc;font-size:14px;&quot;</span><span class="operator">&gt;-------------</span>　　　　本文结束　<span class="operator">&lt;</span>i <span class="keyword">class</span>=&quot;<span class="title class_">fa</span> <span class="title class_">fa</span>-<span class="title class_">flag</span>&quot;&gt;&lt;/<span class="title class_">i</span>&gt;　感谢阅读　　　　-------------&lt;/<span class="title class_">div</span>&gt;</span><br><span class="line">    &#123;<span class="operator">%</span> endif <span class="operator">%</span>&#125;</span><br><span class="line"><span class="operator">&lt;/</span>div<span class="operator">&gt;</span></span><br></pre></td></tr></table></figure>

<h3 id="修改底部用户图标为跳动的心"><a href="#修改底部用户图标为跳动的心" class="headerlink" title="修改底部用户图标为跳动的心"></a>修改底部用户图标为跳动的心</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Icon between year and copyright info.</span></span><br><span class="line"><span class="attr">icon:</span></span><br><span class="line">  <span class="comment"># Icon name in Font Awesome. See: https://fontawesome.com/v4.7.0/icons/</span></span><br><span class="line">  <span class="comment"># `heart` is recommended with animation in red (#ff0000).</span></span><br><span class="line">  <span class="attr">name:</span> <span class="string">heart</span></span><br><span class="line">  <span class="comment"># If you want to animate the icon, set it to true.</span></span><br><span class="line">  <span class="attr">animated:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># Change the color of icon, using Hex Code.</span></span><br><span class="line">  <span class="attr">color:</span> <span class="string">&#x27;#ff5999&#x27;</span></span><br></pre></td></tr></table></figure>

<h3 id="打开文章末尾的版权信息"><a href="#打开文章末尾的版权信息" class="headerlink" title="打开文章末尾的版权信息"></a>打开文章末尾的版权信息</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Creative Commons 4.0 International License.</span></span><br><span class="line"><span class="comment"># See: https://creativecommons.org/share-your-work/licensing-types-examples</span></span><br><span class="line"><span class="comment"># Available values of license: by | by-nc | by-nc-nd | by-nc-sa | by-nd | by-sa | zero</span></span><br><span class="line"><span class="comment"># You can set a language value if you prefer a translated version of CC license, e.g. deed.zh</span></span><br><span class="line"><span class="comment"># CC licenses are available in 39 languages, you can find the specific and correct abbreviation you need on https://creativecommons.org</span></span><br><span class="line"><span class="attr">creative_commons:</span></span><br><span class="line">  <span class="attr">license:</span> <span class="string">by-nc-sa</span></span><br><span class="line">  <span class="attr">sidebar:</span> <span class="literal">false</span></span><br><span class="line">  <span class="attr">post:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">language:</span></span><br></pre></td></tr></table></figure>

<h3 id="菜单栏显示分类-标签中的文章数目"><a href="#菜单栏显示分类-标签中的文章数目" class="headerlink" title="菜单栏显示分类/标签中的文章数目"></a>菜单栏显示分类/标签中的文章数目</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">menu_settings:</span></span><br><span class="line">  <span class="attr">badges:</span> <span class="literal">true</span> <span class="comment"># 显示菜单分类的数目</span></span><br></pre></td></tr></table></figure>

<h3 id="文章目录的设置"><a href="#文章目录的设置" class="headerlink" title="文章目录的设置"></a>文章目录的设置</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Table of Contents in the Sidebar</span></span><br><span class="line"><span class="attr">toc:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># Automatically add list number to toc.</span></span><br><span class="line">  <span class="attr">number:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># If true, all words will placed on next lines if header width longer then sidebar width.</span></span><br><span class="line">  <span class="attr">wrap:</span> <span class="literal">false</span></span><br><span class="line">  <span class="comment"># If true, all level of TOC in a post will be displayed, rather than the activated part of it.</span></span><br><span class="line">  <span class="attr">expand_all:</span> <span class="literal">true</span> <span class="comment"># 目录全展开</span></span><br><span class="line">  <span class="comment"># Maximum heading depth of generated toc. You can set it in one post through `toc_max_depth` in Front-matter.</span></span><br><span class="line">  <span class="attr">max_depth:</span> <span class="number">3</span> <span class="comment"># 目录展开的标题级数</span></span><br></pre></td></tr></table></figure>

<h3 id="字数统计与阅读时长"><a href="#字数统计与阅读时长" class="headerlink" title="字数统计与阅读时长"></a>字数统计与阅读时长</h3><ul>
<li>在终端中输入命令安装插件</li>
</ul>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install hexo<span class="literal">-symbols-count-time</span></span><br></pre></td></tr></table></figure>

<ul>
<li>在 <code>站点配置文件</code> 添加：</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">symbols_count_time:</span></span><br><span class="line">  <span class="comment">#文章内是否显示</span></span><br><span class="line">  <span class="attr">symbols:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">time:</span> <span class="literal">false</span></span><br><span class="line">  <span class="comment"># 网页底部是否显示</span></span><br><span class="line">  <span class="attr">total_symbols:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">total_time:</span> <span class="literal">false</span></span><br><span class="line">  <span class="attr">exclude_codeblock:</span> <span class="literal">false</span></span><br><span class="line">  <span class="attr">awl:</span> <span class="number">4</span></span><br><span class="line">  <span class="attr">wpm:</span> <span class="number">275</span></span><br><span class="line">  <span class="attr">suffix:</span> <span class="string">&#x27;mins.&#x27;</span></span><br></pre></td></tr></table></figure>

<ul>
<li>修改 <code>主题配置文件</code></li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">symbols_count_time:</span></span><br><span class="line">  <span class="attr">separated_meta:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">item_text_post:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">item_text_total:</span> <span class="literal">false</span></span><br></pre></td></tr></table></figure>

<h3 id="修改文章底部标签的样式"><a href="#修改文章底部标签的样式" class="headerlink" title="修改文章底部标签的样式"></a>修改文章底部标签的样式</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Use icon instead of the symbol # to indicate the tag at the bottom of the post</span></span><br><span class="line"><span class="comment"># 标签代替#</span></span><br><span class="line"><span class="attr">tag_icon:</span> <span class="literal">true</span></span><br></pre></td></tr></table></figure>

<h3 id="SEO-优化"><a href="#SEO-优化" class="headerlink" title="SEO 优化"></a>SEO 优化</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># ---------------------------------------------------------------</span></span><br><span class="line"><span class="comment"># SEO Settings</span></span><br><span class="line"><span class="comment"># ---------------------------------------------------------------</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Disable Baidu transformation on mobile devices.</span></span><br><span class="line"><span class="attr">disable_baidu_transformation:</span> <span class="literal">false</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Set a canonical link tag in your hexo, you could use it for your SEO of blog.</span></span><br><span class="line"><span class="comment"># See: https://support.google.com/webmasters/answer/139066</span></span><br><span class="line"><span class="comment"># Remember to set up your URL in Hexo `_config.yml` (e.g. url: http://yourdomain.com)</span></span><br><span class="line"><span class="attr">canonical:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Change headers hierarchy on site-subtitle (will be main site description) and on all post / page titles for better SEO-optimization.</span></span><br><span class="line"><span class="attr">seo:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># If true, will add site-subtitle to index page.</span></span><br><span class="line"><span class="comment"># Remember to set up your site-subtitle in Hexo `_config.yml` (e.g. subtitle: Subtitle)</span></span><br><span class="line"><span class="attr">index_with_subtitle:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Automatically add external URL with Base64 encrypt &amp; decrypt.</span></span><br><span class="line"><span class="attr">exturl:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Google Webmaster tools verification.</span></span><br><span class="line"><span class="comment"># See: https://www.google.com/webmasters</span></span><br><span class="line"><span class="attr">google_site_verification:</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Bing Webmaster tools verification.</span></span><br><span class="line"><span class="comment"># See: https://www.bing.com/webmaster</span></span><br><span class="line"><span class="attr">bing_site_verification:</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Yandex Webmaster tools verification.</span></span><br><span class="line"><span class="comment"># See: https://webmaster.yandex.ru</span></span><br><span class="line"><span class="attr">yandex_site_verification:</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Baidu Webmaster tools verification.</span></span><br><span class="line"><span class="comment"># See: https://ziyuan.baidu.com/site</span></span><br><span class="line"><span class="attr">baidu_site_verification:</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Enable baidu push so that the blog will push the url to baidu automatically which is very helpful for SEO.</span></span><br><span class="line"><span class="attr">baidu_push:</span> <span class="literal">true</span></span><br></pre></td></tr></table></figure>

<h3 id="第三方插件"><a href="#第三方插件" class="headerlink" title="第三方插件"></a>第三方插件</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Math Formulas Render Support</span></span><br><span class="line"><span class="attr">math:</span></span><br><span class="line">  <span class="comment"># Default (true) will load mathjax / katex script on demand.</span></span><br><span class="line">  <span class="comment"># That is it only render those page which has `mathjax: true` in Front-matter.</span></span><br><span class="line">  <span class="comment"># If you set it to false, it will load mathjax / katex srcipt EVERY PAGE.</span></span><br><span class="line">  <span class="attr">per_page:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line">  <span class="comment"># hexo-renderer-pandoc (or hexo-renderer-kramed) required for full MathJax support.</span></span><br><span class="line">  <span class="attr">mathjax:</span></span><br><span class="line">    <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">    <span class="comment"># See: https://mhchem.github.io/MathJax-mhchem/</span></span><br><span class="line">    <span class="attr">mhchem:</span> <span class="literal">false</span></span><br><span class="line"></span><br><span class="line">  <span class="comment"># hexo-renderer-markdown-it-plus (or hexo-renderer-markdown-it with markdown-it-katex plugin) required for full Katex support.</span></span><br><span class="line">  <span class="attr">katex:</span></span><br><span class="line">    <span class="attr">enable:</span> <span class="literal">false</span></span><br><span class="line">    <span class="comment"># See: https://github.com/KaTeX/KaTeX/tree/master/contrib/copy-tex</span></span><br><span class="line">    <span class="attr">copy_tex:</span> <span class="literal">false</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Easily enable fast Ajax navigation on your website.</span></span><br><span class="line"><span class="comment"># Dependencies: https://github.com/theme-next/theme-next-pjax</span></span><br><span class="line"><span class="comment"># For moreinformation: https://github.com/MoOx/pjax</span></span><br><span class="line"><span class="attr">pjax:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># FancyBox is a tool that offers a nice and elegant way to add zooming functionality for images.</span></span><br><span class="line"><span class="comment"># For more information: https://fancyapps.com/fancybox</span></span><br><span class="line"><span class="comment"># 点击图片放大</span></span><br><span class="line"><span class="attr">fancybox:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># A JavaScript library for zooming images like Medium.</span></span><br><span class="line"><span class="comment"># Do not enable both `fancybox` and `mediumzoom`.</span></span><br><span class="line"><span class="comment"># For more information: https://github.com/francoischalifour/medium-zoom</span></span><br><span class="line"><span class="attr">mediumzoom:</span> <span class="literal">false</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Vanilla JavaScript plugin for lazyloading images.</span></span><br><span class="line"><span class="comment"># For more information: https://github.com/ApoorvSaxena/lozad.js</span></span><br><span class="line"><span class="comment"># 高性能、轻巧和可配置的延迟加载器</span></span><br><span class="line"><span class="attr">lazyload:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Pangu Support</span></span><br><span class="line"><span class="comment"># For more information: https://github.com/vinta/pangu.js</span></span><br><span class="line"><span class="comment"># 神器啊，盘古之白：自动在中文字和半形的英文、数字、符号之间插入空白。</span></span><br><span class="line"><span class="attr">pangu:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line"><span class="comment"># Quicklink Support</span></span><br><span class="line"><span class="comment"># For more information: https://github.com/GoogleChromeLabs/quicklink</span></span><br><span class="line"><span class="comment"># 通过在空闲时间预取in-viewport链接来加快后续页面加载速度。</span></span><br><span class="line"><span class="attr">quicklink:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line">  <span class="comment"># Quicklink (quicklink.umd.js script) is loaded on demand.</span></span><br><span class="line">  <span class="comment"># Add `quicklink: true` in Front-matter of the page or post you need.</span></span><br><span class="line">  <span class="comment"># Home page and archive page can be controlled through home and archive options below.</span></span><br><span class="line">  <span class="attr">home:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">archive:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line">  <span class="comment"># Default (true) will initialize quicklink after the load event fires.</span></span><br><span class="line">  <span class="attr">delay:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># Custom a time in milliseconds by which the browser must execute prefetching.</span></span><br><span class="line">  <span class="attr">timeout:</span> <span class="number">3000</span></span><br><span class="line">  <span class="comment"># Default (true) will enable fetch() or falls back to XHR.</span></span><br><span class="line">  <span class="attr">priority:</span> <span class="literal">true</span></span><br><span class="line"></span><br><span class="line">  <span class="comment"># For more flexibility you can add some patterns (RegExp, Function, or Array) to ignores.</span></span><br><span class="line">  <span class="comment"># See: https://github.com/GoogleChromeLabs/quicklink#custom-ignore-patterns</span></span><br><span class="line">  <span class="attr">ignores:</span></span><br></pre></td></tr></table></figure>

<h3 id="动态线条背景"><a href="#动态线条背景" class="headerlink" title="动态线条背景"></a>动态线条背景</h3><ul>
<li>Next7 内置了 2 个动态背景，但我都觉得不太好看，安装对应插件打开即可。</li>
<li>three:</li>
</ul>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">git clone https://github.com/theme<span class="literal">-next</span>/theme<span class="literal">-next-three</span> source/lib/three</span><br></pre></td></tr></table></figure>

<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># JavaScript 3D library.</span></span><br><span class="line"><span class="comment"># Dependencies: https://github.com/theme-next/theme-next-three</span></span><br><span class="line"><span class="attr">three:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">false</span></span><br><span class="line">  <span class="attr">three_waves:</span> <span class="literal">false</span></span><br><span class="line">  <span class="attr">canvas_lines:</span> <span class="literal">false</span></span><br><span class="line">  <span class="attr">canvas_sphere:</span> <span class="literal">false</span></span><br></pre></td></tr></table></figure>

<ul>
<li>canvas_ribbon</li>
</ul>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">git clone https://github.com/theme<span class="literal">-next</span>/theme<span class="literal">-next-canvas-ribbon</span> source/lib/canvas<span class="literal">-ribbon</span></span><br></pre></td></tr></table></figure>

<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Canvas-ribbon</span></span><br><span class="line"><span class="comment"># Dependencies: https://github.com/theme-next/theme-next-canvas-ribbon</span></span><br><span class="line"><span class="comment"># For more information: https://github.com/zproo/canvas-ribbon</span></span><br><span class="line"><span class="attr">canvas_ribbon:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">false</span></span><br><span class="line">  <span class="attr">size:</span> <span class="number">300</span> <span class="comment"># The width of the ribbon</span></span><br><span class="line">  <span class="attr">alpha:</span> <span class="number">0.6</span> <span class="comment"># The transparency of the ribbon</span></span><br><span class="line">  <span class="attr">zIndex:</span> <span class="number">-1</span> <span class="comment"># The display level of the ribbon</span></span><br></pre></td></tr></table></figure>

<ul>
<li>我使用的 canvas_nest</li>
</ul>
<p>在<code>source\_data\footer.swig</code>文件中添加以下代码即可。</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">&lt;script color=&quot;0,0,255&quot; opacity=&quot;0.5&quot; zIndex=&quot;-1&quot; count=&quot;99&quot; src=&quot;https://cdn.jsdelivr.net/npm/canvas-nest.js@1/dist/canvas-nest.js&quot;&gt;&lt;/script&gt;</span><br></pre></td></tr></table></figure>

<p>如果你没有配置 <strong>第 11 条</strong> ，那一定要记得打开<code>主题配置文件</code> 中的：</p>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">custom_file_path:</span></span><br><span class="line">  <span class="attr">footer:</span> <span class="string">source/_data/footer.swig</span></span><br></pre></td></tr></table></figure>

<h3 id="点击头像跳转到关于页面"><a href="#点击头像跳转到关于页面" class="headerlink" title="点击头像跳转到关于页面"></a>点击头像跳转到关于页面</h3><p>打开 <code>themes\next\layout\_partials\sidebar\site-overview.swig</code> 文件，添加以下的 a 标签。当然这个 about 你也可以改成你想要页面。</p>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line"><span class="operator">&lt;</span>div <span class="keyword">class</span>=&quot;<span class="title class_">site</span>-<span class="title class_">author</span> <span class="title class_">motion</span>-<span class="title class_">element</span>&quot; <span class="title class_">itemprop</span>=&quot;<span class="title class_">author</span>&quot; <span class="title class_">itemscope</span> <span class="title class_">itemtype</span>=&quot;<span class="title class_">http</span>://<span class="title class_">schema</span>.<span class="title class_">org</span>/<span class="title class_">Person</span>&quot;&gt;</span><br><span class="line">  &#123;# 点击头像跳转 #&#125;</span><br><span class="line">  <span class="operator">&lt;</span>a href<span class="operator">=</span><span class="string">&quot;/about&quot;</span><span class="operator">&gt;</span></span><br><span class="line">  <span class="operator">&lt;</span>img <span class="keyword">class</span>=&quot;<span class="title class_">site</span>-<span class="title class_">author</span>-<span class="title class_">image</span>&quot; <span class="title class_">itemprop</span>=&quot;<span class="title class_">image</span>&quot; <span class="title class_">alt</span>=&quot;&#123;&#123; author &#125;&#125;<span class="string">&quot;</span></span><br><span class="line"><span class="string">    src=&quot;</span>&#123;&#123; url_for(theme.avatar.url or theme.images <span class="operator">+</span> &#x27;<span class="operator">/</span>avatar.gif&#x27;) &#125;&#125;<span class="string">&quot;&gt;</span></span><br><span class="line"><span class="string">  &lt;/a&gt;</span></span><br><span class="line"><span class="string">  &lt;p class=&quot;</span>site<span class="operator">-</span>author<span class="operator">-</span>name<span class="string">&quot; itemprop=&quot;</span>name<span class="string">&quot;&gt;&#123;&#123; author &#125;&#125;&lt;/p&gt;</span></span><br><span class="line"><span class="string">  &lt;div class=&quot;</span>site<span class="operator">-</span>description<span class="string">&quot; itemprop=&quot;</span>description<span class="string">&quot;&gt;&#123;&#123; description &#125;&#125;&lt;/div&gt;</span></span><br><span class="line"><span class="string">&lt;/div&gt;</span></span><br></pre></td></tr></table></figure>

<h3 id="点击出现桃心效果"><a href="#点击出现桃心效果" class="headerlink" title="点击出现桃心效果"></a>点击出现桃心效果</h3><p>实现效果图：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200321222340.png" alt="image-20200320121756514"></p>
<p>实现方法：</p>
<ul>
<li><p>创建 JS 文件</p>
<p>在在<code>/themes/next/source/js/src</code>下新建文件<code>clicklove.js</code>，接着把代码拷贝粘贴到<code>clicklove.js</code>文件中。</p>
<figure class="highlight javascript"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br><span class="line">85</span><br><span class="line">86</span><br></pre></td><td class="code"><pre><span class="line">!(<span class="keyword">function</span> (<span class="params">e, t, a</span>) &#123;</span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">n</span>(<span class="params"></span>) &#123;</span><br><span class="line">    <span class="title function_">c</span>(</span><br><span class="line">      <span class="string">&quot;.heart&#123;width: 10px;height: 10px;position: fixed;background: #f00;transform: rotate(45deg);-webkit-transform: rotate(45deg);-moz-transform: rotate(45deg);&#125;.heart:after,.heart:before&#123;content: &#x27;&#x27;;width: inherit;height: inherit;background: inherit;border-radius: 50%;-webkit-border-radius: 50%;-moz-border-radius: 50%;position: fixed;&#125;.heart:after&#123;top: -5px;&#125;.heart:before&#123;left: -5px;&#125;&quot;</span></span><br><span class="line">    ),</span><br><span class="line">      <span class="title function_">o</span>(),</span><br><span class="line">      <span class="title function_">r</span>()</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">r</span>(<span class="params"></span>) &#123;</span><br><span class="line">    <span class="keyword">for</span> (<span class="keyword">var</span> e = <span class="number">0</span>; e &lt; d.<span class="property">length</span>; e++)</span><br><span class="line">      d[e].<span class="property">alpha</span> &lt;= <span class="number">0</span></span><br><span class="line">        ? (t.<span class="property">body</span>.<span class="title function_">removeChild</span>(d[e].<span class="property">el</span>), d.<span class="title function_">splice</span>(e, <span class="number">1</span>))</span><br><span class="line">        : (d[e].<span class="property">y</span>--,</span><br><span class="line">          (d[e].<span class="property">scale</span> += <span class="number">0.004</span>),</span><br><span class="line">          (d[e].<span class="property">alpha</span> -= <span class="number">0.013</span>),</span><br><span class="line">          (d[e].<span class="property">el</span>.<span class="property">style</span>.<span class="property">cssText</span> =</span><br><span class="line">            <span class="string">&#x27;left:&#x27;</span> +</span><br><span class="line">            d[e].<span class="property">x</span> +</span><br><span class="line">            <span class="string">&#x27;px;top:&#x27;</span> +</span><br><span class="line">            d[e].<span class="property">y</span> +</span><br><span class="line">            <span class="string">&#x27;px;opacity:&#x27;</span> +</span><br><span class="line">            d[e].<span class="property">alpha</span> +</span><br><span class="line">            <span class="string">&#x27;;transform:scale(&#x27;</span> +</span><br><span class="line">            d[e].<span class="property">scale</span> +</span><br><span class="line">            <span class="string">&#x27;,&#x27;</span> +</span><br><span class="line">            d[e].<span class="property">scale</span> +</span><br><span class="line">            <span class="string">&#x27;) rotate(45deg);background:&#x27;</span> +</span><br><span class="line">            d[e].<span class="property">color</span> +</span><br><span class="line">            <span class="string">&#x27;;z-index:99999&#x27;</span>))</span><br><span class="line">    <span class="title function_">requestAnimationFrame</span>(r)</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">o</span>(<span class="params"></span>) &#123;</span><br><span class="line">    <span class="keyword">var</span> t = <span class="string">&#x27;function&#x27;</span> == <span class="keyword">typeof</span> e.<span class="property">onclick</span> &amp;&amp; e.<span class="property">onclick</span></span><br><span class="line">    e.<span class="property">onclick</span> = <span class="keyword">function</span> (<span class="params">e</span>) &#123;</span><br><span class="line">      t &amp;&amp; <span class="title function_">t</span>(), <span class="title function_">i</span>(e)</span><br><span class="line">    &#125;</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">i</span>(<span class="params">e</span>) &#123;</span><br><span class="line">    <span class="keyword">var</span> a = t.<span class="title function_">createElement</span>(<span class="string">&#x27;div&#x27;</span>)</span><br><span class="line">    ;(a.<span class="property">className</span> = <span class="string">&#x27;heart&#x27;</span>),</span><br><span class="line">      d.<span class="title function_">push</span>(&#123;</span><br><span class="line">        <span class="attr">el</span>: a,</span><br><span class="line">        <span class="attr">x</span>: e.<span class="property">clientX</span> - <span class="number">5</span>,</span><br><span class="line">        <span class="attr">y</span>: e.<span class="property">clientY</span> - <span class="number">5</span>,</span><br><span class="line">        <span class="attr">scale</span>: <span class="number">1</span>,</span><br><span class="line">        <span class="attr">alpha</span>: <span class="number">1</span>,</span><br><span class="line">        <span class="attr">color</span>: <span class="title function_">s</span>(),</span><br><span class="line">      &#125;),</span><br><span class="line">      t.<span class="property">body</span>.<span class="title function_">appendChild</span>(a)</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">c</span>(<span class="params">e</span>) &#123;</span><br><span class="line">    <span class="keyword">var</span> a = t.<span class="title function_">createElement</span>(<span class="string">&#x27;style&#x27;</span>)</span><br><span class="line">    a.<span class="property">type</span> = <span class="string">&#x27;text/css&#x27;</span></span><br><span class="line">    <span class="keyword">try</span> &#123;</span><br><span class="line">      a.<span class="title function_">appendChild</span>(t.<span class="title function_">createTextNode</span>(e))</span><br><span class="line">    &#125; <span class="keyword">catch</span> (t) &#123;</span><br><span class="line">      a.<span class="property">styleSheet</span>.<span class="property">cssText</span> = e</span><br><span class="line">    &#125;</span><br><span class="line">    t.<span class="title function_">getElementsByTagName</span>(<span class="string">&#x27;head&#x27;</span>)[<span class="number">0</span>].<span class="title function_">appendChild</span>(a)</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">function</span> <span class="title function_">s</span>(<span class="params"></span>) &#123;</span><br><span class="line">    <span class="keyword">return</span> (</span><br><span class="line">      <span class="string">&#x27;rgb(&#x27;</span> +</span><br><span class="line">      ~~(<span class="number">255</span> * <span class="title class_">Math</span>.<span class="title function_">random</span>()) +</span><br><span class="line">      <span class="string">&#x27;,&#x27;</span> +</span><br><span class="line">      ~~(<span class="number">255</span> * <span class="title class_">Math</span>.<span class="title function_">random</span>()) +</span><br><span class="line">      <span class="string">&#x27;,&#x27;</span> +</span><br><span class="line">      ~~(<span class="number">255</span> * <span class="title class_">Math</span>.<span class="title function_">random</span>()) +</span><br><span class="line">      <span class="string">&#x27;)&#x27;</span></span><br><span class="line">    )</span><br><span class="line">  &#125;</span><br><span class="line">  <span class="keyword">var</span> d = []</span><br><span class="line">  ;(e.<span class="property">requestAnimationFrame</span> = (<span class="keyword">function</span> (<span class="params"></span>) &#123;</span><br><span class="line">    <span class="keyword">return</span> (</span><br><span class="line">      e.<span class="property">requestAnimationFrame</span> ||</span><br><span class="line">      e.<span class="property">webkitRequestAnimationFrame</span> ||</span><br><span class="line">      e.<span class="property">mozRequestAnimationFrame</span> ||</span><br><span class="line">      e.<span class="property">oRequestAnimationFrame</span> ||</span><br><span class="line">      e.<span class="property">msRequestAnimationFrame</span> ||</span><br><span class="line">      <span class="keyword">function</span> (<span class="params">e</span>) &#123;</span><br><span class="line">        <span class="built_in">setTimeout</span>(e, <span class="number">1e3</span> / <span class="number">60</span>)</span><br><span class="line">      &#125;</span><br><span class="line">    )</span><br><span class="line">  &#125;)()),</span><br><span class="line">    <span class="title function_">n</span>()</span><br><span class="line">&#125;)(<span class="variable language_">window</span>, <span class="variable language_">document</span>)</span><br></pre></td></tr></table></figure>
</li>
<li><p>修改<code>themes/next/layout/_layout.swig</code>文件，在末尾添加</p>
</li>
</ul>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br></pre></td><td class="code"><pre><span class="line"><span class="operator">&lt;!--</span> 页面点击小红心 <span class="operator">--&gt;</span></span><br><span class="line"><span class="operator">&lt;</span>script type<span class="operator">=</span><span class="string">&quot;text/javascript&quot;</span> src<span class="operator">=</span><span class="string">&quot;/js/src/clicklove.js&quot;</span><span class="operator">&gt;&lt;/</span>script<span class="operator">&gt;</span></span><br></pre></td></tr></table></figure>

<h3 id="页面底部主题信息删除"><a href="#页面底部主题信息删除" class="headerlink" title="页面底部主题信息删除"></a>页面底部主题信息删除</h3><ul>
<li>打开<code>主题配置文件</code> ，修改<code>footer</code>下面的这些设置为<code>false</code>即可。</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># If not defined, `author` from Hexo `_config.yml` will be used.</span></span><br><span class="line"><span class="attr">copyright:</span></span><br><span class="line"><span class="attr">powered:</span></span><br><span class="line">  <span class="comment"># Hexo link (Powered by Hexo).</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">false</span></span><br><span class="line">  <span class="comment"># Version info of Hexo after Hexo link (vX.X.X).</span></span><br><span class="line">  <span class="attr">version:</span> <span class="literal">true</span></span><br><span class="line"><span class="attr">theme:</span></span><br><span class="line">  <span class="comment"># Theme &amp; scheme info link (Theme - NexT.scheme).</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">false</span></span><br><span class="line">  <span class="comment"># Version info of NexT after scheme info (vX.X.X).</span></span><br><span class="line">  <span class="attr">version:</span> <span class="literal">true</span></span><br></pre></td></tr></table></figure>

<h3 id="修改网站的图标-Favicon"><a href="#修改网站的图标-Favicon" class="headerlink" title="修改网站的图标 Favicon"></a>修改网站的图标 Favicon</h3><p>实现效果</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320152532.png" alt="image-20200320152531334"></p>
<p>实现方法：</p>
<ul>
<li>在<a href="https://link.zhihu.com/?target=http://www.easyicon.net/" rel="external nofollow noreferrer">EasyIcon</a>中找一张（32*32）的 ico 图标,或者去别的网站下载或者制作，并将图标名称改为 favicon.ico，然后把图标放在/themes/next/source/images 里，并且修改 <code>主题配置文件</code>：</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">favicon:</span></span><br><span class="line">  <span class="attr">small:</span> <span class="string">/images/favicon.ico</span></span><br><span class="line">  <span class="attr">medium:</span> <span class="string">/images/favicon.ico</span></span><br><span class="line">  <span class="attr">apple_touch_icon:</span> <span class="string">/images/favicon.ico</span></span><br><span class="line">  <span class="attr">safari_pinned_tab:</span> <span class="string">/images/favicon.ico</span></span><br><span class="line">  <span class="comment">#android_manifest: /images/manifest.json</span></span><br><span class="line">  <span class="comment">#ms_browserconfig: /images/browserconfig.xml</span></span><br></pre></td></tr></table></figure>

<h3 id="显示当前浏览进度"><a href="#显示当前浏览进度" class="headerlink" title="显示当前浏览进度"></a>显示当前浏览进度</h3><p>实现效果：</p>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320171112.png" alt="image-20200320171110759"></p>
<p>实现方法：</p>
<ul>
<li>修改<code>主题配置文件</code>，把 <code>false</code> 改为 <code>true</code>：</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">back2top:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># Back to top in sidebar.</span></span><br><span class="line">  <span class="attr">sidebar:</span> <span class="literal">false</span></span><br><span class="line">  <span class="comment"># Scroll percent label in b2t button.</span></span><br><span class="line">  <span class="attr">scrollpercent:</span> <span class="literal">true</span></span><br></pre></td></tr></table></figure>

<h3 id="添加在线联系功能"><a href="#添加在线联系功能" class="headerlink" title="添加在线联系功能"></a>添加在线联系功能</h3><p>实现方法：</p>
<ul>
<li>一个在线的联系功能:<a target="_blank" rel="noopener external nofollow noreferrer" href="http://www.daovoice.io/">DaoVoice</a></li>
<li>点击 <a target="_blank" rel="noopener external nofollow noreferrer" href="http://www.daovoice.io/">注册</a> ,获取 app_id</li>
</ul>
<p><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200320172237.png" alt="image-20200320172235334"></p>
<ul>
<li>修改<code>next\layout_partials\head\head.swig </code>文件, 添加以下内容</li>
</ul>
<figure class="highlight swift"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br></pre></td><td class="code"><pre><span class="line">&#123;<span class="operator">%</span> <span class="keyword">if</span> theme.daovoice <span class="operator">%</span>&#125;</span><br><span class="line">  <span class="operator">&lt;</span>script<span class="operator">&gt;</span></span><br><span class="line">  (function(i,s,o,g,r,a,m)&#123;i[<span class="string">&quot;DaoVoiceObject&quot;</span>]<span class="operator">=</span>r;i[r]<span class="operator">=</span>i[r]<span class="operator">||</span>function()&#123;(i[r].q<span class="operator">=</span>i[r].q<span class="operator">||</span>[]).push(arguments)&#125;,i[r].l<span class="operator">=</span><span class="number">1</span><span class="operator">*</span>new <span class="type">Date</span>();a<span class="operator">=</span>s.createElement(o),m<span class="operator">=</span>s.getElementsByTagName(o)[<span class="number">0</span>];a.async<span class="operator">=</span><span class="number">1</span>;a.src<span class="operator">=</span>g;a.charset<span class="operator">=</span><span class="string">&quot;utf-8&quot;</span>;m.parentNode.insertBefore(a,m)&#125;)(window,document,<span class="string">&quot;script&quot;</span>,(&#x27;https:&#x27; <span class="operator">==</span> document.location.protocol <span class="operator">?</span> &#x27;https:&#x27; : &#x27;http:&#x27;) <span class="operator">+</span> <span class="string">&quot;//widget.daovoice.io/widget/0f81ff2f.js&quot;</span>,<span class="string">&quot;daovoice&quot;</span>)</span><br><span class="line">  daovoice(&#x27;<span class="keyword">init</span>&#x27;, &#123;</span><br><span class="line">      app_id: <span class="string">&quot;&#123;&#123;theme.daovoice_app_id&#125;&#125;&quot;</span></span><br><span class="line">    &#125;);</span><br><span class="line">  daovoice(&#x27;update&#x27;);</span><br><span class="line">  <span class="operator">&lt;/</span>script<span class="operator">&gt;</span></span><br><span class="line">&#123;<span class="operator">%</span> endif <span class="operator">%</span>&#125;</span><br></pre></td></tr></table></figure>

<ul>
<li>修改<code>主题配置文件</code>，添加：</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># daovoice 配置</span></span><br><span class="line"><span class="attr">daovoice:</span> <span class="literal">true</span></span><br><span class="line"><span class="attr">daovoice_app_id:</span> <span class="comment"># 填入刚才的app_id</span></span><br></pre></td></tr></table></figure>

<ul>
<li><p>部署到网站：</p>
<p><code>hexo clean</code> &amp;&amp; <code>hexo g</code> &amp;&amp; <code>hexo d </code>就能看到效果了，网页右下角多了个 图标。具体修改图表的样式，位置。可以在 daovoice 网页中 应用设置-聊天设置 中配置。</p>
</li>
</ul>
<h3 id="添加文章书写样式"><a href="#添加文章书写样式" class="headerlink" title="添加文章书写样式"></a>添加文章书写样式</h3><h4 id="文字增加背景色块"><a href="#文字增加背景色块" class="headerlink" title="文字增加背景色块"></a><del>文字增加背景色块</del></h4><p><del>在<code>source\_data\styles.styl</code>文件中添加属性样式（参考<strong>第 11 条</strong>）：</del><br>2020.3.25 提醒：其实可以直接使用 next 主题支持的 label 方法。</p>
<figure class="highlight plaintext"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">&#123;% label danger @ XXXX %&#125;</span><br></pre></td></tr></table></figure>

<figure class="highlight stylus"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br><span class="line">15</span><br><span class="line">16</span><br><span class="line">17</span><br><span class="line">18</span><br><span class="line">19</span><br><span class="line">20</span><br><span class="line">21</span><br><span class="line">22</span><br><span class="line">23</span><br><span class="line">24</span><br><span class="line">25</span><br><span class="line">26</span><br><span class="line">27</span><br><span class="line">28</span><br><span class="line">29</span><br><span class="line">30</span><br><span class="line">31</span><br><span class="line">32</span><br><span class="line">33</span><br><span class="line">34</span><br><span class="line">35</span><br><span class="line">36</span><br><span class="line">37</span><br><span class="line">38</span><br><span class="line">39</span><br><span class="line">40</span><br><span class="line">41</span><br><span class="line">42</span><br><span class="line">43</span><br><span class="line">44</span><br><span class="line">45</span><br><span class="line">46</span><br><span class="line">47</span><br><span class="line">48</span><br><span class="line">49</span><br><span class="line">50</span><br><span class="line">51</span><br><span class="line">52</span><br><span class="line">53</span><br><span class="line">54</span><br><span class="line">55</span><br><span class="line">56</span><br><span class="line">57</span><br><span class="line">58</span><br><span class="line">59</span><br><span class="line">60</span><br><span class="line">61</span><br><span class="line">62</span><br><span class="line">63</span><br><span class="line">64</span><br><span class="line">65</span><br><span class="line">66</span><br><span class="line">67</span><br><span class="line">68</span><br><span class="line">69</span><br><span class="line">70</span><br><span class="line">71</span><br><span class="line">72</span><br><span class="line">73</span><br><span class="line">74</span><br><span class="line">75</span><br><span class="line">76</span><br><span class="line">77</span><br><span class="line">78</span><br><span class="line">79</span><br><span class="line">80</span><br><span class="line">81</span><br><span class="line">82</span><br><span class="line">83</span><br><span class="line">84</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment">// 颜色块-黄</span></span><br><span class="line">span<span class="selector-id">#inline-yellow</span> &#123;</span><br><span class="line"><span class="attribute">display</span>:inline;</span><br><span class="line"><span class="attribute">padding</span>:.<span class="number">2em</span> .<span class="number">6em</span> .<span class="number">3em</span>;</span><br><span class="line"><span class="attribute">font-size</span>:<span class="number">80%</span>;</span><br><span class="line"><span class="attribute">font-weight</span>:bold;</span><br><span class="line"><span class="attribute">line-height</span>:<span class="number">1</span>;</span><br><span class="line"><span class="attribute">color</span>:<span class="number">#fff</span>;</span><br><span class="line"><span class="attribute">text-align</span>:center;</span><br><span class="line"><span class="attribute">white-space</span>:nowrap;</span><br><span class="line"><span class="attribute">vertical-align</span>:baseline;</span><br><span class="line"><span class="attribute">border-radius</span>:<span class="number">0</span>;</span><br><span class="line"><span class="attribute">background-color</span>: <span class="number">#f0ad4e</span>;</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 颜色块-绿</span></span><br><span class="line">span<span class="selector-id">#inline-green</span> &#123;</span><br><span class="line"><span class="attribute">display</span>:inline;</span><br><span class="line"><span class="attribute">padding</span>:.<span class="number">2em</span> .<span class="number">6em</span> .<span class="number">3em</span>;</span><br><span class="line"><span class="attribute">font-size</span>:<span class="number">80%</span>;</span><br><span class="line"><span class="attribute">font-weight</span>:bold;</span><br><span class="line"><span class="attribute">line-height</span>:<span class="number">1</span>;</span><br><span class="line"><span class="attribute">color</span>:<span class="number">#fff</span>;</span><br><span class="line"><span class="attribute">text-align</span>:center;</span><br><span class="line"><span class="attribute">white-space</span>:nowrap;</span><br><span class="line"><span class="attribute">vertical-align</span>:baseline;</span><br><span class="line"><span class="attribute">border-radius</span>:<span class="number">0</span>;</span><br><span class="line"><span class="attribute">background-color</span>: <span class="number">#5cb85c</span>;</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 颜色块-蓝</span></span><br><span class="line">span<span class="selector-id">#inline-blue</span> &#123;</span><br><span class="line"><span class="attribute">display</span>:inline;</span><br><span class="line"><span class="attribute">padding</span>:.<span class="number">2em</span> .<span class="number">6em</span> .<span class="number">3em</span>;</span><br><span class="line"><span class="attribute">font-size</span>:<span class="number">80%</span>;</span><br><span class="line"><span class="attribute">font-weight</span>:bold;</span><br><span class="line"><span class="attribute">line-height</span>:<span class="number">1</span>;</span><br><span class="line"><span class="attribute">color</span>:<span class="number">#fff</span>;</span><br><span class="line"><span class="attribute">text-align</span>:center;</span><br><span class="line"><span class="attribute">white-space</span>:nowrap;</span><br><span class="line"><span class="attribute">vertical-align</span>:baseline;</span><br><span class="line"><span class="attribute">border-radius</span>:<span class="number">0</span>;</span><br><span class="line"><span class="attribute">background-color</span>: <span class="number">#2780e3</span>;</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 颜色块-紫</span></span><br><span class="line">span<span class="selector-id">#inline-purple</span> &#123;</span><br><span class="line"><span class="attribute">display</span>:inline;</span><br><span class="line"><span class="attribute">padding</span>:.<span class="number">2em</span> .<span class="number">6em</span> .<span class="number">3em</span>;</span><br><span class="line"><span class="attribute">font-size</span>:<span class="number">80%</span>;</span><br><span class="line"><span class="attribute">font-weight</span>:bold;</span><br><span class="line"><span class="attribute">line-height</span>:<span class="number">1</span>;</span><br><span class="line"><span class="attribute">color</span>:<span class="number">#fff</span>;</span><br><span class="line"><span class="attribute">text-align</span>:center;</span><br><span class="line"><span class="attribute">white-space</span>:nowrap;</span><br><span class="line"><span class="attribute">vertical-align</span>:baseline;</span><br><span class="line"><span class="attribute">border-radius</span>:<span class="number">0</span>;</span><br><span class="line"><span class="attribute">background-color</span>: <span class="number">#9954bb</span>;</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 颜色块-橙</span></span><br><span class="line">span<span class="selector-id">#inline-geraldine</span> &#123;</span><br><span class="line"><span class="attribute">display</span>:inline;</span><br><span class="line"><span class="attribute">padding</span>:.<span class="number">2em</span> .<span class="number">6em</span> .<span class="number">3em</span>;</span><br><span class="line"><span class="attribute">font-size</span>:<span class="number">80%</span>;</span><br><span class="line"><span class="attribute">font-weight</span>:bold;</span><br><span class="line"><span class="attribute">line-height</span>:<span class="number">1</span>;</span><br><span class="line"><span class="attribute">color</span>:<span class="number">#fff</span>;</span><br><span class="line"><span class="attribute">text-align</span>:center;</span><br><span class="line"><span class="attribute">white-space</span>:nowrap;</span><br><span class="line"><span class="attribute">vertical-align</span>:baseline;</span><br><span class="line"><span class="attribute">border-radius</span>:<span class="number">0</span>;</span><br><span class="line"><span class="attribute">background-color</span>: <span class="number">#f78a7a</span>;</span><br><span class="line">&#125;</span><br><span class="line"><span class="comment">// 颜色块-红</span></span><br><span class="line">span<span class="selector-id">#inline-roman</span> &#123;</span><br><span class="line"><span class="attribute">display</span>:inline;</span><br><span class="line"><span class="attribute">padding</span>:.<span class="number">2em</span> .<span class="number">6em</span> .<span class="number">3em</span>;</span><br><span class="line"><span class="attribute">font-size</span>:<span class="number">80%</span>;</span><br><span class="line"><span class="attribute">font-weight</span>:bold;</span><br><span class="line"><span class="attribute">line-height</span>:<span class="number">1</span>;</span><br><span class="line"><span class="attribute">color</span>:<span class="number">#fff</span>;</span><br><span class="line"><span class="attribute">text-align</span>:center;</span><br><span class="line"><span class="attribute">white-space</span>:nowrap;</span><br><span class="line"><span class="attribute">vertical-align</span>:baseline;</span><br><span class="line"><span class="attribute">border-radius</span>:<span class="number">0</span>;</span><br><span class="line"><span class="attribute">background-color</span>: <span class="number">#de5c6b</span>;</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p>在你需要编辑的地方，使用如下代码：</p>
<figure class="highlight html"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line">`站点配置文件`</span><br><span class="line"><span class="tag">&lt;<span class="name">span</span> <span class="attr">id</span>=<span class="string">&quot;inline-purple&quot;</span>&gt;</span> `主题配置文件` <span class="tag">&lt;/<span class="name">span</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;<span class="name">span</span> <span class="attr">id</span>=<span class="string">&quot;inline-yellow&quot;</span>&gt;</span> 站点配置文件 <span class="tag">&lt;/<span class="name">span</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;<span class="name">span</span> <span class="attr">id</span>=<span class="string">&quot;inline-green&quot;</span>&gt;</span> `主题配置文件` <span class="tag">&lt;/<span class="name">span</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;<span class="name">span</span> <span class="attr">id</span>=<span class="string">&quot;inline-geraldine&quot;</span>&gt;</span> `主题配置文件` <span class="tag">&lt;/<span class="name">span</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;<span class="name">span</span> <span class="attr">id</span>=<span class="string">&quot;inline-roman&quot;</span>&gt;</span> `主题配置文件` <span class="tag">&lt;/<span class="name">span</span>&gt;</span></span><br></pre></td></tr></table></figure>

<h4 id="下载样式"><a href="#下载样式" class="headerlink" title="下载样式"></a>下载样式</h4><p>在<code>source\_data\styles.styl</code>文件中添加属性样式（参考<strong>第 11 条</strong>）：</p>
<figure class="highlight stylus"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br><span class="line">13</span><br><span class="line">14</span><br></pre></td><td class="code"><pre><span class="line">a<span class="selector-id">#download</span> &#123;</span><br><span class="line"><span class="attribute">display</span>: inline-block;</span><br><span class="line"><span class="attribute">padding</span>: <span class="number">0</span> <span class="number">10px</span>;</span><br><span class="line"><span class="attribute">color</span>: <span class="number">#000</span>;</span><br><span class="line"><span class="attribute">background</span>: transparent;</span><br><span class="line"><span class="attribute">border</span>: <span class="number">2px</span> solid <span class="number">#000</span>;</span><br><span class="line"><span class="attribute">border-radius</span>: <span class="number">2px</span>;</span><br><span class="line"><span class="attribute">transition</span>: all .<span class="number">5s</span> ease;</span><br><span class="line"><span class="attribute">font-weight</span>: bold;</span><br><span class="line"><span class="selector-pseudo">&amp;:hover</span> &#123;</span><br><span class="line"><span class="attribute">background</span>: <span class="number">#000</span>;</span><br><span class="line"><span class="attribute">color</span>: <span class="number">#fff</span>;</span><br><span class="line">&#125;</span><br><span class="line">&#125;</span><br></pre></td></tr></table></figure>

<p>在你需要编辑的文章地方。放置如下代码：</p>
<figure class="highlight html"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">a</span> <span class="attr">id</span>=<span class="string">&quot;download&quot;</span> <span class="attr">href</span>=<span class="string">&quot;https://git-scm.com/download/win&quot;</span></span></span><br><span class="line"><span class="tag">  &gt;</span><span class="tag">&lt;<span class="name">i</span> <span class="attr">class</span>=<span class="string">&quot;fa fa-download&quot;</span>&gt;</span><span class="tag">&lt;/<span class="name">i</span>&gt;</span><span class="tag">&lt;<span class="name">span</span>&gt;</span> Download Now<span class="tag">&lt;/<span class="name">span</span>&gt;</span></span><br><span class="line"><span class="tag">&lt;/<span class="name">a</span>&gt;</span></span><br></pre></td></tr></table></figure>

<h4 id="在文章中增加小图标"><a href="#在文章中增加小图标" class="headerlink" title="在文章中增加小图标"></a>在文章中增加小图标</h4><p>next 支持<a target="_blank" rel="noopener external nofollow noreferrer" href="http://fontawesome.dashgame.com/">Font Awesome</a> 提供图标，你可以选择你喜欢的图标，这样使用即可：</p>
<figure class="highlight html"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line"><span class="tag">&lt;<span class="name">i</span> <span class="attr">class</span>=<span class="string">&quot;fa fa-pencil&quot;</span>&gt;</span><span class="tag">&lt;/<span class="name">i</span>&gt;</span>支持Markdown</span><br></pre></td></tr></table></figure>

<h3 id="添加本地搜索"><a href="#添加本地搜索" class="headerlink" title="添加本地搜索"></a>添加本地搜索</h3><ul>
<li>搜索功能需要依赖 <code>hexo-generator-searchdb</code> 插件，我们先安装插件：</li>
</ul>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">npm install hexo<span class="literal">-generator-searchdb</span> <span class="literal">--save</span></span><br></pre></td></tr></table></figure>

<ul>
<li>打开<code>主题配置文件</code> ，搜索<code>local_search</code>，修改 <code>enable</code> 为 <code>true</code>。</li>
<li>修改<code>站点配置文件</code>，添加代码：</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">search:</span></span><br><span class="line">  <span class="attr">path:</span> <span class="string">search.xml</span></span><br><span class="line">  <span class="attr">field:</span> <span class="string">post</span></span><br><span class="line">  <span class="attr">format:</span> <span class="string">html</span></span><br><span class="line">  <span class="attr">limit:</span> <span class="number">10000</span></span><br></pre></td></tr></table></figure>

<h3 id="右上角增加-fork-me-on-GitHub-图标"><a href="#右上角增加-fork-me-on-GitHub-图标" class="headerlink" title="右上角增加 fork me on GitHub 图标"></a>右上角增加 fork me on GitHub 图标</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">github_banner:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">permalink:</span> <span class="string">https://github.com/yourname</span></span><br><span class="line">  <span class="attr">title:</span> <span class="string">Follow</span> <span class="string">me</span> <span class="string">on</span> <span class="string">GitHub</span></span><br></pre></td></tr></table></figure>

<h3 id="打开代码块的复制功能"><a href="#打开代码块的复制功能" class="headerlink" title="打开代码块的复制功能"></a>打开代码块的复制功能</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br><span class="line">9</span><br><span class="line">10</span><br><span class="line">11</span><br><span class="line">12</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">codeblock:</span></span><br><span class="line">  <span class="comment"># Code Highlight theme</span></span><br><span class="line">  <span class="comment"># Available values: normal | night | night eighties | night blue | night bright | solarized | solarized dark | galactic</span></span><br><span class="line">  <span class="comment"># See: https://github.com/chriskempson/tomorrow-theme</span></span><br><span class="line">  <span class="attr">highlight_theme:</span> <span class="string">normal</span></span><br><span class="line">  <span class="comment"># Add copy button on codeblock</span></span><br><span class="line">  <span class="attr">copy_button:</span></span><br><span class="line">    <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">    <span class="comment"># Show text copy result.</span></span><br><span class="line">    <span class="attr">show_result:</span> <span class="literal">true</span></span><br><span class="line">    <span class="comment"># Available values: default | flat | mac</span></span><br><span class="line">    <span class="attr">style:</span></span><br></pre></td></tr></table></figure>

<h3 id="页面加载进度效果"><a href="#页面加载进度效果" class="headerlink" title="页面加载进度效果"></a>页面加载进度效果</h3><ul>
<li>安装插件</li>
</ul>
<figure class="highlight powershell"><table><tr><td class="gutter"><pre><span class="line">1</span><br></pre></td><td class="code"><pre><span class="line">git clone https://github.com/theme<span class="literal">-next</span>/theme<span class="literal">-next-pace</span> source/lib/pace</span><br></pre></td></tr></table></figure>

<ul>
<li>编辑<code>主题配置文件</code>：</li>
</ul>
<figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br></pre></td><td class="code"><pre><span class="line"><span class="attr">pace:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># Themes list:可选样式列表</span></span><br><span class="line">  <span class="comment"># big-counter | bounce | barber-shop | center-atom | center-circle | center-radar | center-simple</span></span><br><span class="line">  <span class="comment"># corner-indicator | fill-left | flat-top | flash | loading-bar | mac-osx | material | minimal</span></span><br><span class="line">  <span class="attr">theme:</span> <span class="string">minimal</span></span><br></pre></td></tr></table></figure>

<h3 id="阅读进度条"><a href="#阅读进度条" class="headerlink" title="阅读进度条"></a>阅读进度条</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Reading progress bar</span></span><br><span class="line"><span class="attr">reading_progress:</span></span><br><span class="line">  <span class="attr">enable:</span> <span class="literal">true</span></span><br><span class="line">  <span class="comment"># Available values: top | bottom</span></span><br><span class="line">  <span class="attr">position:</span> <span class="string">top</span></span><br><span class="line">  <span class="attr">color:</span> <span class="string">&#x27;#37c6c0&#x27;</span></span><br><span class="line">  <span class="attr">height:</span> <span class="string">3px</span></span><br></pre></td></tr></table></figure>

<h3 id="文章创建时间和更新时间"><a href="#文章创建时间和更新时间" class="headerlink" title="文章创建时间和更新时间"></a>文章创建时间和更新时间</h3><figure class="highlight yaml"><table><tr><td class="gutter"><pre><span class="line">1</span><br><span class="line">2</span><br><span class="line">3</span><br><span class="line">4</span><br><span class="line">5</span><br><span class="line">6</span><br><span class="line">7</span><br><span class="line">8</span><br></pre></td><td class="code"><pre><span class="line"><span class="comment"># Post meta display settings</span></span><br><span class="line"><span class="attr">post_meta:</span></span><br><span class="line">  <span class="attr">item_text:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">created_at:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">updated_at:</span></span><br><span class="line">    <span class="attr">enable:</span> <span class="literal">false</span></span><br><span class="line">    <span class="attr">another_day:</span> <span class="literal">true</span></span><br><span class="line">  <span class="attr">categories:</span> <span class="literal">true</span></span><br></pre></td></tr></table></figure>

<h2 id="结语"><a href="#结语" class="headerlink" title="结语"></a>结语</h2><p>很多内容参考了很多优秀博文，并根据自己实际情况进行了自己的博客进行了修改，感谢前辈们的珍贵经验，参考文章：</p>
<p><a target="_blank" rel="noopener external nofollow noreferrer" href="http://theme-next.iissnan.com/getting-started.html">Next 使用文档</a></p>
<p><a target="_blank" rel="noopener external nofollow noreferrer" href="https://zhuanlan.zhihu.com/p/28128674">hexo 的 next 主题个性化教程:打造炫酷网站</a></p>
<p><a target="_blank" rel="noopener external nofollow noreferrer" href="https://www.jianshu.com/p/e17711e44e00">Hexo 使用攻略-添加分类及标签</a></p>
<p><a target="_blank" rel="noopener external nofollow noreferrer" href="https://www.jianshu.com/p/efbeddc5eb19">Hexo+Next 个人博客主题优化</a></p>
<p><a target="_blank" rel="noopener external nofollow noreferrer" href="http://qiujiaqi.cn/hexo/theme-optimization/">Hexo 博客 NexT 主题定制与优化</a></p>
</article><div class="post-copyright"><div class="post-copyright__author"><span class="post-copyright-meta">文章作者: </span><span class="post-copyright-info"><a href="mailto:undefined" rel="external nofollow noreferrer">ConstOwn</a></span></div><div class="post-copyright__type"><span class="post-copyright-meta">文章链接: </span><span class="post-copyright-info"><a href="http://blog.mhy.loc.cc/archives/264a3045.html">http://blog.mhy.loc.cc/archives/264a3045.html</a></span></div><div class="post-copyright__notice"><span class="post-copyright-meta">版权声明: </span><span class="post-copyright-info">本博客所有文章除特别声明外，均采用 <a href="https://creativecommons.org/licenses/by-nc-sa/4.0/" rel="external nofollow noreferrer" target="_blank">CC BY-NC-SA 4.0</a> 许可协议。转载请注明来自 <a href="http://blog.mhy.loc.cc" target="_blank">你真是一个美好的人类</a>！</span></div></div><div class="tag_share"><div class="post-meta__tag-list"><a class="post-meta__tags" href="/tags/%E5%8D%9A%E5%AE%A2%E6%90%AD%E5%BB%BA/">博客搭建</a><a class="post-meta__tags" href="/tags/Hexo/">Hexo</a><a class="post-meta__tags" href="/tags/Next%E4%B8%BB%E9%A2%98/">Next主题</a><a class="post-meta__tags" href="/tags/Hexo%E7%BE%8E%E5%8C%96/">Hexo美化</a></div><div class="post_share"><div class="social-share" data-image="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" data-sites="facebook,twitter,wechat,weibo,qq"></div><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/social-share.js/dist/css/share.min.css" media="print" onload="this.media='all'"><script src="https://cdn.jsdelivr.net/npm/social-share.js/dist/js/social-share.min.js" defer></script></div></div><nav class="pagination-post" id="pagination"><div class="prev-post pull-left"><a href="/archives/540169c9.html"><img class="prev-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of previous post"><div class="pagination-info"><div class="label">上一篇</div><div class="prev_info">Hexo框架(四)：Next主题文章置顶和公告效果</div></div></a></div><div class="next-post pull-right"><a href="/archives/a2423b27.html"><img class="next-cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" onerror="onerror=null;src='/img/404.jpg'" alt="cover of next post"><div class="pagination-info"><div class="label">下一篇</div><div class="next_info">Hexo框架(二)：配置文件详解与常用命令</div></div></a></div></nav><div class="relatedPosts"><div class="headline"><i class="fas fa-thumbs-up fa-fw"></i><span>相关推荐</span></div><div class="relatedPosts-list"><div><a href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2022-02-10</div><div class="title">使用GitHub Actions 实现自动化部署和部署到服务器</div></div></a></div><div><a href="/archives/353666f0.html" title="给你的博客添加一个收藏页"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200719133158.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-07-19</div><div class="title">给你的博客添加一个收藏页</div></div></a></div><div><a href="/archives/f36eea83.html" title="Hexo博客添加emoji表情支持"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">Hexo博客添加emoji表情支持</div></div></a></div><div><a href="/archives/2798a2b1.html" title="给文章标题添加一个emoji表情"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718183828.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-20</div><div class="title">给文章标题添加一个emoji表情</div></div></a></div><div><a href="/archives/f06684a1.html" title="NexT版本更新V8.0记录"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-19</div><div class="title">NexT版本更新V8.0记录</div></div></a></div><div><a href="/archives/b1e33f9d.html" title="sitemeta渐变背景实现"><img class="cover" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200718184146.png" alt="cover"><div class="content is-center"><div class="date"><i class="far fa-calendar-alt fa-fw"></i> 2020-05-03</div><div class="title">sitemeta渐变背景实现</div></div></a></div></div></div></div><div class="aside-content" id="aside-content"><div class="card-widget card-info"><div class="is-center"><div class="avatar-img"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/avatar.png" onerror="this.onerror=null;this.src='/img/friend_404.gif'" alt="avatar"/></div><div class="author-info__name">ConstOwn</div><div class="author-info__description">能和你一起成长，我荣幸之至。</div></div><div class="card-info-data"><div class="card-info-data-item is-center"><a href="/archives/"><div class="headline">文章</div><div class="length-num">113</div></a></div><div class="card-info-data-item is-center"><a href="/tags/"><div class="headline">标签</div><div class="length-num">82</div></a></div><div class="card-info-data-item is-center"><a href="/categories/"><div class="headline">分类</div><div class="length-num">29</div></a></div></div><a class="button--animated" id="card-info-btn" target="_blank" rel="noopener external nofollow noreferrer" href="https://github.com/constown"><i class="fab fa-github"></i><span>Follow Me</span></a><div class="card-info-social-icons is-center"><a class="social-icon" href="https://github.com/constown" rel="external nofollow noreferrer" target="_blank" title="Github"><i class="fab fa-github"></i></a><a class="social-icon" href="http://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=mvn19Onu9e302vz14vf78-a0_fX3" rel="external nofollow noreferrer" target="_blank" title="Email"><i class="fas fa-envelope"></i></a><a class="social-icon" href="https://juanertu.com" rel="external nofollow noreferrer" target="_blank" title="我的首页"><i class="fas fa-laptop-code"></i></a><a class="social-icon" href="http://wpa.qq.com/msgrd?v=3&amp;uin=912300601&amp;site=qq&amp;menu=yes" rel="external nofollow noreferrer" target="_blank" title="发送QQ消息"><i class="fab fa-qq"></i></a></div></div><div class="card-widget card-announcement"><div class="item-headline"><i class="fas fa-bullhorn card-announcement-animation"></i><span>公告</span></div><div class="announcement_content">暂时不接受友链申请了，如果需要联系我，请不要在博客留言，很少看到，请直接通过上方QQ给我发送临时消息！。</div></div><div class="sticky_layout"><div class="card-widget" id="card-toc"><div class="item-headline"><i class="fas fa-stream"></i><span>目录</span></div><div class="toc-content"><ol class="toc"><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%89%8D%E8%A8%80"><span class="toc-text">前言</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E5%AE%89%E8%A3%85-Next-%E4%B8%BB%E9%A2%98"><span class="toc-text">安装 Next 主题</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%B8%8B%E8%BD%BD-Next"><span class="toc-text">下载 Next</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%90%AF%E7%94%A8%E4%B8%BB%E9%A2%98"><span class="toc-text">启用主题</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%AA%8C%E8%AF%81%E4%B8%BB%E9%A2%98"><span class="toc-text">验证主题</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#Hexo-%E5%9F%BA%E7%A1%80%E9%85%8D%E7%BD%AE"><span class="toc-text">Hexo 基础配置</span></a></li><li class="toc-item toc-level-2"><a class="toc-link" href="#Next-%E4%B8%BB%E9%A2%98%E8%AE%BE%E7%BD%AE"><span class="toc-text">Next 主题设置</span></a><ol class="toc-child"><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%80%89%E6%8B%A9-Scheme"><span class="toc-text">选择 Scheme</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%AE%BE%E7%BD%AE%E8%AF%AD%E8%A8%80"><span class="toc-text">设置语言</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%AE%BE%E7%BD%AE%E8%8F%9C%E5%8D%95"><span class="toc-text">设置菜单</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%AE%BE%E7%BD%AE%E8%8F%9C%E5%8D%95%E9%A1%B9%E7%9A%84%E6%98%BE%E7%A4%BA%E4%B8%AD%E6%96%87%E6%96%87%E6%9C%AC"><span class="toc-text">设置菜单项的显示中文文本</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%AE%BE%E5%AE%9A%E8%8F%9C%E5%8D%95%E9%A1%B9%E7%9A%84%E6%96%87%E4%BB%B6%E7%9B%AE%E5%BD%95%E5%92%8C%E5%AF%B9%E5%BA%94%E5%9B%BE%E6%A0%87"><span class="toc-text">设定菜单项的文件目录和对应图标</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%88%9B%E5%BB%BA%E8%8F%9C%E5%8D%95%E9%A1%B9%E5%AF%B9%E5%BA%94%E6%96%87%E4%BB%B6%E7%9B%AE%E5%BD%95"><span class="toc-text">创建菜单项对应文件目录</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%AE%BE%E7%BD%AE%E5%A4%B4%E5%83%8F"><span class="toc-text">设置头像</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BE%A7%E8%BE%B9%E6%A0%8F%E8%AE%BE%E7%BD%AE"><span class="toc-text">侧边栏设置</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%AE%BE%E7%BD%AE%E4%BE%A7%E8%BE%B9%E6%A0%8F%E7%9A%84%E4%BD%8D%E7%BD%AE"><span class="toc-text">设置侧边栏的位置</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%AE%BE%E7%BD%AE%E4%BE%A7%E8%BE%B9%E6%A0%8F%E6%98%BE%E7%A4%BA%E7%9A%84%E6%97%B6%E6%9C%BA"><span class="toc-text">设置侧边栏显示的时机</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%AE%BE%E7%BD%AE%E4%BE%A7%E8%BE%B9%E6%A0%8F%E7%A4%BE%E4%BA%A4%E9%93%BE%E6%8E%A5"><span class="toc-text">设置侧边栏社交链接</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%8F%8B%E6%83%85%E9%93%BE%E6%8E%A5"><span class="toc-text">友情链接</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%8F%90%E9%86%92%E7%89%88%E6%9C%AC%E6%9B%B4%E6%96%B0"><span class="toc-text">提醒版本更新</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%BC%93%E5%AD%98"><span class="toc-text">缓存</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%87%AA%E5%8A%A8%E6%B8%85%E9%99%A4%E6%97%A0%E7%94%A8%E6%96%87%E4%BB%B6"><span class="toc-text">自动清除无用文件</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%B8%8D%E8%92%9C%E5%AD%90%E7%BB%9F%E8%AE%A1"><span class="toc-text">不蒜子统计</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#valine-%E8%AF%84%E8%AE%BA"><span class="toc-text">valine 评论</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E8%8E%B7%E5%8F%96-APP-id-%E5%92%8C-APP-key"><span class="toc-text">获取 APP id 和 APP key</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%BF%AE%E6%94%B9%E4%B8%BB%E9%A2%98%E9%85%8D%E7%BD%AE%E6%96%87%E4%BB%B6"><span class="toc-text">修改主题配置文件</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AE%9A%E5%88%B6"><span class="toc-text">定制</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%BF%AE%E6%94%B9-valine-%E5%8F%8A%E4%B8%8D%E8%92%9C%E5%AD%90%E6%A0%B7%E5%BC%8F"><span class="toc-text">修改 valine 及不蒜子样式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%B8%AA%E6%80%A7%E7%BD%91%E9%A1%B5%E6%A0%87%E9%A2%98"><span class="toc-text">个性网页标题</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%96%87%E7%AB%A0%E6%9C%AB%E5%B0%BE%E7%BB%9F%E4%B8%80%E6%B7%BB%E5%8A%A0%E6%9C%AC%E6%96%87%E7%BB%93%E6%9D%9F%E5%88%86%E5%89%B2%E7%BA%BF"><span class="toc-text">文章末尾统一添加本文结束分割线</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BF%AE%E6%94%B9%E5%BA%95%E9%83%A8%E7%94%A8%E6%88%B7%E5%9B%BE%E6%A0%87%E4%B8%BA%E8%B7%B3%E5%8A%A8%E7%9A%84%E5%BF%83"><span class="toc-text">修改底部用户图标为跳动的心</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%89%93%E5%BC%80%E6%96%87%E7%AB%A0%E6%9C%AB%E5%B0%BE%E7%9A%84%E7%89%88%E6%9D%83%E4%BF%A1%E6%81%AF"><span class="toc-text">打开文章末尾的版权信息</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E8%8F%9C%E5%8D%95%E6%A0%8F%E6%98%BE%E7%A4%BA%E5%88%86%E7%B1%BB-%E6%A0%87%E7%AD%BE%E4%B8%AD%E7%9A%84%E6%96%87%E7%AB%A0%E6%95%B0%E7%9B%AE"><span class="toc-text">菜单栏显示分类&#x2F;标签中的文章数目</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%96%87%E7%AB%A0%E7%9B%AE%E5%BD%95%E7%9A%84%E8%AE%BE%E7%BD%AE"><span class="toc-text">文章目录的设置</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%AD%97%E6%95%B0%E7%BB%9F%E8%AE%A1%E4%B8%8E%E9%98%85%E8%AF%BB%E6%97%B6%E9%95%BF"><span class="toc-text">字数统计与阅读时长</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BF%AE%E6%94%B9%E6%96%87%E7%AB%A0%E5%BA%95%E9%83%A8%E6%A0%87%E7%AD%BE%E7%9A%84%E6%A0%B7%E5%BC%8F"><span class="toc-text">修改文章底部标签的样式</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#SEO-%E4%BC%98%E5%8C%96"><span class="toc-text">SEO 优化</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%AC%AC%E4%B8%89%E6%96%B9%E6%8F%92%E4%BB%B6"><span class="toc-text">第三方插件</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%8A%A8%E6%80%81%E7%BA%BF%E6%9D%A1%E8%83%8C%E6%99%AF"><span class="toc-text">动态线条背景</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%82%B9%E5%87%BB%E5%A4%B4%E5%83%8F%E8%B7%B3%E8%BD%AC%E5%88%B0%E5%85%B3%E4%BA%8E%E9%A1%B5%E9%9D%A2"><span class="toc-text">点击头像跳转到关于页面</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E7%82%B9%E5%87%BB%E5%87%BA%E7%8E%B0%E6%A1%83%E5%BF%83%E6%95%88%E6%9E%9C"><span class="toc-text">点击出现桃心效果</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%A1%B5%E9%9D%A2%E5%BA%95%E9%83%A8%E4%B8%BB%E9%A2%98%E4%BF%A1%E6%81%AF%E5%88%A0%E9%99%A4"><span class="toc-text">页面底部主题信息删除</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E4%BF%AE%E6%94%B9%E7%BD%91%E7%AB%99%E7%9A%84%E5%9B%BE%E6%A0%87-Favicon"><span class="toc-text">修改网站的图标 Favicon</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%98%BE%E7%A4%BA%E5%BD%93%E5%89%8D%E6%B5%8F%E8%A7%88%E8%BF%9B%E5%BA%A6"><span class="toc-text">显示当前浏览进度</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%B7%BB%E5%8A%A0%E5%9C%A8%E7%BA%BF%E8%81%94%E7%B3%BB%E5%8A%9F%E8%83%BD"><span class="toc-text">添加在线联系功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%B7%BB%E5%8A%A0%E6%96%87%E7%AB%A0%E4%B9%A6%E5%86%99%E6%A0%B7%E5%BC%8F"><span class="toc-text">添加文章书写样式</span></a><ol class="toc-child"><li class="toc-item toc-level-4"><a class="toc-link" href="#%E6%96%87%E5%AD%97%E5%A2%9E%E5%8A%A0%E8%83%8C%E6%99%AF%E8%89%B2%E5%9D%97"><span class="toc-text">文字增加背景色块</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E4%B8%8B%E8%BD%BD%E6%A0%B7%E5%BC%8F"><span class="toc-text">下载样式</span></a></li><li class="toc-item toc-level-4"><a class="toc-link" href="#%E5%9C%A8%E6%96%87%E7%AB%A0%E4%B8%AD%E5%A2%9E%E5%8A%A0%E5%B0%8F%E5%9B%BE%E6%A0%87"><span class="toc-text">在文章中增加小图标</span></a></li></ol></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%B7%BB%E5%8A%A0%E6%9C%AC%E5%9C%B0%E6%90%9C%E7%B4%A2"><span class="toc-text">添加本地搜索</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E5%8F%B3%E4%B8%8A%E8%A7%92%E5%A2%9E%E5%8A%A0-fork-me-on-GitHub-%E5%9B%BE%E6%A0%87"><span class="toc-text">右上角增加 fork me on GitHub 图标</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%89%93%E5%BC%80%E4%BB%A3%E7%A0%81%E5%9D%97%E7%9A%84%E5%A4%8D%E5%88%B6%E5%8A%9F%E8%83%BD"><span class="toc-text">打开代码块的复制功能</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%A1%B5%E9%9D%A2%E5%8A%A0%E8%BD%BD%E8%BF%9B%E5%BA%A6%E6%95%88%E6%9E%9C"><span class="toc-text">页面加载进度效果</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E9%98%85%E8%AF%BB%E8%BF%9B%E5%BA%A6%E6%9D%A1"><span class="toc-text">阅读进度条</span></a></li><li class="toc-item toc-level-3"><a class="toc-link" href="#%E6%96%87%E7%AB%A0%E5%88%9B%E5%BB%BA%E6%97%B6%E9%97%B4%E5%92%8C%E6%9B%B4%E6%96%B0%E6%97%B6%E9%97%B4"><span class="toc-text">文章创建时间和更新时间</span></a></li></ol></li><li class="toc-item toc-level-2"><a class="toc-link" href="#%E7%BB%93%E8%AF%AD"><span class="toc-text">结语</span></a></li></ol></div></div><div class="card-widget card-recent-post"><div class="item-headline"><i class="fas fa-history"></i><span>最新文章</span></div><div class="aside-list"><div class="aside-list-item"><a class="thumbnail" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210615211906.jpg" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="mac OS 配置前端开发环境"/></a><div class="content"><a class="title" href="/archives/91fd1eed.html" title="mac OS 配置前端开发环境">mac OS 配置前端开发环境</a><time datetime="2023-06-15T19:02:11.000Z" title="发表于 2023-06-15 19:02:11">2023-06-15</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/202202091905708.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="使用GitHub Actions 实现自动化部署和部署到服务器"/></a><div class="content"><a class="title" href="/archives/992bac21.html" title="使用GitHub Actions 实现自动化部署和部署到服务器">使用GitHub Actions 实现自动化部署和部署到服务器</a><time datetime="2022-02-10T13:02:13.000Z" title="发表于 2022-02-10 13:02:13">2022-02-10</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20210720104313.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="img标签访问图片返回403 forbidden的解决方法"/></a><div class="content"><a class="title" href="/archives/9ac5350.html" title="img标签访问图片返回403 forbidden的解决方法">img标签访问图片返回403 forbidden的解决方法</a><time datetime="2021-07-20T10:36:33.000Z" title="发表于 2021-07-20 10:36:33">2021-07-20</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201113115021.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化"/></a><div class="content"><a class="title" href="/archives/558885cd.html" title="Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化">Vue基于html2canvas实现HTML页面生成为图片并下载的功能及清晰度优化</a><time datetime="2020-11-13T11:36:33.000Z" title="发表于 2020-11-13 11:36:33">2020-11-13</time></div></div><div class="aside-list-item"><a class="thumbnail" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性"><img src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20201112094634.png" onerror="this.onerror=null;this.src='/img/404.jpg'" alt="那些你总是要用却又死活记不住的css属性"/></a><div class="content"><a class="title" href="/archives/59df034f.html" title="那些你总是要用却又死活记不住的css属性">那些你总是要用却又死活记不住的css属性</a><time datetime="2020-11-12T09:36:33.000Z" title="发表于 2020-11-12 09:36:33">2020-11-12</time></div></div></div></div></div></div></main><footer id="footer"><div id="footer-wrap"><div class="copyright">&copy;2019 - 2023 By ConstOwn</div><div class="footer_custom_text"><span style="color:#999" onclick="window.open('https://beian.miit.gov.cn/#/Integrated/index/')"><a class="footer-a"> 渝ICP备19004608号</a></span> <br /> <span style="color:#999" onclick="window.open('http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=50023502000331')"><a class="footer-a"><img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="https://cdn.jsdelivr.net/gh/constown/HexoStaticFile/img/20200831161110.png">渝公网安备50023502000331号</a></span> <br /> <a class="footer-a" onclick="window.open('https:\/\/www.upyun.com/?utm_source=lianmeng&utm_medium=referral')">本网站由 <img class="upy" align="absmiddle" src= "data:image/gif;base64,R0lGODdhAQABAPAAAMPDwwAAACwAAAAAAQABAAACAkQBADs=" data-lazy-src="/images/upy.png"> 提供CDN加速/云储存服务</a></div></div></footer></div><div id="rightside"><div id="rightside-config-hide"><button id="hide-aside-btn" type="button" title="单栏和双栏切换"><i class="fas fa-arrows-alt-h"></i></button></div><div id="rightside-config-show"><button class="close" id="mobile-toc-button" type="button" title="目录"><i class="fas fa-list-ul"></i></button><button id="go-up" type="button" title="回到顶部"><i class="fas fa-arrow-up"></i></button></div></div><div id="local-search"><div class="search-dialog"><div class="search-dialog__title" id="local-search-title">本地搜索</div><div id="local-input-panel"><div id="local-search-input"><div class="local-search-box"><input class="local-search-box--input" placeholder="搜索文章" type="text"/></div></div></div><hr/><div id="local-search-results"></div><span class="search-close-button"><i class="fas fa-times"></i></span></div><div id="search-mask"></div></div><div><script src="/js/utils.js"></script><script src="/js/main.js"></script><script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload/dist/lazyload.iife.min.js"></script><script>function panguFn () {
  if (typeof pangu === 'object') pangu.autoSpacingPage()
  else {
    getScript('https://cdn.jsdelivr.net/npm/pangu/dist/browser/pangu.min.js')
      .then(() => {
        pangu.autoSpacingPage()
      })
  }
}

function panguInit () {
  if (false){
    GLOBAL_CONFIG_SITE.isPost && panguFn()
  } else {
    panguFn()
  }
}

document.addEventListener('DOMContentLoaded', panguInit)</script><script src="/js/search/local-search.js"></script><div class="js-pjax"></div><script async data-pjax src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script></div></body></html>